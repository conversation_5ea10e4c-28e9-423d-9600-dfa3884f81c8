{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "storage": {"rules": "storage.rules"}, "flutter": {"platforms": {"android": {"default": {"projectId": "pu-laundry", "appId": "1:1036649922444:android:b0889b9e38fae8cc3720a9", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "pu-laundry", "appId": "1:1036649922444:ios:b697eadb67b546043720a9", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "pu-laundry", "configurations": {"android": "1:1036649922444:android:b0889b9e38fae8cc3720a9", "ios": "1:1036649922444:ios:b697eadb67b546043720a9"}}}}}}
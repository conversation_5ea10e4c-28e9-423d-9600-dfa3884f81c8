{"buildFiles": ["/Users/<USER>/Documents/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/MetaDC/Laundry/laundry_resident/android/app/.cxx/Debug/l353yrr0/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/MetaDC/Laundry/laundry_resident/android/app/.cxx/Debug/l353yrr0/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
// ignore_for_file: no_wildcard_variable_uses

import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/shared/router.dart';
import 'package:laundry_resident/views/Profile/profilepage.dart';
import 'package:laundry_resident/views/others_pages.dart';
import 'views/common/block_page.dart';

class Wrapper extends StatefulWidget {
  const Wrapper({super.key, required this.child, this.appbar});
  final Widget child;
  final PreferredSizeWidget? appbar;

  @override
  State<Wrapper> createState() => _WrapperState();
}

class _WrapperState extends State<Wrapper> {
  StreamSubscription<List<ConnectivityResult>>? connectionStream;
  bool connectionExist = true;
  @override
  void initState() {
    super.initState();
    checkConnectivity();
  }

  checkConnectivity() {
    connectionStream?.cancel();
    connectionStream = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> result) {
      // Received changes in available connectivity types!
      if (result.contains(ConnectivityResult.mobile)) {
        connectionExist = true;
        setState(() {});
      } else if (result.contains(ConnectivityResult.wifi)) {
        connectionExist = true;
        setState(() {});
      } else if (result.contains(ConnectivityResult.ethernet)) {
        connectionExist = true;
        setState(() {});
      } else {
        connectionExist = false;
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: GetBuilder<HomeCtrl>(
        builder: (_) {
          return connectionExist
              ? _.versionSupported
                  ? !_.loaded || !_.deviceLoaded
                      ? const Center(
                          child: CircularProgressIndicator(strokeWidth: 3.5))
                      : _.residentModel != null
                          ? _.sameDevice
                              ? _.residentModel!.blocked ||
                                      _.residentModel!.deleted
                                  ? _.residentModel!.blocked
                                      ? const BlockPage()
                                      : EditProfilePage(
                                          resident: _.residentModel)
                                  : Row(children: [
                                      Expanded(child: widget.child),
                                    ])
                              : const DeviceCheckPage(isNewUser: true)
                          : EditProfilePage(emptyProfile: true)
                  // : const ProfilePage(emptyProfile: true)
                  : const UpdateVersionScreen()
              : const ConnectivityPage();
        },
      ),
    );
  }
}

class Combo extends StatelessWidget {
  const Combo({
    super.key,
    required this.txt,
    required this.data,
    required this.onPressed,
    required this.index,
  });
  final Function() onPressed;
  final String txt;
  final IconData data;
  final int index;

  @override
  Widget build(BuildContext context) {
    // final size = MediaQuery.sizeOf(context);
    return Expanded(
      child: GetBuilder<HomeCtrl>(builder: (_) {
        final selected = _.selectedIndex == index;
        return InkWell(
            onTap: onPressed,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  data,
                  color: selected
                      ? const Color(0xff01B49E)
                      : const Color(0xff4F4F4F),
                  size: selected ? 25 : null,
                ),
                const SizedBox(height: 4),
                Text(
                  txt,
                  style: TextStyle(
                    color: selected
                        ? const Color(0xff01B49E)
                        : const Color(0xff4F4F4F),
                    fontWeight: selected ? FontWeight.w600 : null,
                  ),
                )
              ],
            ));
      }),
    );
  }
}

class Wrapper2 extends StatelessWidget {
  const Wrapper2({super.key, required this.child});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (_) {
        return Scaffold(
          body: Padding(
            padding: EdgeInsets.only(top: MediaQuery.paddingOf(context).top),
            child: _.residentModel != null
                ? _.residentModel!.blocked || _.residentModel!.deleted
                    ? _.residentModel!.blocked
                        ? const BlockPage()
                        : EditProfilePage(resident: _.residentModel)
                    : Row(children: [Expanded(child: child)])
                : const EditProfilePage(emptyProfile: true),
          ),
          bottomNavigationBar: _.residentModel != null &&
                  !(_.residentModel!.blocked || _.residentModel!.deleted)
              ? BottomAppBar(
                  padding: EdgeInsets.zero,
                  color: const Color(0xffF2F2F2),
                  surfaceTintColor: const Color(0xffF2F2F2),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Combo(
                        index: 0,
                        data: CupertinoIcons.house,
                        txt: 'Home',
                        onPressed: () {
                          _.selectedIndex = 0;
                          _.update();
                          context.go(homeRoute);
                        },
                      ),
                      Combo(
                        data: CupertinoIcons.shopping_cart,
                        txt: 'Clothes',
                        onPressed: () {
                          _.selectedIndex = 1;
                          _.update();
                          context.go(Routes.clothes);
                        },
                        index: 1,
                      ),
                      Combo(
                        data: Icons.contact_support_outlined,
                        txt: 'Help',
                        onPressed: () {
                          _.selectedIndex = 2;
                          _.update();
                          context.go(Routes.help);
                        },
                        index: 2,
                      ),
                      Combo(
                        data: Icons.account_circle_outlined,
                        txt: 'Account',
                        onPressed: () {
                          _.selectedIndex = 3;
                          _.update();
                          context.go(Routes.account);
                        },
                        index: 3,
                      ),
                    ],
                  ),
                )
              : null,
        );
      },
    );
  }

  /* Widget _botAppBar(BuildContext context) {
    return BottomAppBar(
      color: const Color(0xFFF2F2F2),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: size.width > 339 ? 20.0 : 10),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Combo(
              index: 0,
              data: CupertinoIcons.house,
              txt: 'Home',
              onPressed: () {
                context.go(homeRoute);
              },
            ),
            Combo(
              data: CupertinoIcons.shopping_cart,
              txt: 'Clothes',
              onPressed: () {
                context.go(Routes.clothes);
              },
              index: 1,
            ),
            Combo(
              data: Icons.contact_support_outlined,
              txt: 'Help',
              onPressed: () {
                context.go(Routes.help);
              },
              index: 2,
            ),
            Combo(
              data: Icons.account_circle_outlined,
              txt: 'Account',
              onPressed: () {
                context.go(Routes.account);
              },
              index: 3,
            ),
          ],
        ),
      ),
    );
  }
 */
}

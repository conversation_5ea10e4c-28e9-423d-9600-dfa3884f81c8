import 'package:cloud_firestore/cloud_firestore.dart';

class PickUpScheduleModel {
  final String docId;
  final String uId;
  final String hostelDocId;
  final String wing;
  final int floor;
  final int roomNo;
  final int bedNo;
  final String residentName;
  final String? bunchDocId;
  final Timestamp? lastPickupScheduleDate;
  final int totalCloths;
  final int totalValue;
  final int? paidForCloth;
  final int? paidForValue;
  final num? paidAmount;
  final Timestamp? paymentTime;
  final Timestamp? lastUpdate;
  final num perValueAmount;
  final int? missingCount;
  final int? missingValue;
  final String? complaintDocId;
  final bool? complaintSolved;
  final Timestamp createdAt;
  final int? pickedUpCount;
  final int? pickedUpValue;
  final bool pickedUp;
  final bool delivered;
  final bool isPaid;
  final Timestamp? pickedAt;
  final Timestamp? deliveryTime;
  final bool needApproval;
  final String? collectedByName;
  final String? collectedByDocId;
  final String? pickedUpByDocId;
  final String? pickedUpByName;
  final bool enableApprove;
  final String? deliveredByDocId;
  final String? deliveredByName;
  final String? talliedByName;
  final String? talliedByDocId;
  final Timestamp? talliedAt;
  final List<ClothPickupModel> clothes;
  final List<FoundClothPickupModel>? foundClothes;

  PickUpScheduleModel({
    required this.docId,
    required this.uId,
    required this.hostelDocId,
    required this.wing,
    required this.floor,
    required this.roomNo,
    required this.bedNo,
    required this.lastPickupScheduleDate,
    required this.residentName,
    required this.bunchDocId,
    required this.totalCloths,
    required this.totalValue,
    required this.paidForCloth,
    required this.paidForValue,
    required this.paidAmount,
    required this.paymentTime,
    required this.lastUpdate,
    required this.perValueAmount,
    required this.missingCount,
    required this.missingValue,
    required this.complaintDocId,
    required this.complaintSolved,
    required this.createdAt,
    required this.pickedUpCount,
    required this.pickedUpValue,
    required this.pickedUp,
    required this.delivered,
    required this.isPaid,
    required this.pickedAt,
    required this.deliveryTime,
    required this.needApproval,
    required this.collectedByName,
    required this.collectedByDocId,
    required this.pickedUpByDocId,
    required this.pickedUpByName,
    required this.enableApprove,
    required this.deliveredByDocId,
    required this.deliveredByName,
    required this.talliedByName,
    required this.talliedByDocId,
    required this.talliedAt,
    required this.clothes,
    required this.foundClothes,
  });

  factory PickUpScheduleModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> json) {
    return PickUpScheduleModel(
      docId: json.id,
      uId: json['uId'],
      hostelDocId: json['hostelDocId'],
      wing: json['wing'],
      floor: json['floor'],
      roomNo: json['roomNo'],
      bedNo: json['bedNo'],
      residentName: json['residentName'],
      bunchDocId: json['bunchDocId'],
      totalCloths: json['totalCloths'],
      totalValue: json['totalValue'],
      paidForCloth: json['paidForCloth'],
      paidForValue: json['paidForValue'],
      paidAmount: json['paidAmount'],
      paymentTime: json['paymentTime'],
      lastUpdate: json['lastUpdate'],
      lastPickupScheduleDate: json['lastPickupScheduleDate'],
      perValueAmount: json['perValueAmount'],
      missingCount: json['missingCount'],
      missingValue: json['missingValue'],
      complaintDocId: json['complaintDocId'],
      complaintSolved: json['complaintSolved'],
      createdAt: json['createdAt'],
      pickedUpCount: json['pickedUpCount'],
      pickedUpValue: json['pickedUpValue'],
      pickedUp: json['pickedUp'],
      delivered: json['delivered'],
      isPaid: json['isPaid'],
      pickedAt: json['pickedAt'],
      deliveryTime: json['deliveryTime'],
      needApproval: json['needApproval'],
      collectedByName: json['collectedByName'],
      collectedByDocId: json['collectedByDocId'],
      pickedUpByDocId: json['pickedUpByDocId'],
      pickedUpByName: json['pickedUpByName'],
      enableApprove: json['enableApprove'],
      deliveredByDocId: json['deliveredByDocId'],
      deliveredByName: json['deliveredByName'],
      talliedByName: json['talliedByName'],
      talliedByDocId: json['talliedByDocId'],
      talliedAt: json['talliedAt'],
      clothes: List<ClothPickupModel>.from(
          json['clothes'].map((item) => ClothPickupModel.fromJson(item))),
      foundClothes: json.data()?['foundClothes'] != null
          ? List<FoundClothPickupModel>.from(json['foundClothes']
              .map((item) => FoundClothPickupModel.fromJson(item)))
          : null,
    );
  }

  factory PickUpScheduleModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    // print(json.data());
    // print(json.id);
    return PickUpScheduleModel(
      docId: json.id,
      uId: json['uId'],
      hostelDocId: json['hostelDocId'],
      wing: json['wing'],
      floor: json['floor'],
      roomNo: json['roomNo'],
      bedNo: json['bedNo'],
      residentName: json['residentName'],
      bunchDocId: json['bunchDocId'],
      totalCloths: json['totalCloths'],
      totalValue: json['totalValue'],
      paidForCloth: json['paidForCloth'],
      paidForValue: json['paidForValue'],
      paidAmount: json['paidAmount'],
      lastPickupScheduleDate: json.data()['lastPickupScheduleDate'],
      paymentTime: json['paymentTime'],
      lastUpdate: json['lastUpdate'],
      perValueAmount: json['perValueAmount'],
      missingCount: json['missingCount'],
      missingValue: json['missingValue'],
      complaintDocId: json['complaintDocId'],
      complaintSolved: json['complaintSolved'],
      createdAt: json['createdAt'],
      pickedUpCount: json['pickedUpCount'],
      pickedUpValue: json['pickedUpValue'],
      pickedUp: json['pickedUp'],
      delivered: json['delivered'],
      isPaid: json['isPaid'],
      pickedAt: json['pickedAt'],
      deliveryTime: json['deliveryTime'],
      needApproval: json['needApproval'],
      collectedByName: json['collectedByName'],
      collectedByDocId: json['collectedByDocId'],
      pickedUpByDocId: json['pickedUpByDocId'],
      pickedUpByName: json['pickedUpByName'],
      enableApprove: json['enableApprove'],
      deliveredByDocId: json['deliveredByDocId'],
      deliveredByName: json['deliveredByName'],
      talliedByName: json['talliedByName'],
      talliedByDocId: json['talliedByDocId'],
      talliedAt: json['talliedAt'],
      clothes: List<ClothPickupModel>.from(
          json['clothes'].map((item) => ClothPickupModel.fromJson(item))),
      foundClothes: json.data()['foundClothes'] != null
          ? List<FoundClothPickupModel>.from(json['foundClothes']
              .map((item) => FoundClothPickupModel.fromJson(item)))
          : null,
    );
  }

  factory PickUpScheduleModel.fromJson(Map<String, dynamic> json) {
    return PickUpScheduleModel(
      docId: json['docId'],
      uId: json['uId'],
      hostelDocId: json['hostelDocId'],
      wing: json['wing'],
      floor: json['floor'],
      roomNo: json['roomNo'],
      bedNo: json['bedNo'],
      lastPickupScheduleDate: json['lastPickupScheduleDate'],
      residentName: json['residentName'],
      bunchDocId: json['bunchDocId'],
      totalCloths: json['totalCloths'],
      totalValue: json['totalValue'],
      paidForCloth: json['paidForCloth'],
      paidForValue: json['paidForValue'],
      paidAmount: json['paidAmount'],
      paymentTime: json['paymentTime'],
      lastUpdate: json['lastUpdate'],
      perValueAmount: json['perValueAmount'],
      missingCount: json['missingCount'],
      missingValue: json['missingValue'],
      complaintDocId: json['complaintDocId'],
      complaintSolved: json['complaintSolved'],
      createdAt: json['createdAt'],
      pickedUpCount: json['pickedUpCount'],
      pickedUpValue: json['pickedUpValue'],
      pickedUp: json['pickedUp'],
      delivered: json['delivered'],
      isPaid: json['isPaid'],
      pickedAt: json['pickedAt'],
      deliveryTime: json['deliveryTime'],
      needApproval: json['needApproval'],
      collectedByName: json['collectedByName'],
      collectedByDocId: json['collectedByDocId'],
      pickedUpByDocId: json['pickedUpByDocId'],
      pickedUpByName: json['pickedUpByName'],
      enableApprove: json['enableApprove'],
      deliveredByDocId: json['deliveredByDocId'],
      deliveredByName: json['deliveredByName'],
      talliedByName: json['talliedByName'],
      talliedByDocId: json['talliedByDocId'],
      talliedAt: json['talliedAt'],
      clothes: List<ClothPickupModel>.from(
          json['clothes'].map((item) => ClothPickupModel.fromJson(item))),
      foundClothes: List<FoundClothPickupModel>.from(
          json['clothes'].map((item) => FoundClothPickupModel.fromJson(item))),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'uId': uId,
      'hostelDocId': hostelDocId,
      'wing': wing,
      'floor': floor,
      'roomNo': roomNo,
      'bedNo': bedNo,
      'residentName': residentName,
      'bunchDocId': bunchDocId,
      'totalCloths': totalCloths,
      'totalValue': totalValue,
      'paidForCloth': paidForCloth,
      'paidForValue': paidForValue,
      'paidAmount': paidAmount,
      'paymentTime': paymentTime,
      'lastUpdate': lastUpdate,
      'perValueAmount': perValueAmount,
      'missingCount': missingCount,
      'missingValue': missingValue,
      'complaintDocId': complaintDocId,
      'complaintSolved': complaintSolved,
      'createdAt': createdAt,
      'pickedUpCount': pickedUpCount,
      'pickedUpValue': pickedUpValue,
      'pickedUp': pickedUp,
      'delivered': delivered,
      'isPaid': isPaid,
      'pickedAt': pickedAt,
      'deliveryTime': deliveryTime,
      'needApproval': needApproval,
      'collectedByName': collectedByName,
      'collectedByDocId': collectedByDocId,
      'pickedUpByDocId': pickedUpByDocId,
      'pickedUpByName': pickedUpByName,
      'enableApprove': enableApprove,
      'deliveredByDocId': deliveredByDocId,
      'deliveredByName': deliveredByName,
      'talliedByName': talliedByName,
      'talliedByDocId': talliedByDocId,
      'talliedAt': talliedAt,
      'clothes': List<dynamic>.from(clothes.map((item) => item.toJson())),
      'foundClothes':
          List<dynamic>.from(foundClothes!.map((item) => item.toJson())),
    };
  }
}

class ClothPickupModel {
  final String docId;
  final String photoUrl;
  final int categoryValue;
  final bool? tallied;
  final bool pickedUp;
  final bool? collected;
  final String? categoryName;

  ClothPickupModel({
    required this.docId,
    required this.photoUrl,
    required this.categoryValue,
    required this.categoryName,
    required this.tallied,
    required this.pickedUp,
    required this.collected,
  });

  factory ClothPickupModel.fromJson(Map<String, dynamic> json) {
    return ClothPickupModel(
      docId: json['docId'],
      photoUrl: json['photoUrl'],
      categoryValue: json['categoryValue'],
      categoryName: json['categoryName'],
      tallied: json['tallied'],
      pickedUp: json['pickedUp'],
      collected: json['collected'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'photoUrl': photoUrl,
      'categoryValue': categoryValue,
      'tallied': tallied,
      'pickedUp': pickedUp,
      'collected': collected,
      'categoryName': categoryName,
    };
  }

  Map<String, dynamic> toJson3(bool? collectedd) {
    return {
      'docId': docId,
      'photoUrl': photoUrl,
      'categoryValue': categoryValue,
      'tallied': collectedd != null && collectedd ? true : tallied,
      'pickedUp': pickedUp,
      'collected': collectedd,
      'categoryName': categoryName,
    };
  }
}

class FoundClothPickupModel {
  final String docId;
  final String photoUrl;
  final int categoryValue;
  final bool? tallied;
  bool pickedUp;
  bool found;
  final bool? collected;
  final String? categoryName;

  FoundClothPickupModel({
    required this.docId,
    required this.photoUrl,
    required this.categoryValue,
    required this.categoryName,
    required this.tallied,
    required this.found,
    required this.pickedUp,
    required this.collected,
  });

  factory FoundClothPickupModel.fromJson(Map<String, dynamic> json) {
    return FoundClothPickupModel(
      docId: json['docId'],
      photoUrl: json['photoUrl'],
      categoryValue: json['categoryValue'],
      categoryName: json['categoryName'],
      tallied: json['tallied'],
      found: json['found'],
      pickedUp: json['pickedUp'],
      collected: json['collected'],
    );
  }

  Map<String, dynamic> toJsonFound(bool found) {
    return {
      'docId': docId,
      'photoUrl': photoUrl,
      'categoryValue': categoryValue,
      'tallied': tallied,
      'found': found,
      'pickedUp': pickedUp,
      'collected': collected,
      'categoryName': categoryName,
    };
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'photoUrl': photoUrl,
      'categoryValue': categoryValue,
      'tallied': tallied,
      'found': found,
      'pickedUp': pickedUp,
      'collected': collected,
      'categoryName': categoryName,
    };
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

class ManagersInputModel {
  late TextEditingController nameController;
  late TextEditingController docIdController;
  late List<String> hostelDocIds;
  late bool blocked;
  late TextEditingController lastBunchDocIdController;
  late TextEditingController phoneNumberController;

  ManagersInputModel({
    required this.nameController,
    required this.docIdController,
    required this.hostelDocIds,
    required this.blocked,
    required this.lastBunchDocIdController,
    required this.phoneNumberController,
  });

  ManagerModel toManagersModel() {
    return ManagerModel(
      docId: docIdController.text,
      name: nameController.text,
      hostelDocIds: List<String>.from(hostelDocIds),
      blocked: blocked,
      lastBunchDocId: lastBunchDocIdController.text,
      phoneNumber: phoneNumberController.text,
    );
  }
}

class ManagerModel {
  final String docId;
  final String name;
  final List<String> hostelDocIds;
  final bool blocked;
  final String lastBunchDocId;
  final String phoneNumber;

  ManagerModel({
    required this.docId,
    required this.name,
    required this.hostelDocIds,
    required this.blocked,
    required this.lastBunchDocId,
    required this.phoneNumber,
  });

  factory ManagerModel.fromJson(Map<String, dynamic> json) {
    return ManagerModel(
      docId: json['docId'],
      name: json['name'],
      hostelDocIds: List<String>.from(json['hostelDocIds']),
      blocked: json['blocked'],
      lastBunchDocId: json['lastBunchDocId'],
      phoneNumber: json['phoneNumber'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'hostelDocIds': hostelDocIds,
      'blocked': blocked,
      'lastBunchDocId': lastBunchDocId,
      'phoneNumber': phoneNumber,
    };
  }

  factory ManagerModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return ManagerModel(
      docId: json.id,
      name: json['name'],
      hostelDocIds: List.castFrom<dynamic, String>(json['hostelDocIds']),
      blocked: json['blocked'],
      lastBunchDocId: json['lastBunchDocId'],
      phoneNumber: json['phoneNumber'],
    );
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';

class ReviewModel {
  final String docId;
  final String name;
  final String hostelDocId;
  final String hostelId;
  final int floor;
  final int roomNo;
  final int bedNo;
  final bool profileUnderReview;
  final String wing;
  final String? imageUrl;
  final String uId;
  final String email;
  final Timestamp createdAt;
  final String status;
  final String? rejectionReason;
  final bool blocked;
  final String? mobile;
  final Timestamp? acpRejDate;

  ReviewModel({
    required this.docId,
    required this.createdAt,
    required this.name,
    required this.uId,
    required this.blocked,
    required this.rejectionReason,
    required this.status,
    required this.imageUrl,
    required this.email,
    required this.hostelDocId,
    required this.hostelId,
    required this.floor,
    required this.roomNo,
    this.mobile,
    required this.bedNo,
    required this.profileUnderReview,
    required this.wing,
    required this.acpRejDate,
  });

  factory ReviewModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> snap) {
    return ReviewModel(
      docId: snap.id,
      name: snap['name'],
      uId: snap['uId'] ?? "",
      blocked: snap['blocked'],
      rejectionReason: snap['rejectionReason'],
      status: snap['status'],
      email: snap.data()['email'],
      createdAt: snap.data()['createdAt'],
      hostelDocId: snap['hostelDocId'],
      hostelId: snap['hostelId'],
      wing: snap['wing'],
      floor: snap['floor'],
      mobile: snap['mobile'],
      roomNo: snap['roomNo'],
      bedNo: snap['bedNo'],
      profileUnderReview: snap['profileUnderReview'],
      imageUrl: snap['imageUrl'],
      acpRejDate: snap.data()['acpRejDate'] ?? Timestamp.now(),
    );
  }
  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      docId: json['docId'],
      uId: json['uId'],
      rejectionReason: json['rejectionReason'],
      status: json['status'],
      name: json['name'],
      createdAt: json['createdAt'],
      hostelDocId: json['hostelDocId'],
      hostelId: json['hostelId'],
      wing: json['wing'],
      email: json['email'],
      floor: json['floor'],
      roomNo: json['roomNo'],
      mobile: json['mobile'],
      bedNo: json['bedNo'],
      profileUnderReview: json['profileUnderReview'],
      imageUrl: json['imageUrl'],
      blocked: json['blocked'],
      acpRejDate: json['acpRejDate'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'hostelDocId': hostelDocId,
      'hostelId': hostelId,
      'wing': wing,
      'floor': floor,
      'roomNo': roomNo,
      'mobile': mobile,
      'email': email,
      'bedNo': bedNo,
      'blocked': blocked,
      'profileUnderReview': profileUnderReview,
      'imageUrl': imageUrl,
      'uId': uId,
      'crecreatedAt': createdAt,
      'rejectionReason': rejectionReason,
      'acpRejDate': acpRejDate,
      'status': status,
    };
  }
}

import 'package:flutter/material.dart';

class RoomTypesInputModel {
  late TextEditingController docIdController;
  late TextEditingController titleController;
  late TextEditingController bedCountController;

  RoomTypesInputModel({
    required this.docIdController,
    required this.titleController,
    required this.bedCountController,
  });

  RoomTypes toRoomTypesModel() {
    return RoomTypes(
      docId: docIdController.text,
      title: titleController.text,
      bedCount: int.parse(bedCountController.text),
    );
  }
}

class RoomTypes {
  final String docId;
  final String title;
  final int bedCount;

  RoomTypes({
    required this.docId,
    required this.title,
    required this.bedCount,
  });

  factory RoomTypes.fromJson(Map<String, dynamic> json) {
    return RoomTypes(
      docId: json['docId'],
      title: json['title'],
      bedCount: json['bedCount'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'title': title,
      'bedCount': bedCount,
    };
  }
}

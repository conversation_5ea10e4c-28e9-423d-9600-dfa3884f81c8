import 'package:cloud_firestore/cloud_firestore.dart';

class SchedulesModel {
  final String docId;
  final String hostelDocId;
  final bool weekly;
  final int? day;
  final DateTime? date;
  final bool pickUp;
  final List<String> staff;
  final num? maxClothValue;

  SchedulesModel({
    this.maxClothValue,
    required this.docId,
    required this.hostelDocId,
    required this.weekly,
    required this.day,
    required this.date,
    required this.pickUp,
    required this.staff,
  });

  factory SchedulesModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> snap) {
    return SchedulesModel(
      docId: snap.id,
      hostelDocId: snap['hostelDocId'],
      maxClothValue: snap['maxClothValue'],
      weekly: snap['weekly'],
      day: snap['day'],
      date: snap['date'].toDate(),
      pickUp: snap['pickUp'],
      staff: List<String>.from(snap['staff']),
    );
  }
  factory SchedulesModel.fromJson(String dId, Map<String, dynamic> json) {
    return SchedulesModel(
      docId: dId,
      hostelDocId: json['hostelDocId'],
      weekly: json['weekly'],
      maxClothValue: json['maxClothValue'],
      day: json['day'],
      date: json['date'],
      pickUp: json['pickUp'],
      staff: List<String>.from(json['staff']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'hostelDocId': hostelDocId,
      'weekly': weekly,
      'day': day,
      'date': date,
      'pickUp': pickUp,
      'staff': staff,
      'maxClothValue': maxClothValue,
    };
  }
}

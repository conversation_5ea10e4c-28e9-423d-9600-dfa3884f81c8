import 'package:cloud_firestore/cloud_firestore.dart';

class ActivitiesModel {
  final String docId;
  final Timestamp createdAt;
  final String? uId;
  final String? desc;
  final String? title;
  final String? type;
  final String? typeDocId;
  final String? userType;

  ActivitiesModel({
    required this.type,
    required this.typeDocId,
    required this.userType,
    required this.title,
    required this.docId,
    required this.createdAt,
    required this.uId,
    required this.desc,
  });

  factory ActivitiesModel.fromJson(Map<String, dynamic> json) {
    return ActivitiesModel(
      docId: json['docId'],
      createdAt: json['createdAt'],
      type: json['type'],
      title: json['title'],
      typeDocId: json['typeDocId'],
      userType: json['userType'],
      uId: json['uId'],
      desc: json['desc'],
    );
  }
  factory ActivitiesModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    // print(json.data());
    return ActivitiesModel(
      docId: json.id,
      createdAt: json['createdAt'],
      type: json['type'],
      title: json['title'],
      typeDocId: json['typeDocId'],
      userType: json['userType'],
      uId: json['uId'],
      desc: json['desc'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'createdAt': createdAt,
      'uId': uId,
      'desc': desc,
      'title': title,
      'type': type,
      'typeDocId': typeDocId,
      'userType': userType,
    };
  }
}

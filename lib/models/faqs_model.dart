class FaqsModel {
  final String docId;
  final String question;
  final String answer;

  FaqsModel({
    required this.docId,
    required this.question,
    required this.answer,
  });

  factory FaqsModel.fromJson(Map<String, dynamic> json) {
    return FaqsModel(
      docId: json['docId'],
      question: json['question'],
      answer: json['answer'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'question': question,
      'answer': answer,
    };
  }
}

class FaqsInputModel {
  final String docId;
  final String question;
  final String answer;

  FaqsInputModel({
    required this.docId,
    required this.question,
    required this.answer,
  });

  factory FaqsInputModel.fromJson(Map<String, dynamic> json) {
    return FaqsInputModel(
      docId: json['docId'],
      question: json['question'],
      answer: json['answer'],
    );
  }
}

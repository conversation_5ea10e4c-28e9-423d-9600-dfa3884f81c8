import 'package:cloud_firestore/cloud_firestore.dart';

class UserSavedModel {
  final String docId;
  final String name;
  final String hostelDocId;
  final String hostelId;
  final String wing;
  final int floor;
  final int roomNo;
  final int bedNo;
  final Timestamp createdAt;
  final Timestamp validity;
  final String mobile;

  UserSavedModel({
    required this.docId,
    required this.name,
    required this.mobile,
    required this.hostelDocId,
    required this.hostelId,
    required this.wing,
    required this.floor,
    required this.roomNo,
    required this.bedNo,
    required this.createdAt,
    required this.validity,
  });
  factory UserSavedModel.fromMap(Map<String, dynamic> map) {
    return UserSavedModel(
      docId: map['docId'],
      name: map['name'],
      mobile: map['mobile'],
      hostelDocId: map['hostelDocId'],
      hostelId: map['hostelId'],
      wing: map['wing'],
      floor: map['floor'],
      roomNo: map['roomNo'],
      bedNo: map['bedNo'],
      createdAt: map['createdAt'],
      validity: map['validity'],
    );
  }
  Map<String, dynamic> tojson() {
    return {
      'name': name,
      'mobile': mobile,
      'hostelDocId': hostelDocId,
      'hostelId': hostelId,
      'wing': wing,
      'floor': floor,
      'roomNo': roomNo,
      'bedNo': bedNo,
      'createdAt': createdAt,
      'validity': validity,
    };
  }

  factory UserSavedModel.fromSnap(
      DocumentSnapshot<Map<String, dynamic>> snapshot) {
    final data = snapshot.data()!;
    return UserSavedModel(
      docId: snapshot.id,
      name: data['name'],
      mobile: data['mobile'],
      hostelDocId: data['hostelDocId'],
      hostelId: data['hostelId'],
      wing: data['wing'],
      floor: data['floor'],
      roomNo: data['roomNo'],
      bedNo: data['bedNo'],
      createdAt: data['createdAt'],
      validity: data['validity'],
    );
  }
}

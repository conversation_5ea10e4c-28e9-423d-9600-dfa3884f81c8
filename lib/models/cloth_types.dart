import 'package:flutter/material.dart';

class ClothTypeInputModel {
  late TextEditingController docIdController;
  late TextEditingController titleController;
  late TextEditingController valueController;

  ClothTypeInputModel({
    required this.docIdController,
    required this.titleController,
    required this.valueController,
  });

  ClothType toClothTypeModel() {
    return ClothType(
      docId: docIdController.text,
      title: titleController.text,
      value: int.parse(valueController.text),
    );
  }
}

class ClothType {
  final String docId;
  final String title;
  final int value;

  ClothType({
    required this.docId,
    required this.title,
    required this.value,
  });

  factory ClothType.fromJson(Map<String, dynamic> json) {
    return ClothType(
      docId: json['docId'],
      title: json['title'],
      value: json['value'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'title': title,
      'value': value,
    };
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';

class HostelModel {
  late String docId;
  late String name;
  late String code;
  late DateTime? lastPickupDate;
  late DateTime? lastDeliveryDate;
  late List<String> staff;
  late List<WingModel> wings;
  late bool blocked;

  HostelModel(
      {required this.docId,
      required this.name,
      required this.code,
      required this.lastPickupDate,
      required this.lastDeliveryDate,
      required this.staff,
      required this.wings,
      required this.blocked});

  factory HostelModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return HostelModel(
      docId: json.id,
      name: json["name"],
      code: json["code"],
      lastPickupDate: DateTime.tryParse(json["lastPickupDate"] ?? ""),
      lastDeliveryDate: DateTime.tryParse(json["lastDeliveryDate"] ?? ""),
      staff: List.castFrom<dynamic, String>(json["staff"]),
      wings: Map.from(json["wings"]).entries.map((entry) {
        return WingModel.fromJson(entry.key, entry.value);
      }).toList(),
      blocked: json["blocked"],
    );
  }
  factory HostelModel.fromJson(DocumentSnapshot<Map<String, dynamic>> json) {
    return HostelModel(
      docId: json.id,
      name: json["name"],
      code: json["code"],
      lastPickupDate: DateTime.tryParse(json["lastPickupDate"] ?? ""),
      lastDeliveryDate: DateTime.tryParse(json["lastDeliveryDate"] ?? ""),
      staff: List.castFrom<dynamic, String>(json["staff"]),
      wings: Map.from(json["wings"]).entries.map((entry) {
        return WingModel.fromJson(entry.key, entry.value);
      }).toList(),
      blocked: json["blocked"],
    );
  }
}

class WingModel {
  late String name;
  late List<FloorModel> floors;

  WingModel({required this.name, required this.floors});

  factory WingModel.fromJson(String namee, Map<String, dynamic> json) {
    return WingModel(
      name: namee,
      floors: json.entries.map((entry) {
        return FloorModel.fromJson(entry.key, entry.value);
      }).toList(),
    );
  }
}

class FloorModel {
  late String fnumber;
  late List<RoomModel> rooms;

  FloorModel({required this.fnumber, required this.rooms});

  factory FloorModel.fromJson(String floorIdd, Map<String, dynamic> json) {
    return FloorModel(
      fnumber: floorIdd,
      rooms: json.entries.map((entry) {
        return RoomModel.fromJson(entry.key, entry.value);
      }).toList(),
    );
  }
}

class RoomModel {
  late String rnumber;
  late String typeDocId;
  late int bedCount;

  RoomModel({
    required this.rnumber,
    required this.typeDocId,
    required this.bedCount,
  });

  factory RoomModel.fromJson(String roomId, Map<String, dynamic> json) {
    return RoomModel(
      rnumber: roomId,
      typeDocId: json['typeDocId'],
      bedCount: json['bedCount'],
    );
  }
}

/* 
class HostelInputModel {
  late TextEditingController nameController;
  late TextEditingController codeController;
  late DateTime lastPickupDate;
  late DateTime lastDeliveryDate;
  late List<String> staff;
  late List<WingInputModel> wings;

  HostelInputModel({
    required this.nameController,
    required this.codeController,
    required this.lastPickupDate,
    required this.lastDeliveryDate,
    required this.staff,
    required this.wings,
  });

  static HostelInputModel emptyHostel() {
    return HostelInputModel(
        nameController: TextEditingController(),
        codeController: TextEditingController(),
        lastPickupDate: DateTime.now(),
        lastDeliveryDate: DateTime.now(),
        staff: [],
        wings: []);
  }

  HostelModel toHostelModel() {
    return HostelModel(
      name: nameController.text,
      code: codeController.text,
      lastPickupDate: lastPickupDate,
      lastDeliveryDate: lastDeliveryDate,
      staff: List.from(staff),
      wings: wings.map((wing) => wing.toWingModel()).toList(),
      docId: '',
    );
  }
}

class WingInputModel {
  late TextEditingController nameController;
  late List<FloorInputModel> floors;

  WingInputModel({required this.nameController, required this.floors});

  static WingInputModel emptyWing() {
    return WingInputModel(
      nameController: TextEditingController(),
      floors: [],
    );
  }

  WingModel toWingModel() {
    return WingModel(
      name: nameController.text,
      floors: floors.map((floor) => floor.toFloorModel()).toList(),
    );
  }
}

class FloorInputModel {
  late TextEditingController fnumberController;
  late List<RoomInputModel> rooms;

  FloorInputModel({required this.fnumberController, required this.rooms});

  FloorModel toFloorModel() {
    return FloorModel(
      fnumber: fnumberController.text,
      rooms: rooms.map((room) => room.toRoomModel()).toList(),
    );
  }
}

class RoomInputModel {
  late TextEditingController rnumberController;
  late TextEditingController typeNameController;
  late TextEditingController typeDocIdController;
  late TextEditingController countController;

  RoomInputModel({
    required this.rnumberController,
    required this.typeNameController,
    required this.typeDocIdController,
    required this.countController,
  });

  RoomModel toRoomModel() {
    return RoomModel(
      rnumber: rnumberController.text,
      typeName: typeNameController.text,
      typeDocId: typeDocIdController.text,
      count: countController.text,
    );
  }
}*/
import 'package:cloud_firestore/cloud_firestore.dart';

class BunchModel {
  final String docId;
  final bool solo;
  final int clothCount;
  final int clothValue;
  final bool open;
  final String createdByDocId;
  final String createdByName;
  final String? deliveredByDocId;
  final bool delivered;
  final bool mixBunch;
  final String hostelDocId;
  final String? wing;
  final int? floor;
  final List<String>? complaintDocIds;
  final List<String> staff;
  final Timestamp createdAt;
  final Timestamp? closedAt;
  final bool tallied;
  final Timestamp? talliedAt;
  final String? talliedByDocId;

  BunchModel({
    required this.tallied,
    required this.talliedAt,
    required this.talliedByDocId,
    required this.docId,
    required this.solo,
    required this.clothCount,
    required this.clothValue,
    required this.open,
    required this.mixBunch,
    required this.createdByDocId,
    required this.createdByName,
    required this.deliveredByDocId,
    required this.delivered,
    required this.hostelDocId,
    required this.wing,
    required this.floor,
    required this.complaintDocIds,
    required this.staff,
    required this.createdAt,
    required this.closedAt,
  });

  factory BunchModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return BunchModel(
      docId: json.id,
      solo: json['solo'],
      clothCount: json['clothCount'],
      clothValue: json['clothValue'],
      open: json['open'],
      mixBunch: json['mixBunch'],
      createdByDocId: json['createdByDocId'],
      createdByName: json['createdByName'],
      deliveredByDocId: json['deliveredByDocId'],
      tallied: json['tallied'],
      talliedAt: json['talliedAt'],
      talliedByDocId: json['talliedByDocId'],
      delivered: json['delivered'],
      hostelDocId: json['hostelDocId'],
      wing: json['wing'],
      floor: json['floor'],
      complaintDocIds: List<String>.from(json['complaintDocIds']),
      staff: List<String>.from(json['staff']),
      createdAt: json['createdAt'],
      closedAt: json['closedAt'],
    );
  }
  factory BunchModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return BunchModel(
      docId: json.id,
      solo: json['solo'],
      clothCount: json['clothCount'],
      clothValue: json['clothValue'],
      open: json['open'],
      mixBunch: json.data()['mixBunch'] ?? false,
      createdByDocId: json['createdByDocId'],
      tallied: json.data()['tallied'] ?? false,
      talliedByDocId: json.data()['talliedByDocId'],
      talliedAt: json.data()['talliedAt'],
      createdByName: json['createdByName'],
      deliveredByDocId: json['deliveredByDocId'],
      delivered: json['delivered'],
      hostelDocId: json['hostelDocId'],
      wing: json['wing'],
      floor: json['floor'],
      complaintDocIds: List<String>.from(json['complaintDocIds']),
      staff: List<String>.from(json['staff']),
      createdAt: json['createdAt'],
      closedAt: json['closedAt'],
    );
  }

  factory BunchModel.fromJson(String docId, Map<String, dynamic> json) {
    return BunchModel(
      docId: docId,
      solo: json['solo'],
      clothCount: json['clothCount'],
      clothValue: json['clothValue'],
      open: json['open'],
      mixBunch: json['mixBunch'],
      createdByDocId: json['createdByDocId'],
      createdByName: json['createdByName'],
      deliveredByDocId: json['deliveredByDocId'],
      delivered: json['delivered'],
      tallied: json['tallied'],
      talliedByDocId: json['talliedByDocId'],
      talliedAt: json['talliedAt'],
      hostelDocId: json['hostelDocId'],
      wing: json['wing'],
      floor: json['floor'],
      complaintDocIds: List<String>.from(json['complaintDocIds']),
      staff: List<String>.from(json['staff']),
      createdAt: json['createdAt'] != null
          ? Timestamp.fromMillisecondsSinceEpoch(json['createdAt'])
          : Timestamp.now(),
      closedAt: json['closedAt'] != null
          ? Timestamp.fromMillisecondsSinceEpoch(json['closedAt'])
          : Timestamp.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'solo': solo,
      'clothCount': clothCount,
      'clothValue': clothValue,
      'open': open,
      'mixBunch': mixBunch,
      'createdByDocId': createdByDocId,
      'createdByName': createdByName,
      'deliveredByDocId': deliveredByDocId,
      'delivered': delivered,
      'hostelDocId': hostelDocId,
      'wing': wing,
      'floor': floor,
      'complaintDocIds': complaintDocIds,
      'staff': staff,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'closedAt': closedAt?.microsecondsSinceEpoch,
    };
  }
}





// tojson() {
//   return {
//     "docId": "dsGf",
//     "solo": true,
//     "clothCount": 3,
//     "clothValue": 4,
//     "open": false,
//     "createdByDocId": "jkbfh",
//     "createdByName": "jkbfh",
//     "deliveredByDocId": "jkbfh",
//     "delivered": true,
//     "hostelDocId": "jkbfh",
//     "wing": "df",
//     "floor": "df",
//     "complaintDocIds": ["jdabfj", "knsa"],
//     "staff": ["jdabfj", "knsa"],
//     "createdAt": "",
//     "closedAt": "",
//   };
// }

import 'package:cloud_firestore/cloud_firestore.dart';
import 'pickup_schedules.dart';

class PaymentOrderModel {
  final String docId;
  final String uId;
  final String pickUpScheduleDocId;
  final Timestamp createdAt;
  final bool processed;
  final List<ClothPickupModel> clothes;
  final num amount;
  final num chargesPerValue;
  final int paidForCloth;
  final int paidForValue;
  final String? lastPickupScheduleDate;
  final String? hostelDocId;
  // final String? hostelName;

  PaymentOrderModel({
    required this.docId,
    required this.uId,
    required this.pickUpScheduleDocId,
    required this.createdAt,
    required this.processed,
    required this.amount,
    required this.clothes,
    required this.chargesPerValue,
    required this.paidForCloth,
    required this.paidForValue,
    required this.hostelDocId,
    // required this.hostelName,
    this.lastPickupScheduleDate,
  });

  // fromDocumentSnapshot method
  factory PaymentOrderModel.fromDocumentSnapshot(DocumentSnapshot doc) {
    return PaymentOrderModel(
      docId: doc.id,
      uId: doc['uId'],
      pickUpScheduleDocId: doc['pickUpScheduleDocId'],
      createdAt: doc['createdAt'],
      processed: doc['processed'],
      amount: doc['amount'],
      clothes: (doc['clothes'] as List)
          .map((item) => ClothPickupModel.fromJson(item))
          .toList(),
      chargesPerValue: doc['chargesPerValue'],
      paidForCloth: doc['paidForCloth'],
      paidForValue: doc['paidForValue'],
      hostelDocId:
          (doc.data() as Map<String, dynamic>).containsKey('hostelDocId')
              ? doc['hostelDocId']
              : null,
      // hostelName: (doc.data() as Map<String, dynamic>).containsKey('hostelName')
      //     ? doc['hostelName']
      //     : null,
      lastPickupScheduleDate: doc['lastPickupScheduleDate'],
    );
  }

  // fromQueryDocumentSnapshot method
  factory PaymentOrderModel.fromQueryDocumentSnapshot(
      QueryDocumentSnapshot doc) {
    return PaymentOrderModel(
      docId: doc.id,
      uId: doc['uId'],
      pickUpScheduleDocId: doc['pickUpScheduleDocId'],
      createdAt: doc['createdAt'],
      processed: doc['processed'],
      amount: doc['amount'],
      clothes: (doc['clothes'] as List)
          .map((item) => ClothPickupModel.fromJson(item))
          .toList(),
      chargesPerValue: doc['chargesPerValue'],
      paidForCloth: doc['paidForCloth'],
      paidForValue: doc['paidForValue'],
      hostelDocId:
          (doc.data() as Map<String, dynamic>).containsKey('hostelDocId')
              ? doc['hostelDocId']
              : null,
      // hostelName: (doc.data() as Map<String, dynamic>).containsKey('hostelName')
      //     ? doc['hostelName']
      //     : null,
      lastPickupScheduleDate: doc['lastPickupScheduleDate'],
    );
  }

  // toJson method
  Map<String, dynamic> toJson() {
    return {
      'uId': uId,
      'pickUpScheduleDocId': pickUpScheduleDocId,
      'createdAt': createdAt,
      'processed': processed,
      'amount': amount,
      'clothes': clothes.map((cloth) => cloth.toJson()).toList(),
      'chargesPerValue': chargesPerValue,
      'paidForCloth': paidForCloth,
      'paidForValue': paidForValue,
      'lastPickupScheduleDate': lastPickupScheduleDate,
    };
  }

  // fromJson method
  factory PaymentOrderModel.fromJson(Map<String, dynamic> json) {
    return PaymentOrderModel(
      docId: json['docId'],
      uId: json['uId'],
      pickUpScheduleDocId: json['pickUpScheduleDocId'],
      createdAt: json['createdAt'],
      processed: json['processed'],
      amount: json['amount'],
      clothes: (json['clothes'] as List)
          .map((item) => ClothPickupModel.fromJson(item))
          .toList(),
      chargesPerValue: json['chargesPerValue'],
      paidForCloth: json['paidForCloth'],
      paidForValue: json['paidForValue'],
      lastPickupScheduleDate: json['lastPickupScheduleDate'],
      hostelDocId: json.containsKey('hostelDocId') ? json['hostelDocId'] : null,
      // hostelName: json.containsKey('hostelName') ? json['hostelName'] : null,
    );
  }
}

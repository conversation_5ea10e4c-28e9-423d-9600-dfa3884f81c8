import 'package:cloud_firestore/cloud_firestore.dart';

class ResidentModel {
  final String docId;
  final String name;
  final String hostelDocId;
  final String hostelId;
  final String wing;
  final String email;
  final int floor;
  final int roomNo;
  final int bedNo;
  final bool blocked;
  final bool deleted;
  final String? profileImage;
  final Timestamp createdAt;
  final String? deviceId;
  final int balance;
  final String? mobile;
  final List<String> topics;

  ResidentModel({
    required this.docId,
    required this.name,
    required this.hostelDocId,
    required this.hostelId,
    required this.wing,
    required this.floor,
    required this.roomNo,
    required this.bedNo,
    required this.blocked,
    required this.deleted,
    required this.email,
    this.profileImage,
    this.deviceId,
    this.mobile,
    required this.createdAt,
    required this.balance,
    required this.topics,
  });

  factory ResidentModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> snap) {
    return ResidentModel(
      docId: snap.id,
      name: snap['name'],
      hostelDocId: snap['hostelDocId'],
      hostelId: snap['hostelId'],
      wing: snap['wing'],
      email: snap.data()['email'],
      balance: snap.data()['balance'],
      floor: snap['floor'],
      roomNo: snap['roomNo'],
      bedNo: snap['bedNo'],
      deviceId: snap.data()['deviceId'],
      blocked: snap['blocked'],
      deleted: snap.data()['deleted'] ?? false,
      createdAt: snap.data()['createdAt'],
      mobile: snap.data()['mobile'],
      topics: snap.data().containsKey('topics')
          ? List<String>.from(snap['topics'])
          : <String>[],
    );
  }
  factory ResidentModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> snap) {
    return ResidentModel(
      docId: snap.id,
      name: snap['name'],
      hostelDocId: snap['hostelDocId'],
      hostelId: snap['hostelId'],
      wing: snap['wing'],
      email: snap.data()?['email'] ?? "",
      balance: snap.data()?['balance'] ?? 0,
      floor: snap['floor'],
      roomNo: snap['roomNo'],
      bedNo: snap['bedNo'],
      blocked: snap['blocked'],
      deviceId: snap.data()?['deviceId'],
      deleted: snap.data()?['deleted'] ?? false,
      profileImage: snap.data()?['profileImage'],
      mobile: snap.data()?['mobile'],
      createdAt: snap.data()?['createdAt'] ?? Timestamp.now(),
      topics: List<String>.from(snap.data()?['topics'] ?? []),
    );
  }
  factory ResidentModel.fromJson(Map<String, dynamic> json) {
    return ResidentModel(
      docId: json['docId'],
      name: json['name'],
      hostelDocId: json['hostelDocId'],
      hostelId: json['hostelId'],
      wing: json['wing'],
      email: json['email'],
      balance: json['balance'],
      floor: json['floor'],
      roomNo: json['roomNo'],
      bedNo: json['bedNo'],
      blocked: json['blocked'],
      deviceId: json['deviceId'],
      deleted: json['deleted'] ?? false,
      createdAt: json['createdAt'],
      mobile: json['mobile'],
      profileImage: json['profileImage'],
      topics: json['topics'],

      // profileImage: snap.data()?['profileImage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'hostelDocId': hostelDocId,
      'hostelId': hostelId,
      'wing': wing,
      'email': email,
      'floor': floor,
      'balance': balance,
      'roomNo': roomNo,
      'bedNo': bedNo,
      'blocked': blocked,
      'deviceId': deviceId,
      'deleted': deleted,
      'profileImage': profileImage,
      'createdAt': createdAt,
      'mobile': mobile,
      'topics': topics,
    };
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:laundry_resident/models/cloth_types.dart';
import 'package:laundry_resident/models/room_types.dart';

// class SettingsInputModel {
//   late TextEditingController maxClothesController;
//   late TextEditingController maxClothesPerDayController;
//   late TextEditingController chargesPerValueController;

//   SettingsInputModel({
//     required this.maxClothesController,
//     required this.maxClothesPerDayController,
//     required this.chargesPerValueController,
//   });

//   Settings toSettingsModel() {
//     return Settings(
//       maxClothes: int.parse(maxClothesController.text),
//       maxClothesPerDay: int.parse(maxClothesPerDayController.text),
//       chargesPerValue: num.parse(chargesPerValueController.text),
//       // roomTypes: [],

//       roomTypes: json["roomTypes"].entries.map((entry) {
//         return RoomTypes.fromJson(entry.key, entry.value);
//       }).toList(),
//     );
//   }
// }

class SettingsModel {
  late num maxClothes;
  late num maxClothesPerDay;
  late num chargesPerValue;
  late num bunchMinSize;
  late num bunchMaxSize;
  final String chatNumber;
  final String callNumber;
  final String email;
  late List<RoomTypes> roomTypes;
  late List<ClothType> clothtypes;
  late String version;
  late String iosbuildNumber;
  late String andbuildNumber;
  final String? tutorialLink;
  final String? noteLink;
  final String note;

  SettingsModel({
    required this.roomTypes,
    required this.clothtypes,
    required this.maxClothes,
    required this.bunchMinSize,
    required this.bunchMaxSize,
    required this.chatNumber,
    required this.callNumber,
    required this.email,
    required this.maxClothesPerDay,
    required this.chargesPerValue,
    required this.version,
    required this.iosbuildNumber,
    required this.andbuildNumber,
    this.tutorialLink,
    this.noteLink,
    required this.note,
  });

  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      maxClothes: json['maxClothes'],
      maxClothesPerDay: json['maxClothesPerDay'],
      bunchMinSize: json['bunchMinSize'],
      bunchMaxSize: json['bunchMaxSize'],
      version: json['version'],
      chargesPerValue: json['chargesPerValue'],
      chatNumber: json['chatNumber'],
      callNumber: json['callNumber'],
      email: json['email'],
      // buildNumber: json['buildNumber'],
      iosbuildNumber: json['iosbuildNumber'],
      andbuildNumber: json['andbuildNumber'],
      tutorialLink:
          json.containsKey('tutorialLink') ? json['tutorialLink'] : null,
      noteLink: json.containsKey('noteLink') ? json['noteLink'] : null,
      roomTypes: Map.from(json["roomTypes"]).entries.map((entry) {
        return RoomTypes.fromJson(entry.key);
      }).toList(),
      note: json.containsKey('note') ? json['note'] : '',
      clothtypes: Map.from(json["clothtypes"]).entries.map((entry) {
        return ClothType.fromJson(entry.key);
      }).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'maxClothes': maxClothes,
      'maxClothesPerDay': maxClothesPerDay,
      'chargesPerValue': chargesPerValue,
      'bunchMaxSize': bunchMaxSize,
      'bunchMinSize': bunchMinSize,
      'chatNumber': chatNumber,
      'version': version,
      'tutorialLink': tutorialLink,
      'buildNumber': iosbuildNumber,
      'andbuildNumber': andbuildNumber,
      'noteLink': noteLink,
      'note': note,
    };
  }

  factory SettingsModel.fromSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return SettingsModel(
      maxClothes: json['maxClothes'],
      maxClothesPerDay: json['maxClothesPerDay'],
      bunchMinSize: json['bunchMinSize'],
      version: json['version'],
      callNumber: json['callNumber'],
      email: json['email'],
      iosbuildNumber: json['iosbuildNumber'],
      andbuildNumber: json['andbuildNumber'],
      chatNumber: json['chatNumber'],
      bunchMaxSize: json['bunchMaxSize'],
      tutorialLink: json['tutorialLink'],
      noteLink: json['noteLink'],
      chargesPerValue: json['chargesPerValue'],
      note: json['note'],
      roomTypes: List<RoomTypes>.from(
          json["roomTypes"].map((model) => RoomTypes.fromJson(model))),
      clothtypes: List<ClothType>.from(
          json["clothtypes"].map((model) => ClothType.fromJson(model))),
    );
  }
}

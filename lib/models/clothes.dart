import 'package:cloud_firestore/cloud_firestore.dart';

class Clothes {
  final String docId;
  final String photoUrl;
  final String category;
  final Timestamp? uploadedAt;

  Clothes({
    required this.uploadedAt,
    required this.docId,
    required this.photoUrl,
    required this.category,
  });

  factory Clothes.fromSnap(QueryDocumentSnapshot<Map<String, dynamic>> snap) {
    return Clothes(
      docId: snap.id,
      photoUrl: snap['photoUrl'] ?? '',
      category: snap['category'] ?? '',
      uploadedAt:
          snap.data().containsKey('uploadedAt') ? snap['uploadedAt'] : null,
    );
  }
  factory Clothes.fromJson(Map<String, dynamic> json) {
    return Clothes(
      docId: json['docId'] ?? '',
      photoUrl: json['photoUrl'] ?? '',
      category: json['category'] ?? '',
      uploadedAt: json['uploadedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'photoUrl': photoUrl,
      'category': category,
    };
  }
}

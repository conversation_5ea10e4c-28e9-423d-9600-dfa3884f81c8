import 'package:cloud_firestore/cloud_firestore.dart';

class ComplaintsModel {
  final String docId;
  final String uId;
  final String randomId;
  final String hostelDocId;
  // final String hostelName;
  // final String hostelCode;
  final String wing;
  final String? mobile;
  final String? remark;
  final int floor;
  final int roomNo;
  final int bedNo;
  final String residentName;
  final String? bunchDocId;
  final String? pickUpScheduleDocId;
  final List<String> complainType;
  final String status;
  final String? complainDesc;
  final bool autoGenerate;
  final bool resolved;
  final List<String> missingClothes;
  final List<String> clothesUrl;
  final Timestamp createdAt;
  final Timestamp? resolvedAt;
  final DateTime? pickupDate;
  final bool? isSatisfied;

  ComplaintsModel({
    required this.docId,
    required this.randomId,
    required this.missingClothes,
    required this.clothesUrl,
    required this.uId,
    required this.complainDesc,
    required this.remark,
    required this.hostelDocId,
    // required this.hostelName,
    // required this.hostelCode,
    required this.wing,
    required this.floor,
    required this.roomNo,
    required this.bedNo,
    this.mobile,
    required this.residentName,
    required this.bunchDocId,
    required this.pickUpScheduleDocId,
    required this.complainType,
    required this.status,
    required this.autoGenerate,
    required this.resolved,
    required this.createdAt,
    required this.resolvedAt,
    required this.pickupDate,
    this.isSatisfied,
  });

  factory ComplaintsModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return ComplaintsModel(
      docId: json.id,
      uId: json['uId'],
      randomId: json.data()['randomId'],
      hostelDocId: json['hostelDocId'],
      // hostelName: json['hostelName'],
      // hostelCode: json['hostelCode'],
      wing: json['wing'],
      complainDesc: json['complainDesc'],
      floor: json['floor'],
      remark: json['remark'],
      mobile: json.data()['mobile'],
      roomNo: json['roomNo'],
      bedNo: json['bedNo'],
      residentName: json['residentName'],
      bunchDocId: json['bunchDocId'],
      pickUpScheduleDocId: json['pickUpScheduleDocId'],
      clothesUrl: List<String>.from(json['clothesUrl']),
      missingClothes: List<String>.from(json['missingClothes']),
      complainType: List<String>.from(json['complainType']),
      status: json['status'],
      autoGenerate: json['autoGenerate'],
      resolved: json['resolved'],
      createdAt: json['createdAt'],
      isSatisfied: json.data()['isSatisfied'],
      resolvedAt: json.data()['resolvedAt'] ?? Timestamp.now(),
      pickupDate: (json.data()['pickupDate'] as Timestamp?)?.toDate(),
    );
  }
  factory ComplaintsModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> json) {
    return ComplaintsModel(
      docId: json.id,
      uId: json['uId'],
      randomId: json.data()?['randomId'],
      hostelDocId: json['hostelDocId'],
      // hostelName: json['hostelName'],
      // hostelCode: json['hostelCode'],
      wing: json['wing'],
      complainDesc: json['complainDesc'],
      floor: json['floor'],
      remark: json['remark'],
      roomNo: json['roomNo'],
      mobile: json.data()?['mobile'],
      bedNo: json['bedNo'],
      residentName: json['residentName'],
      bunchDocId: json['bunchDocId'],
      pickUpScheduleDocId: json['pickUpScheduleDocId'],
      clothesUrl: List<String>.from(json['clothesUrl']),
      missingClothes: List<String>.from(json['missingClothes']),
      complainType: List<String>.from(json['complainType']),
      status: json['status'],
      autoGenerate: json['autoGenerate'],
      resolved: json['resolved'],
      createdAt: json['createdAt'],
      isSatisfied: json.data()?['isSatisfied'],

      resolvedAt: json.data()?['resolvedAt'] ?? Timestamp.now(),
      pickupDate: (json.data()?['pickupDate'] as Timestamp?)?.toDate(),
    );
  }
  factory ComplaintsModel.fromJson(Map<String, dynamic> json) {
    return ComplaintsModel(
      docId: json['docId'],
      uId: json['uId'],
      clothesUrl: List<String>.from(json['clothesUrl']),
      missingClothes: List<String>.from(json['missingClothes']),
      hostelDocId: json['hostelDocId'],
      wing: json['wing'],
      complainDesc: json['complainDesc'],
      floor: json['floor'],
      roomNo: json['roomNo'],
      bedNo: json['bedNo'],
      mobile: json['mobile'],
      remark: json['remark'],
      randomId: json['randomId'],
      residentName: json['residentName'],
      bunchDocId: json['bunchDocId'],
      pickUpScheduleDocId: json['pickUpScheduleDocId'],
      complainType: List<String>.from(json['complainType']),
      status: json['status'],
      autoGenerate: json['autoGenerate'],
      resolved: json['resolved'],
      createdAt: json['createdAt'],
      resolvedAt: json['resolvedAt'],
      pickupDate: json['pickupDate'],
      isSatisfied: json['isSatisfied'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'uId': uId,
      'hostelDocId': hostelDocId,
      'wing': wing,
      'clothesUrl': clothesUrl,
      'randomId': randomId,
      'complainDesc': complainDesc,
      'missingClothes': missingClothes,
      'floor': floor,
      'roomNo': roomNo,
      'remark': remark,
      'bedNo': bedNo,
      'residentName': residentName,
      'mobile': mobile,
      'bunchDocId': bunchDocId,
      'pickUpScheduleDocId': pickUpScheduleDocId,
      'complainType': complainType,
      'status': status,
      'autoGenerate': autoGenerate,
      'resolved': resolved,
      'createdAt': createdAt,
      'resolvedAt': resolvedAt,
      'pickupDate': pickupDate,
      'isSatisfied': isSatisfied,
    };
  }
}

class ComplainType {
  static const missingclothes = "Missing clothes";
  static const tornClothes = "Torn clothes";
  static const staincolorBleedingClothes = "Stains & colour bledding";
  static const shrinkStretchClothes = "Shrinking & stretching";
  static const other = "Other";
}

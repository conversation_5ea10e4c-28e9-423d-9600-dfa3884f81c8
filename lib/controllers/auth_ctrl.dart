import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:laundry_resident/shared/firebse.dart';
import '../models/settings.dart';
import '../shared/methods.dart';

class AuthCtrl extends GetxController {
  final emailctrl = TextEditingController();
  final passctrl = TextEditingController();
  final verifyPassCtrl = TextEditingController();
  SettingsModel? settings;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? settingsStream;

  final FirebaseAuth auth = FirebaseAuth.instance;
  @override
  void onInit() {
    super.onInit();
    setting();
  }

  setting() async {
    try {
      settingsStream?.cancel();
      settingsStream = FBFireStore.settings.snapshots().listen((event) {
        settings = SettingsModel.fromSnap(event);

        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future resetPassword() async {
    try {
      await FBAuth.auth.sendPasswordResetEmail(email: emailctrl.text.trim());
      showAppSnackBar('Password Sent to enetred Email');
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<bool> onSignUp() async {
    if (passctrl.text == verifyPassCtrl.text) {
      return await createAccount(emailctrl.text, passctrl.text);
    } else {
      return false;
    }
  }

  Future<bool> createAccount(String email, String password) async {
    try {
      await auth.createUserWithEmailAndPassword(
          email: email, password: password);
      return true;
    } on FirebaseAuthException catch (signupError) {
      if (signupError.code == "weak-password") {
        showAppSnackBar('Weak Password');
        return false;
      } else if (signupError.code == "email-already-in-use") {
        showAppSnackBar('Account with same email exist.');
        return false;
      } else {
        showAppSnackBar('Unknown error occured. Please try again');
        return false;
      }
    }
  }

  Future<bool> onLogin() async {
    return await logIn(
      emailctrl.text,
      passctrl.text,
    );
  }

  Future<bool> logIn(
    String email,
    String password,
  ) async {
    try {
      await auth.signInWithEmailAndPassword(email: email, password: password);
      return true;
      // update();
    } on FirebaseAuthException catch (loginerror) {
      if (loginerror.code == 'invalid-credential') {
        showAppSnackBar('Wrong credentals');
        return false;
        // } else if (loginerror.code == 'wrong-password') {
        //   print('Login Error2');
        //   showAppSnackBar('Wrong password');
        //   return false;
      } else {
        debugPrint(loginerror.toString());
      }
      showAppSnackBar('Something went wrong. Please try again after sometime.');
      return false;

      // } catch (e) {
      //   print(e);

      // }
    }
  }
}

// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:go_router/go_router.dart';

// import '../shared/router.dart';

// class AuthCtrl extends GetxController {
//   late final TextEditingController emailctrl;
//   late final TextEditingController passctrl;
//   createCred() async {
//     try {
//       final credential =
//           await FirebaseAuth.instance.createUserWithEmailAndPassword(
//         email: emailctrl.text,
//         password: passctrl.text,
//       );
//     } on FirebaseAuthException catch (e) {
//       if (e.code == 'weak-password') {
//         print('The password provided is too weak.');
//       } else if (e.code == 'email-already-in-use') {
//         print('The account already exists for that email.');
//       }
//     } catch (e) {
//       print(e);
//     }
//   }

//   signIn() async {
//     try {
//       final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
//           email: emailctrl.text, password: passctrl.text);
//     } on FirebaseAuthException catch (e) {
//       if (e.code == 'user-not-found') {
//         print('No user found for that email.');
//       } else if (e.code == 'wrong-password') {
//         print('Wrong password provided for that user.');
//       }
//     }
//   }
// }

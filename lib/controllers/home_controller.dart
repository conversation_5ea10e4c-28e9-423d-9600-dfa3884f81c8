import 'dart:async';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:laundry_resident/models/hostel_model_ori.dart';
import 'package:laundry_resident/models/pickup_schedules.dart';
import 'package:laundry_resident/models/resident.dart';
import 'package:laundry_resident/models/review_model.dart';
import 'package:laundry_resident/models/schedules.dart';
import 'package:laundry_resident/shared/firebse.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:unique_identifier/unique_identifier.dart';
import '../models/clothes.dart';
import '../models/settings.dart';
import '../shared/methods.dart';

class HomeCtrl extends GetxController {
  bool fcmTokenChecked = false;
  bool versionSupported = true;
  bool sameDevice = false;
  int selectedIndex = 0;
  bool loaded = false;
  bool deviceLoaded = false;
  // String? currentMac;
  ResidentModel? residentModel;
  // List<HostelModel> hostels = <HostelModel>[];
  HostelModel? hostelModel;
  PickUpScheduleModel? currentPickupSchedule;
  List<PickUpScheduleModel> lastPickupedSchedules = [];
  SettingsModel? settings;
  List<SchedulesModel> hostelSchedules = <SchedulesModel>[];
  SchedulesModel? nextSchedule;
  List<Clothes> clothes = <Clothes>[];
  List<String> topics = <String>[];
  DateTime? nextHostelPickUpDate;
  DateTime? nextHostelDeliveryDate;
  ReviewModel? review;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? currentUserStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? hostelsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      hostelSchedulesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? schedulesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? userClothesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? reviewsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? currentpickup;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? lastpickup;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? settingsStream;

  @override
  void onInit() {
    super.onInit();
    setupAuthStream();
    // print(hostels.length);
  }

  setupAuthStream() async {
    try {
      FBAuth.auth.authStateChanges().listen((user) {
        if (user != null) setupProfileDataStream();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  setupProfileDataStream() async {
    try {
      // if (FBAuth.auth.currentUser == null) return;

      currentUserStream?.cancel();
      // final currentMac = await GetMac.macAddress;
      // print("Current mac: $currentMac");
      // print("=-======-==-==-=$currentMac");

      currentUserStream = FBFireStore.residents
          .doc(FBAuth.auth.currentUser?.uid)
          .snapshots()
          .listen((event) {
        if (event.exists) {
          residentModel = ResidentModel.fromDocSnap(event);
          checkDevice(deviceId: residentModel?.deviceId);
          hostelStream();

          topics = residentModel?.topics ?? <String>[];
          setFcmToken(event.data()?['tokens'] ?? []);
        } else {
          residentModel = null;
          deviceLoaded = true;
        }
        clothesStream();
        reviewStream();
        loaded = true;
        hostelSchedulesStrea();
        setting();
        currentPickUpSchedulStream();
        lastPickupScheduleStream();
        checkVersionSupported();

        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  reviewStream() {
    try {
      reviewsStream?.cancel();
      reviewsStream = FBFireStore.review
          .where('uId', isEqualTo: residentModel?.docId)
          .where('profileUnderReview', isEqualTo: true)
          .snapshots()
          .listen((eve) {
        if (eve.size > 0) {
          review = ReviewModel.fromSnap(eve.docs.first);
        } else {
          review = null;
        }
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  setFcmToken(List tokens) async {
    try {
      if (fcmTokenChecked) return;
      fcmTokenChecked = true;
      String? fcmToken = await FirebaseMessaging.instance.getToken();
      debugPrint('FCMTOKEN::::::: $fcmToken');
      if (fcmToken != null) {
        if (tokens.length > 7) {
          await FBFireStore.residents.doc(FBAuth.auth.currentUser?.uid).update({
            'tokens': [fcmToken]
          });
        } else {
          await FBFireStore.residents.doc(FBAuth.auth.currentUser?.uid).update({
            'tokens': FieldValue.arrayUnion([fcmToken])
          });
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  clothesStream() {
    try {
      userClothesStream?.cancel();
      userClothesStream = FBFireStore.residents
          .doc(FBAuth.auth.currentUser?.uid)
          .collection('clothes')
          .snapshots()
          .listen((eve) {
        clothes = eve.docs.map((e) => Clothes.fromSnap(e)).toList();
        update();
        // print(clothes.length);
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  // topicStream() {
  //   try {
  //     userClothesStream?.cancel();
  //     userClothesStream = FBFireStore.residents
  //         .doc(FBAuth.auth.currentUser?.uid)
  //         .collection('topics')
  //         .snapshots()
  //         .listen((eve) {
  //       topics = eve.docs.map((e) => e.data()['']).toList();
  //       update();
  //       // print(clothes.length);
  //     });
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  // }

  hostelStream() async {
    try {
      hostelsStream?.cancel();
      hostelsStream = FBFireStore.hostels
          .doc(residentModel?.hostelDocId)
          .snapshots()
          .listen((event) {
        if (event.exists) {
          hostelModel = HostelModel.fromJson(event);
        }
      });

      currentPickUpSchedulStream();
      update();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  hostelSchedulesStrea() async {
    try {
      hostelSchedulesStream?.cancel();
      hostelSchedulesStream = FBFireStore.schedules
          .where("hostelDocId", isEqualTo: residentModel?.hostelDocId)
          .where("date",
              isGreaterThanOrEqualTo: Timestamp.fromDate(DateTime(
                DateTime.now().year,
                DateTime.now().month,
                DateTime.now().day,
              )))
          .snapshots()
          .listen((ev) {
        hostelSchedules =
            ev.docs.map((e) => SchedulesModel.fromSnap(e)).toList();

        setNextPickUpDate();
        setNextDeliveryDate();
        update();
      });
      // print(weeklyPickupSchedules.length);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  setting() async {
    try {
      settingsStream?.cancel();
      settingsStream = FBFireStore.settings.snapshots().listen((event) {
        settings = SettingsModel.fromSnap(event);
        settings?.roomTypes.sort((a, b) => a.docId.compareTo(b.docId));
        settings?.clothtypes.sort((c, d) => c.docId.compareTo(d.docId));
        checkVersionSupported();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  checkVersionSupported() async {
    if (settings == null) return;
    final packageInfo = await PackageInfo.fromPlatform();
    // String currentAppVersion = packageInfo.version;
    // String firebaseVersion = settings!.version;
    // print(PackageInfo.)
    String currentBuildVersion = packageInfo.buildNumber;
    String firebaseBuildVersion = Platform.isAndroid
        ? settings!.andbuildNumber
        : settings!.iosbuildNumber;

    // int currentVersionConverted = getExtendedVersionNumber(currentAppVersion);
    // int firebaseVersionconverted = getExtendedVersionNumber(firebaseVersion);
    versionSupported =
        int.parse(currentBuildVersion) >= int.parse(firebaseBuildVersion);
    update();
  }

  currentPickUpSchedulStream() async {
    try {
      currentpickup?.cancel();
      currentpickup = FBFireStore.pickUpSchedules
          .where("uId", isEqualTo: residentModel?.docId)
          .where("pickedUp", isEqualTo: false)
          .where("delivered", isEqualTo: false)
          .snapshots()
          .listen((event) {
        if (event.size > 0) {
          try {
            currentPickupSchedule =
                PickUpScheduleModel.fromSnap(event.docs.first);
          } on Exception catch (e) {
            debugPrint(e.toString());
          }
        } else {
          currentPickupSchedule = null;
        }
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  lastPickupScheduleStream() async {
    try {
      lastpickup?.cancel();
      lastpickup = FBFireStore.pickUpSchedules
          .where("uId", isEqualTo: residentModel?.docId)
          .where("pickedUp", isEqualTo: true)
          .where("delivered", isEqualTo: false)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .listen((event) {
        if (event.size > 0) {
          lastPickupedSchedules =
              event.docs.map((e) => PickUpScheduleModel.fromSnap(e)).toList();
        } else {
          lastPickupedSchedules = [];
        }
        lastPickupedSchedules
            .sort((a, b) => a.createdAt.compareTo(b.createdAt));
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  setNextPickUpDate() {
    final hostelPickUpSchedules =
        hostelSchedules.where((element) => element.pickUp);
    if (hostelPickUpSchedules.isEmpty) return;
    final hostelWeeklyPickUpSchedules =
        hostelPickUpSchedules.where((element) => element.weekly).toList();
    final hostelDateWiseSchedules =
        hostelPickUpSchedules.where((element) => !element.weekly).toList();
    DateTime today =
        DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
    hostelDateWiseSchedules
        .sort((a, b) => a.date?.compareTo(b.date ?? today) ?? 0);
    hostelWeeklyPickUpSchedules
        .sort((a, b) => a.day?.compareTo(b.day ?? 0) ?? 0);
    //
    DateTime nextPickUpDate = DateTime.now();
    final lastPickUpDate = (lastPickupedSchedules.isEmpty
        ? null
        : (lastPickupedSchedules.first.pickedAt != null
            ? DateTime(
                lastPickupedSchedules.first.pickedAt!.toDate().year,
                lastPickupedSchedules.first.pickedAt!.toDate().month,
                lastPickupedSchedules.first.pickedAt!.toDate().day)
            : null));
    // final lastPickUpDate = hostelModel?.lastPickupDate ?? today;
    // IF lastPickUpDate is today,
    // then incrementing today to directly get next day insted of showing today
    if (lastPickUpDate == today) {
      today = today.add(const Duration(days: 1));
    }
    // Check In << Weekly >> Schedules //
    if (hostelWeeklyPickUpSchedules.length == 1) {
      final thisDay = (hostelWeeklyPickUpSchedules.first.day ?? 0);
      final dayDifference = thisDay - today.weekday;
      if (dayDifference.isNegative) {
        nextPickUpDate =
            today.add(Duration(days: (thisDay - today.weekday) + 7));
      } else {
        nextPickUpDate = today.add(Duration(days: thisDay - today.weekday));
      }
      nextSchedule = hostelWeeklyPickUpSchedules.first;
    } else {
      List<SchedulesModel> listOfSchGreaterThanToday = [];
      List<SchedulesModel> listOfSchSmallerThanToday = [];
      List<SchedulesModel> listOfSchEqualToToday = [];

      // Split data into list - data already sorted here
      for (var element in hostelWeeklyPickUpSchedules) {
        final dayDifference = (element.day ?? 0) - today.weekday;
        switch (dayDifference) {
          case == 0:
            listOfSchEqualToToday.add(element);
            break;
          case < 0:
            listOfSchSmallerThanToday.add(element);
            break;
          case > 0:
            listOfSchGreaterThanToday.add(element);
            break;
        }
      }

      // Priority wise assignment
      if (listOfSchEqualToToday.isNotEmpty) {
        nextPickUpDate = today;
        nextSchedule = listOfSchEqualToToday.first;
      } else if (listOfSchGreaterThanToday.isNotEmpty) {
        final thisDay = listOfSchGreaterThanToday.first;
        final dayDifference = (thisDay.day ?? 0) - today.weekday;
        nextPickUpDate = today.add(Duration(days: dayDifference));
        nextSchedule = thisDay;
      } else {
        final thisDay = listOfSchSmallerThanToday.first;
        final dayDifference = (thisDay.day ?? 0) - today.weekday;
        nextPickUpDate = today.add(Duration(days: dayDifference + 7));
        nextSchedule = thisDay;
      }
    }

    // Check if Any << DATE WISE >> pickup is scheduled //
    if (hostelDateWiseSchedules.isNotEmpty) {
      if (hostelDateWiseSchedules.first.date?.isBefore(nextPickUpDate) ??
          false) {
        nextPickUpDate = hostelDateWiseSchedules.first.date ?? nextPickUpDate;
        nextSchedule = hostelDateWiseSchedules.first;
      }
    }

    // Assigning to var
    nextHostelPickUpDate = nextPickUpDate;
    debugPrint('NEXT PICKUP DATE: $nextHostelPickUpDate');
  }

  setNextDeliveryDate() {
    final hostelDeliverySchedules =
        hostelSchedules.where((element) => !element.pickUp);
    if (hostelDeliverySchedules.isEmpty) return;
    final hostelWeeklyDeliverySchedules =
        hostelDeliverySchedules.where((element) => element.weekly).toList();
    final hostelDateWiseSchedules =
        hostelDeliverySchedules.where((element) => !element.weekly).toList();
    DateTime today =
        DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
    hostelDateWiseSchedules
        .sort((a, b) => a.date?.compareTo(b.date ?? today) ?? 0);
    hostelWeeklyDeliverySchedules
        .sort((a, b) => a.day?.compareTo(b.day ?? 0) ?? 0);
    //
    DateTime nextDeliveryDate = DateTime.now();
    final lastDeliveryDate = (lastPickupedSchedules.isEmpty
        ? null
        : (lastPickupedSchedules.first.pickedAt != null
            ? lastPickupedSchedules.first.deliveryTime == null
                ? null
                : DateTime(
                    lastPickupedSchedules.first.deliveryTime!.toDate().year,
                    lastPickupedSchedules.first.deliveryTime!.toDate().month,
                    lastPickupedSchedules.first.deliveryTime!.toDate().day)
            : null));
    // final lastDeliveryDate = hostelModel?.lastDeliveryDate ?? today;
    if (lastDeliveryDate == today) {
      today = today.add(const Duration(days: 1));
    }
    // Check In <<Weekly>> Schedules //
    if (hostelWeeklyDeliverySchedules.length == 1) {
      final thisDay = (hostelWeeklyDeliverySchedules.first.day ?? 0);
      final dayDifference = thisDay - today.weekday;
      if (dayDifference.isNegative) {
        nextDeliveryDate =
            today.add(Duration(days: (thisDay - today.weekday) + 7));
      } else {
        nextDeliveryDate = today.add(Duration(days: thisDay - today.weekday));
      }
    } else {
      List<SchedulesModel> listOfSchGreaterThanToday = [];
      List<SchedulesModel> listOfSchSmallerThanToday = [];
      List<SchedulesModel> listOfSchEqualToToday = [];
      // Split data into list - data already sorted here
      for (var element in hostelWeeklyDeliverySchedules) {
        final dayDifference = (element.day ?? 0) - today.weekday;
        switch (dayDifference) {
          case == 0:
            listOfSchEqualToToday.add(element);
            break;
          case < 0:
            listOfSchSmallerThanToday.add(element);
            break;
          case > 0:
            listOfSchGreaterThanToday.add(element);
            break;
        }
      }
      // Priority wise assignment
      if (listOfSchEqualToToday.isNotEmpty) {
        nextDeliveryDate = today;
      } else if (listOfSchGreaterThanToday.isNotEmpty) {
        final thisDay = listOfSchGreaterThanToday.first;
        final dayDifference = (thisDay.day ?? 0) - today.weekday;
        nextDeliveryDate = today.add(Duration(days: dayDifference));
      } else {
        final thisDay = listOfSchSmallerThanToday.first;
        final dayDifference = (thisDay.day ?? 0) - today.weekday;
        nextDeliveryDate = today.add(Duration(days: dayDifference + 7));
      }
    }

    // Check if Any <<DATE WISE>> pickup is scheduled //
    if (hostelDateWiseSchedules.isNotEmpty) {
      if (hostelDateWiseSchedules.first.date?.isBefore(nextDeliveryDate) ??
          false) {
        nextDeliveryDate =
            hostelDateWiseSchedules.first.date ?? nextDeliveryDate;
      }
    }

    // Assigning to var
    nextHostelDeliveryDate = nextDeliveryDate;
    debugPrint('NEXT DELIVERY DATE: $nextHostelDeliveryDate');
  }

  // checkConnectivity() async {
  //   final List<ConnectivityResult> connectivityResult =
  //       await (Connectivity().checkConnectivity());

  //   if (connectivityResult.contains(ConnectivityResult.mobile)) {
  //     return true;
  //   } else if (connectivityResult.contains(ConnectivityResult.wifi)) {
  //     return true;
  //     // Wi-fi is available.
  //     // Note for Android:
  //     // When both mobile and Wi-Fi are turned on system will return Wi-Fi only as active network type
  //   } else {
  //     return false;
  //   }
  // }

  checkDevice({required String? deviceId}) async {
    if (deviceId == null) {
      await FBFireStore.residents.doc(FBAuth.auth.currentUser?.uid).update({
        'deviceId': await UniqueIdentifier.serial,
      });
      sameDevice = true;
      deviceLoaded = true;
      return;
    }
    deviceLoaded = false;
    final currentDeviceId = await UniqueIdentifier.serial;
    if (deviceId == currentDeviceId) {
      sameDevice = true;
      deviceLoaded = true;
      update();
    }
    if (deviceId != currentDeviceId) {
      sameDevice = false;
      deviceLoaded = true;
      update();
    }
  }
}

Future<bool> initiateComplaint({
  required String uId,
  required String hostelDocId,
  required String wing,
  required int floor,
  required int roomNo,
  required int bedNo,
  required String residentName,
  required String bunchDocId,
  required String pickUpScheduleDocId,
  required List<String> complainType,
  required String randomId,
  required String? complainDesc,
  required List<String> clothesUrl,
  required List<String> missingClothes,
  required bool autoGenerate,
  required DateTime pickupDate,
}) async {
  try {
    final packageInfo = await PackageInfo.fromPlatform();
    final data = <String, dynamic>{
      'uId': uId,
      'hostelDocId': hostelDocId,
      'wing': wing,
      'floor': floor,
      'roomNo': roomNo,
      'bedNo': bedNo,
      'residentName': residentName,
      'bunchDocId': bunchDocId,
      'pickUpScheduleDocId': pickUpScheduleDocId,
      'complainType': complainType,
      'complainDesc': complainDesc,
      'status': "Active",
      'autoGenerate': autoGenerate,
      'resolved': false,
      'createdAt': Timestamp.now(),
      'remark': null,
      'clothesUrl': clothesUrl,
      'randomId': randomId,
      'missingClothes': missingClothes,
      'pickupDate': pickupDate,
      'mobileVersion': packageInfo.buildNumber,
    };
    final newDocId = FBFireStore.complaints.doc().id;
    await FBFireStore.complaints.doc(newDocId).set(data);
    await FBFireStore.pickUpSchedules
        .doc(pickUpScheduleDocId)
        .update({"complaintDocId": newDocId});
    initiateActivity(
      uId: uId,
      type: ActivityType.complain,
      typeDocId: newDocId,
      desc:
          'Your complain for ${complainType[0]} has been raised under ID: $randomId on ${DateTime.now().convertToDDMMYY()} at ${DateTime.now().goodTime()}.',
      userType: UserTypes.resident,
      title: 'Complain',
    );
    return true;
  } catch (e) {
    debugPrint(e.toString());
    return false;
  }
}

initiateActivity({
  required String? uId,
  required String? type,
  required String? typeDocId,
  required String? desc,
  required String? userType,
  required String? title,
}) async {
  try {
    final data = <String, dynamic>{
      'createdAt': Timestamp.now(),
      'uId': uId,
      'userType': userType,
      'title': title,
      'type': type,
      'typeDocId': typeDocId,
      'desc': desc,
    };
    await FBFireStore.activities.add(data);
  } catch (e) {
    debugPrint(e.toString());
  }
}

class UserTypes {
  static const admin = 'admin';
  static const staff = 'staff';
  static const resident = 'resident';
}

class ActivityType {
  static const clothesPicked = 'clothesPicked';
  static const clothesDelivered = 'clothesDelivered';
  static const clothesTallied = 'clothesTallied';
  static const complain = 'complain';
  static const profileRequest = 'profileRequest';
  static const balance = 'balance';
}

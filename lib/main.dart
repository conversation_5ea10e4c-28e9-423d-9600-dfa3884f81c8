import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/instance_manager.dart';
import 'controllers/auth_ctrl.dart';
import 'firebase_options.dart';
import 'shared/const.dart';
import 'shared/router.dart';
import 'shared/theme.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  if (Platform.isIOS) {
    flutterLocalNotificationsPlugin.show(
        DateTime.now().microsecond,
        message.data['title'],
        message.data['body'],
        const NotificationDetails(
            android: androidPlatformChannelSpecifics,
            iOS: iOSPlatformChannelSpecifics));
  }
}

Future<void> _onDidReceiveBackgroundNotification(
    NotificationResponse details) async {
  // print(details.payload);
}

AndroidNotificationChannel? channel;

// INIT Local Notification
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // FirebaseFirestore.instance.settings =
  //     const Settings(persistenceEnabled: false);
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // ---------------------- FCM ---------------------- //
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  channel = const AndroidNotificationChannel(
    'bosslaundry',
    'bosslaundry',
    importance: Importance.high,
  );

  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel!);

  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );

  FirebaseMessaging.instance.requestPermission(
    alert: true,
    badge: true,
    provisional: false,
    sound: true,
  );

  // ---------------------- Local Notification ---------------------- //
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('tielogo');
  final DarwinInitializationSettings initializationSettingsIOS =
      DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
          defaultPresentBadge: false,
          onDidReceiveLocalNotification:
              (int i, String? x, String? y, String? z) {});
  final InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid, iOS: initializationSettingsIOS);
  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (details) {
      debugPrint(details.payload);
    },
    onDidReceiveBackgroundNotificationResponse:
        _onDidReceiveBackgroundNotification,
  );
  runApp(const MyApp());
}

/* 
MediaQuery(
      data: MediaQuery.of(context)
          .copyWith(textScaler: const TextScaler.linear(1.0)),
      child: const MyApp()) 
      */

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // Get.put(HomeCtrl());
    Get.put(AuthCtrl());
  }

  @override
  Widget build(BuildContext context) {
    // print(MediaQuery.sizeOf(context).width);
    SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle.dark.copyWith(statusBarColor: Colors.transparent));
    return MediaQuery(
      data: MediaQuery.of(context)
          .copyWith(boldText: false, textScaler: const TextScaler.linear(1.0)),
      child: MaterialApp.router(
        scaffoldMessengerKey: snackbarKey,
        debugShowCheckedModeBanner: false,
        theme: themeData,
        routerConfig: appRouter,
      ),
    );
  }
}

// Notification Channels //

const AndroidNotificationDetails androidPlatformChannelSpecifics =
    AndroidNotificationDetails(
  'bosslaundry',
  'bosslaundry',
  importance: Importance.max,
  showWhen: false,
  icon: 'tielogo',
  // largeIcon: DrawableResourceAndroidBitmap('tielogo'),
  playSound: true,
  // sound: RawResourceAndroidNotificationSound('pop'),
  enableLights: true,
);

const DarwinNotificationDetails iOSPlatformChannelSpecifics =
    DarwinNotificationDetails(
  presentAlert: true,
  presentBadge: true,
  presentSound: true,
);

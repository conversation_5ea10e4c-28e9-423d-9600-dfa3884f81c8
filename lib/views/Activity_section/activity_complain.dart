import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/models/complaints_model.dart';
import 'package:laundry_resident/shared/methods.dart';

import '../../shared/firebse.dart';

class ActivityComplain extends StatefulWidget {
  const ActivityComplain({super.key, required this.typeDocId});
  final String typeDocId;

  @override
  State<ActivityComplain> createState() => _ActivityComplainState();
}

class _ActivityComplainState extends State<ActivityComplain> {
  bool fetchingData = false;
  @override
  void initState() {
    super.initState();
    getComplainData();
  }

  ComplaintsModel? complain;
  List<String> clothes = [];

  getComplainData() async {
    fetchingData = true;
    setState(() {});
    final ctrl = Get.find<HomeCtrl>();
    complain =
        await FBFireStore.complaints.doc(widget.typeDocId).get().then((value) {
      if (value.data() == null) {
        return null;
      }
      return ComplaintsModel.fromDocSnap(value);
    });

    final missingClothesUrl = ctrl.clothes
        .where(
            (cloth) => complain?.missingClothes.contains(cloth.docId) ?? false)
        .map((e) => e.photoUrl)
        .toList();
    clothes = complain?.complainType[0] == 'Missing clothes'
        ? missingClothesUrl
        : complain?.clothesUrl ?? [];
    fetchingData = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shadowColor: Colors.white,
        titleSpacing: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
        title: const Text(
          'Complain Details',
          style: TextStyle(
            color: Color(0xFF4F4F4F),
            // fontSize: 24,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: fetchingData
          ? const Center(child: CircularProgressIndicator(strokeWidth: 3.5))
          : complain != null
              ? SingleChildScrollView(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 13),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 13.0, vertical: 15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              DisplayUi(
                                  title: 'Complain Id:',
                                  data: complain?.randomId),
                              const SizedBox(height: 4),
                              DisplayUi(
                                  title: 'Date:',
                                  data: complain?.createdAt
                                      .toDate()
                                      .goodDayDate()
                                      .toString()),
                              const SizedBox(height: 4),
                              DisplayUi(
                                  title: 'Type:',
                                  data: complain?.complainType[0]),
                              const SizedBox(height: 4),
                              DisplayUi(
                                  title: 'Status:', data: complain?.status),
                              if (complain?.resolved ?? false)
                                const SizedBox(height: 4),
                              if (complain?.resolved ?? false)
                                DisplayUi(
                                    title: 'Resolved on:',
                                    data: complain?.resolvedAt
                                        ?.toDate()
                                        .goodDayDate()),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 5.0),
                        child: Text(
                          'Description:',
                          style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w500,
                              color: Color(0xff2d2e2f)),
                        ),
                      ),
                      const SizedBox(height: 7),
                      Container(
                        width: double.maxFinite,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10.0, vertical: 12),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6),
                            border:
                                Border.all(color: Colors.black12, width: 1.2)
                            // boxShadow: const [
                            //   BoxShadow(
                            //     blurRadius: 1,
                            //     color: Colors.black12,
                            //     offset: Offset(0, 0),
                            //     spreadRadius: 1,
                            //   ),
                            // ],
                            ),
                        child: Text(complain?.complainDesc ?? ""),
                      ),

                      /*     Card(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10.0, vertical: 12),
                            child: Text(complain?.complainDesc ?? ""),
                          )), */
                      const SizedBox(height: 13),
                      if (complain?.remark != null)
                        const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 5.0),
                          child: Text(
                            'Remark:',
                            style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                                color: Color(0xff2d2e2f)),
                          ),
                        ),
                      if (complain?.remark != null) const SizedBox(height: 7),
                      if (complain?.remark != null)
                        Container(
                          width: double.maxFinite,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10.0, vertical: 12),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(6),
                              border:
                                  Border.all(color: Colors.black12, width: 1.2)
                              // boxShadow: const [
                              //   BoxShadow(
                              //     blurRadius: 1,
                              //     color: Colors.black12,
                              //     offset: Offset(0, 0),
                              //     spreadRadius: 1,
                              //   ),
                              // ],
                              ),
                          child: Text(complain?.remark ?? ""),
                        ),
                      /*    Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10.0, vertical: 12),
                              child: Text(complain?.remark ?? ""),
                            )), */
                      if (complain?.remark != null) const SizedBox(height: 13),
                      const Text(
                        'Clothes:',
                        style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                            color: Color(0xff2d2e2f)),
                      ),
                      const SizedBox(height: 4),
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 130,
                        mainAxisSpacing: 12,
                        crossAxisSpacing: 12,
                        children: [
                          ...List.generate(
                            clothes.length,
                            (index) {
                              return AspectRatio(
                                aspectRatio: 1,
                                child: Container(
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5)),
                                  child: CachedNetworkImage(
                                    imageUrl: clothes[index],
                                    placeholder: (context, url) {
                                      return const Icon(
                                        CupertinoIcons.camera,
                                        size: 16,
                                        color: Colors.black26,
                                      );
                                    },
                                    errorWidget: (context, url, error) {
                                      return const Icon(
                                        CupertinoIcons.graph_square,
                                        size: 16,
                                        color: Colors.black26,
                                      );
                                    },
                                  ),
                                ),
                              );
                            },
                          )
                        ],
                      ),
                    ],
                  ),
                )
              : const Center(child: Text("No Data")),
    );
  }
}

class DisplayUi extends StatelessWidget {
  const DisplayUi({super.key, required this.title, required this.data});
  final String title;
  final String? data;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            title,
            style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: Color(0xff2d2e2f)),
          ),
        ),
        const SizedBox(width: 5),
        Expanded(
          child: Text(
            data ?? "",
            style: const TextStyle(fontSize: 13.5, color: Color(0xff2d2e2f)),
          ),
        ),
      ],
    );
  }
}

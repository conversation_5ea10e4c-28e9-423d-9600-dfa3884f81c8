import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/models/pickup_schedules.dart';
import 'package:laundry_resident/shared/methods.dart';
import '../../shared/firebse.dart';

class ActivityPickupScheduleDetails extends StatefulWidget {
  const ActivityPickupScheduleDetails({super.key, required this.typeDocId});
  final String typeDocId;

  @override
  State<ActivityPickupScheduleDetails> createState() =>
      _ActivityPickupScheduleDetailsState();
}

class _ActivityPickupScheduleDetailsState
    extends State<ActivityPickupScheduleDetails> {
  PickUpScheduleModel? pickUpSchedule;
  List<ClothPickupModel> allClothesAdded = [];
  bool fetchingData = false;
  @override
  void initState() {
    super.initState();
    getPickupScheduleData();
  }

  getPickupScheduleData() async {
    fetchingData = true;
    setState(() {});
    pickUpSchedule = await FBFireStore.pickUpSchedules
        .doc(widget.typeDocId)
        .get()
        .then((value) =>
            value.exists ? PickUpScheduleModel.fromDocSnap(value) : null);
    allClothesAdded.clear();
    allClothesAdded.addAll(pickUpSchedule?.clothes ?? []);
    fetchingData = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shadowColor: Colors.white,
        titleSpacing: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
        title: const Text(
          'Picked clothes',
          style: TextStyle(
            color: Color(0xFF4F4F4F),
            // fontSize: 24,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: fetchingData
          ? const Center(
              child: CircularProgressIndicator(strokeWidth: 3.5),
            )
          : pickUpSchedule == null
              ? const Center(child: Text("No Data"))
              : SingleChildScrollView(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text.rich(
                        TextSpan(
                          children: [
                            const TextSpan(
                                text: 'Paid for Value',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                )),
                            const TextSpan(text: ': '),
                            TextSpan(
                                text: (pickUpSchedule?.paidForValue ?? 0)
                                    .toString(),
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                )),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text.rich(
                        TextSpan(
                          children: [
                            const TextSpan(
                                text: 'Paid Amount',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                )),
                            const TextSpan(text: ': '),
                            TextSpan(
                                text:
                                    '₹${(pickUpSchedule?.paidAmount ?? 0).toString()}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                )),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                      ClothesWidget(
                        descriptionPart: 'picked',
                        date:
                            pickUpSchedule!.pickedAt?.toDate().goodDayDate() ??
                                "",
                        filteredClothes: allClothesAdded
                            .where((cloth) => cloth.pickedUp == true)
                            .map((e) => e.docId)
                            .toList(),
                        title: 'Pickup Details',
                        clothes: allClothesAdded,
                      ),
                      const SizedBox(height: 20),
                      /*     if (pickUpSchedule.talliedAt != null)
                    ClothesWidget(
                      descriptionPart: 'tallied',
                      date: pickUpSchedule.talliedAt?.toDate().goodDayDate() ??
                          "",
                      filteredClothes: allClothesAdded
                          .where((cloth) =>
                              cloth.pickedUp == true && cloth.tallied == true)
                          .map((e) => e.docId)
                          .toList(),
                      title: 'Tally Details',
                      clothes: allClothesAdded
                          .where((cloth) => cloth.pickedUp == true)
                          .toList(),
                    ),
                  if (pickUpSchedule.talliedAt != null)
                    const SizedBox(height: 20), */
                      if (pickUpSchedule!.delivered)
                        ClothesWidget(
                          descriptionPart: 'delivered',
                          date: pickUpSchedule?.deliveryTime
                                  ?.toDate()
                                  .goodDayDate() ??
                              "",
                          collectedBy: pickUpSchedule?.collectedByName,
                          filteredClothes: allClothesAdded
                              .where((cloth) =>
                                  cloth.pickedUp == true &&
                                  cloth.tallied == true &&
                                  cloth.collected == true)
                              .map((e) => e.docId)
                              .toList(),
                          title: 'Delivery Details',
                          clothes: allClothesAdded
                              .where((cloth) => cloth.pickedUp == true)
                              .toList(),
                        ),
                    ],
                  ),
                ),
    );
  }
}

class ClothesWidget extends StatelessWidget {
  const ClothesWidget({
    super.key,
    required this.clothes,
    required this.descriptionPart,
    required this.filteredClothes,
    required this.title,
    this.collectedBy,
    required this.date,
  });
  final String date;
  final List<ClothPickupModel> clothes;
  final List<String> filteredClothes;
  final String title;
  final String descriptionPart;
  final String? collectedBy;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          width: double.maxFinite,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            border: Border.all(color: const Color(0xFF01B49E)),
            color: const Color.fromARGB(23, 1, 180, 159),
            // color: themeColor.withOpacity(.3),
          ),
          child: Text(
            title,
            style: const TextStyle(
              // color: Color(0xFF01B49E),
              color: Color(0xff4F4F4F),
              fontSize: 16,
              letterSpacing: .7,
              wordSpacing: .7,
              fontWeight: FontWeight.w500,
            ),
            // style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
        ),
        const SizedBox(height: 3),
        // const Divider(
        //   color: Colors.black,
        //   thickness: .2,
        //   height: 0,
        // ),
        const SizedBox(height: 10),
        Row(
          children: [
            const Text("Date:"),
            const SizedBox(width: 3),
            Text(date),
          ],
        ),
        const SizedBox(height: 5),
        Text(
            'Your ${filteredClothes.length} out of ${clothes.length} clothes are $descriptionPart.'),
        if (collectedBy != null) const SizedBox(height: 5),
        if (collectedBy != null)
          Row(
            children: [
              const Text("Collected By:"),
              const SizedBox(width: 3),
              Text(collectedBy ?? ""),
            ],
          ),
        const SizedBox(height: 5),
        StaggeredGrid.extent(
          maxCrossAxisExtent: 120,
          mainAxisSpacing: 15,
          crossAxisSpacing: 15,
          children: [
            ...List.generate(
              clothes.length,
              (index) {
                final crossMark =
                    !filteredClothes.contains(clothes[index].docId);
                return AspectRatio(
                  aspectRatio: 1,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      Container(
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: const Color(0xFFF2F2F2),
                          // image: DecorationImage(
                          //     image: NetworkImage(widget.imageName), fit: BoxFit.cover),
                        ),
                        child: AspectRatio(
                          aspectRatio: 1,
                          child: CachedNetworkImage(
                            imageUrl: clothes[index].photoUrl,
                            placeholder: (context, url) {
                              return const Icon(
                                CupertinoIcons.camera,
                                color: Colors.black26,
                                size: 17,
                              );
                            },
                            errorWidget: (context, url, error) {
                              return const Icon(CupertinoIcons.graph_square);
                            },
                            fit: BoxFit.fill,
                            width: double.infinity,
                          ),
                        ),
                      ),
                      if (crossMark)
                        const Padding(
                          padding: EdgeInsets.all(1.0),
                          child: AspectRatio(
                            aspectRatio: 1,
                            child: Placeholder(
                              color: Colors.red,
                            ),
                          ),
                        ),
                      if (crossMark)
                        AspectRatio(
                          aspectRatio: 1,
                          child: Container(
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: Colors.white, width: 2.5)),
                          ),
                        ),
                    ],
                  ),
                );
              },
            )
          ],
        )
      ],
    );
  }
}

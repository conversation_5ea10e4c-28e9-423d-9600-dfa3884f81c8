import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/models/resident.dart';
import 'package:laundry_resident/shared/firebse.dart';
import '../../models/activities_model.dart';
import '../../shared/router.dart';

class ActivityPage extends StatefulWidget {
  const ActivityPage({super.key, this.resident});
  final ResidentModel? resident;
  @override
  State<ActivityPage> createState() => _ActivityPageState();
}

class _ActivityPageState extends State<ActivityPage> {
  List<ActivitiesModel> activities = [];
  ScrollController scrollCtrl = ScrollController();
  bool reloading = false;
  bool fetchingData = false;
  bool allLoaded = false;
  QueryDocumentSnapshot<Object?>? lastvisibleDoc;
  @override
  void initState() {
    super.initState();
    getFirstActivitiesData();
  }

  getFirstActivitiesData() async {
    fetchingData = true;
    setState(() {});
    final allActivitiesData = await FBFireStore.activities
        .where('uId', isEqualTo: FBAuth.auth.currentUser?.uid)
        // .where('type', isNotEqualTo: ActivityType.clothesTallied)
        .orderBy('createdAt', descending: true)
        .limit(10)
        .get()
        .then((value) {
      if (value.docs.isEmpty) return <ActivitiesModel>[];
      lastvisibleDoc = value.docs.last;
      return value.docs.map((e) => ActivitiesModel.fromSnap(e)).toList();
    });
    // allActivitiesData.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    activities.clear();
    activities.addAll(allActivitiesData);
    fetchingData = false;
    setState(() {});
  }

  getOtherActivitiesData() async {
    if (lastvisibleDoc == null || reloading || allLoaded) return;
    reloading = true;
    setState(() {});
    await Future.delayed(const Duration(milliseconds: 1500));
    try {
      final otherActivityList = await FBFireStore.activities
          .where('uId', isEqualTo: FBAuth.auth.currentUser?.uid)
          // .where('type', isNotEqualTo: ActivityType.clothesTallied)
          .orderBy('createdAt', descending: true)
          .startAfterDocument(lastvisibleDoc!)
          .limit(10)
          .get()
          .then((event) {
        if (event.docs.isEmpty) {
          lastvisibleDoc = null;

          return <ActivitiesModel>[];
        }
        lastvisibleDoc = event.docs.last;
        if (event.docs.length < 10) allLoaded = true;
        return event.docs.map((e) => ActivitiesModel.fromSnap(e)).toList();
      });
      activities.addAll(otherActivityList);

      reloading = false;
      setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
      reloading = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    activities
        .removeWhere((element) => element.type == ActivityType.clothesTallied);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      scrollCtrl.addListener(() {
        if (scrollCtrl.position.maxScrollExtent == scrollCtrl.offset) {
          getOtherActivitiesData();
        }
      });
    });
    final size = MediaQuery.sizeOf(context);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shadowColor: Colors.white,
        titleSpacing: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
        // centerTitle: false,
        title: const Text(
          'Activities',
          style: TextStyle(
            color: Color(0xFF4F4F4F),
            // fontSize: 24,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
        /*  actions: [
          if (_.clothes.length < _.settings!.maxClothes)
            Padding(
              padding: const EdgeInsets.only(right: 20.0),
              child: ElevatedButton(
                style: const ButtonStyle(
                    // fixedSize:
                    //     MaterialStateProperty.all(const Size(130, 36)),
                    padding: MaterialStatePropertyAll(EdgeInsets.zero),
                    backgroundColor:
                        MaterialStatePropertyAll(Color(0xFF01B49E))),
                onPressed: () {
                  pickedImage = null;
                  setState(() {});
                  newClothes(context, categoryList, size, null);
                },
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8.0, horizontal: 20),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // const Icon(
                      //   Icons.,
                      //   color: Colors.white,
                      // ),
                      // Image.asset(
                      //   'assets/images/cloth.png',
                      //   color: Colors.white,
                      // ),
                      // const SizedBox(width: 7),
                      size.width <= 335
                          ? const Icon(
                              CupertinoIcons.cloud_upload,
                              color: Colors.white,
                            )
                          // Image.asset("assets/images/cloth.png")
                          : const Text(
                              'Upload',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 17,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ],
                  ),
                ),
              ),
            ),
        ],
       */
      ),
      body: Container(
        height: size.height,
        decoration: const BoxDecoration(
            image: DecorationImage(
                repeat: ImageRepeat.repeatY,
                opacity: .5,
                image: AssetImage('assets/images/Image.png'))),
        child: fetchingData
            ? const Center(child: CircularProgressIndicator(strokeWidth: 3.5))
            : activities.isEmpty
                ? const Center(child: Text("No Activities yet!"))
                : Stack(
                    children: [
                      SingleChildScrollView(
                        physics: const ClampingScrollPhysics(),
                        controller: scrollCtrl,
                        padding: const EdgeInsets.all(15),
                        child: Padding(
                          padding: EdgeInsets.only(
                              bottom: lastvisibleDoc == null ? 0 : 20.0),
                          child: ListView.separated(
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemCount: activities.length,
                            separatorBuilder: (context, index) {
                              return const SizedBox(height: 20);
                            },
                            itemBuilder: (context, index) {
                              final activity = activities[index];
                              return ActivityCard(activity: activity);
                              /*    ListTile(
                      shape: RoundedRectangleBorder(
                          side: const BorderSide(color: Colors.grey),
                          borderRadius: BorderRadius.circular(5)),
                      // leading: const Icon(CupertinoIcons.check_mark_circled),
                      title: Text(activity.title ?? ""),
                      subtitle: Text(activity.desc ?? ""),
                      contentPadding:
                          const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
                      trailing: const CupertinoListTileChevron(),
                                        ); */
                            },
                          ),
                        ),
                      ),
                      if (reloading)
                        const Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                    height: 28,
                                    width: 28,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 3.2,
                                    )),
                              ],
                            ),
                          ],
                        ),
                    ],
                  ),
      ),
    );
  }
}

class ActivityCard extends StatelessWidget {
  const ActivityCard({
    super.key,
    required this.activity,
  });

  final ActivitiesModel activity;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: activity.type == ActivityType.profileRequest
          ? null
          : () => goToPage(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(5), topRight: Radius.circular(5)),
                color: Colors.grey.shade300,
                border: Border.all(color: Colors.grey.shade300)),
            width: double.maxFinite,
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  activity.title ?? "",
                  style: const TextStyle(
                      letterSpacing: .7,
                      // color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.w500),
                ),
                if (activity.type != ActivityType.profileRequest)
                  const Icon(
                    CupertinoIcons.chevron_forward,
                    // color: Colors.black,
                    size: 20,
                  )
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            width: double.maxFinite,
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                    bottomRight: Radius.circular(5),
                    bottomLeft: Radius.circular(5)),
                border: Border(
                  left: BorderSide(color: Colors.grey.shade300),
                  right: BorderSide(color: Colors.grey.shade300),
                  bottom: BorderSide(color: Colors.grey.shade300),
                )),
            child: Text(
              activity.desc ?? "",
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  void goToPage(BuildContext context) {
    switch (activity.type) {
      case ActivityType.complain:
        context.push('${Routes.activityComplain}/${activity.typeDocId}');
        break;
      case ActivityType.clothesPicked:
        context.push('${Routes.activityPickupSchedule}/${activity.typeDocId}');
        break;
      case ActivityType.clothesTallied:
        context.push('${Routes.activityPickupSchedule}/${activity.typeDocId}');
        break;
      case ActivityType.clothesDelivered:
        context.push('${Routes.activityPickupSchedule}/${activity.typeDocId}');
        break;
      case ActivityType.balance:
        context.push('${Routes.activityPickupSchedule}/${activity.typeDocId}');
        break;
      default:
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/auth_ctrl.dart';
import 'package:laundry_resident/shared/router.dart';
import '../../shared/theme.dart';

class Login extends StatefulWidget {
  const Login({super.key, this.sctrl});
  final ScrollController? sctrl;
  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  bool onSubmitLoad = false;
  @override
  Widget build(BuildContext context) {
    // final size = MediaQuery.sizeOf(context);
    AuthCtrl authCtrl = Get.find<AuthCtrl>();
    return Container(
      width: double.maxFinite,
      decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(30), topRight: Radius.circular(30))),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 15, 20, 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Center(
            //   child: Container(
            //     width: 30,
            //     height: 5,
            //     decoration: BoxDecoration(
            //         color: Colors.grey,
            //         borderRadius: BorderRadius.circular(5)),
            //   ),
            // ),
            // const SizedBox(height: 25),
            const Text(
              'Email ID',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: authCtrl.emailctrl,
              cursorColor: Colors.black87,
              decoration: const InputDecoration(
                  focusedBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(color: Color(0xff01B49E), width: 2)),
                  // enabledBorder: OutlineInputBorder(
                  //     borderSide: BorderSide(color: themeColor)),
                  hintText: 'Enter Your Email',
                  border: OutlineInputBorder(borderSide: BorderSide())),
            ),
            const SizedBox(height: 10),
            const Text(
              'Password',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: authCtrl.passctrl,
              obscureText: true,
              cursorColor: Colors.black87,
              decoration: const InputDecoration(
                  focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: themeColor, width: 2)),
                  hintText: "Enter Your Password",
                  border: OutlineInputBorder()),
            ),
            const SizedBox(height: 25),
            onSubmitLoad
                ? const Center(
                    child: SizedBox(
                      height: 23,
                      width: 23,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        color: Color.fromARGB(255, 20, 189, 172),
                      ),
                    ),
                  )
                : Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                            onPressed: () async {
                              try {
                                if (authCtrl.emailctrl.text.isEmpty ||
                                    authCtrl.passctrl.text.isEmpty) {
                                  const snackBar = SnackBar(
                                      duration: Duration(milliseconds: 1500),
                                      content: Text(
                                          'Please fill all the required fields'));
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(snackBar);
                                  return;
                                }

                                setState(() {
                                  onSubmitLoad = true;
                                });
                                final res = await authCtrl.onLogin();
                                setState(() {
                                  onSubmitLoad = false;
                                });
                                if (!res) return;
                                if (context.mounted) {
                                  await Future.delayed(Duration.zero)
                                      .then((value) => context.go(homeRoute));
                                }
                                authCtrl.emailctrl.clear();
                                authCtrl.passctrl.clear();
                              } on Exception catch (e) {
                                debugPrint(e.toString());
                              }
                            },
                            style: ButtonStyle(
                                padding: const WidgetStatePropertyAll(
                                    EdgeInsets.symmetric(vertical: 15)),
                                shape: WidgetStateProperty.all(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(4))),
                                backgroundColor: const WidgetStatePropertyAll(
                                    Color.fromARGB(255, 20, 189, 172))),
                            child: const Text(
                              "Login",
                              style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                  fontSize: 16),
                            )),
                      ),
                    ],
                  ),
            const SizedBox(height: 10),
            if (!onSubmitLoad)
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                    onPressed: () {
                      authCtrl.emailctrl.clear();
                      authCtrl.passctrl.clear();
                      authCtrl.verifyPassCtrl.clear();
                      context.push(Routes.resetPw);
                    },
                    child: const Text(
                      'Forgot password?',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    )),
              ),
            const SizedBox(height: 20),
            /*   Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text("Don't have an account?"),
                          const SizedBox(
                            width: 5,
                          ),
                          ElevatedButton(
                              onPressed: () {
                                authCtrl.emailctrl.clear();
                                authCtrl.passctrl.clear();
                                authCtrl.verifyPassCtrl.clear();
                                context.go(Routes.signup);
                              },
                              style: ButtonStyle(
                                  shape: WidgetStateProperty.all(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(5))),
                                  backgroundColor:
                                      const WidgetStatePropertyAll(
                                          Color.fromARGB(255, 20, 189, 172))),
                              child: const Text(
                                "Sign Up",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500),
                              )),
                        ],
                      ) */
          ],
        ),
      ),
    );
/* 
    Stack(
      children: [
        Align(
          alignment: Alignment.topCenter,
          child: Column(
            // mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 120),
              SizedBox(
                // width: 194,
                height: 183,
                child: Image.asset(
                  'assets/images/logo.png',
                  fit: BoxFit.cover,
                ),
              ),
              // const SizedBox(height: 48),
              // const Text(
              //   'BOSS LAUNDRY SERVICE',
              //   textAlign: TextAlign.center,
              //   style: TextStyle(
              //     color: Color(0xFF333333),
              //     fontSize: 30,
              //     fontFamily: 'Poppins',
              //     fontWeight: FontWeight.w700,
              //     height: 0,
              //   ),
              // ),
            ],
          ),
        ),
        // const SizedBox(height: 20),
        DraggableScrollableSheet(
            maxChildSize: .7,
            initialChildSize: .45,
            minChildSize: .4,
            builder: (context, sCtrl) {
              return Container(
                width: double.maxFinite,
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30))),
                child: SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  controller: sCtrl,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(20, 15, 20, 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: Container(
                            width: 30,
                            height: 5,
                            decoration: BoxDecoration(
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(5)),
                          ),
                        ),
                        const SizedBox(height: 25),
                        const Text(
                          'Email ID',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                        const SizedBox(height: 10),
                        TextField(
                          controller: authCtrl.emailctrl,
                          cursorColor: Colors.black87,
                          decoration: const InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                      color: Color(0xff01B49E), width: 2)),
                              // enabledBorder: OutlineInputBorder(
                              //     borderSide: BorderSide(color: themeColor)),
                              hintText: 'Enter Your Email',
                              border:
                                  OutlineInputBorder(borderSide: BorderSide())),
                        ),
                        const SizedBox(height: 10),
                        const Text(
                          'Password',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                        const SizedBox(height: 10),
                        TextField(
                          controller: authCtrl.passctrl,
                          obscureText: true,
                          cursorColor: Colors.black87,
                          decoration: const InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: themeColor, width: 2)),
                              hintText: "Enter Your Password",
                              border: OutlineInputBorder()),
                        ),
                        const SizedBox(height: 25),
                        onSubmitLoad
                            ? const Center(
                                child: SizedBox(
                                  height: 23,
                                  width: 23,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 3,
                                    color: Color.fromARGB(255, 20, 189, 172),
                                  ),
                                ),
                              )
                            : Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton(
                                        onPressed: () async {
                                          try {
                                            if (authCtrl
                                                    .emailctrl.text.isEmpty ||
                                                authCtrl
                                                    .passctrl.text.isEmpty) {
                                              const snackBar = SnackBar(
                                                  duration: Duration(
                                                      milliseconds: 1500),
                                                  content: Text(
                                                      'Please fill all the required fields'));
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(snackBar);
                                              return;
                                            }

                                            setState(() {
                                              onSubmitLoad = true;
                                            });
                                            final res =
                                                await authCtrl.onLogin();
                                            setState(() {
                                              onSubmitLoad = false;
                                            });
                                            if (!res) return;
                                            if (context.mounted) {
                                              await Future.delayed(
                                                      Duration.zero)
                                                  .then((value) =>
                                                      context.go(homeRoute));
                                            }
                                            authCtrl.emailctrl.clear();
                                            authCtrl.passctrl.clear();
                                          } on Exception catch (e) {
                                            debugPrint(e.toString());
                                          }
                                        },
                                        style: ButtonStyle(
                                            padding:
                                                const WidgetStatePropertyAll(
                                                    EdgeInsets.symmetric(
                                                        vertical: 15)),
                                            shape: WidgetStateProperty.all(
                                                RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            4))),
                                            backgroundColor:
                                                const WidgetStatePropertyAll(
                                                    Color.fromARGB(
                                                        255, 20, 189, 172))),
                                        child: const Text(
                                          "Login",
                                          style: TextStyle(
                                              fontWeight: FontWeight.w500,
                                              color: Colors.white,
                                              fontSize: 16),
                                        )),
                                  ),
                                ],
                              ),
                        const SizedBox(height: 10),
                        if (!onSubmitLoad)
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                                onPressed: () {
                                  authCtrl.emailctrl.clear();
                                  authCtrl.passctrl.clear();
                                  authCtrl.verifyPassCtrl.clear();
                                  context.push(Routes.resetPw);
                                },
                                child: const Text(
                                  'Forgot password?',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black,
                                  ),
                                )),
                          ),
                        const SizedBox(height: 20),
                        /*   Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Text("Don't have an account?"),
                            const SizedBox(
                              width: 5,
                            ),
                            ElevatedButton(
                                onPressed: () {
                                  authCtrl.emailctrl.clear();
                                  authCtrl.passctrl.clear();
                                  authCtrl.verifyPassCtrl.clear();
                                  context.go(Routes.signup);
                                },
                                style: ButtonStyle(
                                    shape: WidgetStateProperty.all(
                                        RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(5))),
                                    backgroundColor:
                                        const WidgetStatePropertyAll(
                                            Color.fromARGB(255, 20, 189, 172))),
                                child: const Text(
                                  "Sign Up",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500),
                                )),
                          ],
                        ) */
                      ],
                    ),
                  ),
                ),
              );
            })
      ],
    );
   */
  }
}



// PREVIOUS LOGIN UI
/* class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  bool onSubmitLoad = false;
  @override
  Widget build(BuildContext context) {
    // final size = MediaQuery.sizeOf(context);
    AuthCtrl authCtrl = Get.find<AuthCtrl>();
    return Scaffold(
      backgroundColor: const Color(0xFF5EFFEF),
      body: Stack(
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: Column(
              // mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 120),

                SizedBox(
                  // width: 194,
                  height: 183,
                  child: Image.asset(
                    'assets/images/logo.png',
                    fit: BoxFit.cover,
                  ),
                ),
                // const SizedBox(height: 48),
                // const Text(
                //   'BOSS LAUNDRY SERVICE',
                //   textAlign: TextAlign.center,
                //   style: TextStyle(
                //     color: Color(0xFF333333),
                //     fontSize: 30,
                //     fontFamily: 'Poppins',
                //     fontWeight: FontWeight.w700,
                //     height: 0,
                //   ),
                // ),
              ],
            ),
          ),
          // const SizedBox(height: 20),
          DraggableScrollableSheet(
              maxChildSize: .7,
              initialChildSize: .45,
              minChildSize: .4,
              builder: (context, sCtrl) {
                return Container(
                  width: double.maxFinite,
                  decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30))),
                  child: SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    controller: sCtrl,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 15, 20, 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Center(
                            child: Container(
                              width: 30,
                              height: 5,
                              decoration: BoxDecoration(
                                  color: Colors.grey,
                                  borderRadius: BorderRadius.circular(5)),
                            ),
                          ),
                          const SizedBox(height: 25),
                          const Text(
                            'Email ID',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                          const SizedBox(height: 10),
                          TextField(
                            controller: authCtrl.emailctrl,
                            cursorColor: Colors.black87,
                            decoration: const InputDecoration(
                                focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: Color(0xff01B49E), width: 2)),
                                // enabledBorder: OutlineInputBorder(
                                //     borderSide: BorderSide(color: themeColor)),
                                hintText: 'Enter Your Email',
                                border: OutlineInputBorder(
                                    borderSide: BorderSide())),
                          ),
                          const SizedBox(height: 10),
                          const Text(
                            'Password',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                          const SizedBox(height: 10),
                          TextField(
                            controller: authCtrl.passctrl,
                            obscureText: true,
                            cursorColor: Colors.black87,
                            decoration: const InputDecoration(
                                focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: themeColor, width: 2)),
                                hintText: "Enter Your Password",
                                border: OutlineInputBorder()),
                          ),
                          const SizedBox(height: 25),
                          onSubmitLoad
                              ? const Center(
                                  child: SizedBox(
                                    height: 23,
                                    width: 23,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 3,
                                      color: Color.fromARGB(255, 20, 189, 172),
                                    ),
                                  ),
                                )
                              : Row(
                                  children: [
                                    Expanded(
                                      child: ElevatedButton(
                                          onPressed: () async {
                                            try {
                                              if (authCtrl
                                                      .emailctrl.text.isEmpty ||
                                                  authCtrl
                                                      .passctrl.text.isEmpty) {
                                                const snackBar = SnackBar(
                                                    duration: Duration(
                                                        milliseconds: 1500),
                                                    content: Text(
                                                        'Please fill all the required fields'));
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(snackBar);
                                                return;
                                              }

                                              setState(() {
                                                onSubmitLoad = true;
                                              });
                                              final res =
                                                  await authCtrl.onLogin();
                                              setState(() {
                                                onSubmitLoad = false;
                                              });
                                              if (!res) return;
                                              if (context.mounted) {
                                                await Future.delayed(
                                                        Duration.zero)
                                                    .then((value) =>
                                                        context.go(homeRoute));
                                              }
                                              authCtrl.emailctrl.clear();
                                              authCtrl.passctrl.clear();
                                            } on Exception catch (e) {
                                              debugPrint(e.toString());
                                            }
                                          },
                                          style: ButtonStyle(
                                              padding:
                                                  const WidgetStatePropertyAll(
                                                      EdgeInsets.symmetric(
                                                          vertical: 15)),
                                              shape: WidgetStateProperty.all(
                                                  RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              4))),
                                              backgroundColor:
                                                  const WidgetStatePropertyAll(
                                                      Color.fromARGB(
                                                          255, 20, 189, 172))),
                                          child: const Text(
                                            "Login",
                                            style: TextStyle(
                                                fontWeight: FontWeight.w500,
                                                color: Colors.white,
                                                fontSize: 16),
                                          )),
                                    ),
                                  ],
                                ),
                          const SizedBox(height: 10),
                          if (!onSubmitLoad)
                            Align(
                              alignment: Alignment.centerRight,
                              child: TextButton(
                                  onPressed: () {
                                    authCtrl.emailctrl.clear();
                                    authCtrl.passctrl.clear();
                                    authCtrl.verifyPassCtrl.clear();
                                    context.push(Routes.resetPw);
                                  },
                                  child: const Text(
                                    'Forgot password?',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black,
                                    ),
                                  )),
                            ),
                          const SizedBox(height: 20),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text("Don't have an account?"),
                              const SizedBox(
                                width: 5,
                              ),
                              ElevatedButton(
                                  onPressed: () {
                                    authCtrl.emailctrl.clear();
                                    authCtrl.passctrl.clear();
                                    authCtrl.verifyPassCtrl.clear();
                                    context.go(Routes.signup);
                                  },
                                  style: ButtonStyle(
                                      shape: WidgetStateProperty.all(
                                          RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(5))),
                                      backgroundColor:
                                          const WidgetStatePropertyAll(
                                              Color.fromARGB(
                                                  255, 20, 189, 172))),
                                  child: const Text(
                                    "Sign Up",
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w500),
                                  )),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                );
              })
        ],
      ),
    );
  }
}

 */



// OLD UI

/* import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/auth_ctrl.dart';
import 'package:laundry_resident/shared/router.dart';
import '../../shared/theme.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  bool onSubmitLoad = false;
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    AuthCtrl authCtrl = Get.find<AuthCtrl>();
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              height: size.height / 2.2,
              width: double.infinity,
              clipBehavior: Clip.antiAlias,
              decoration: const BoxDecoration(color: Color(0xFF5EFFEF)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                      width: 194,
                      height: 183,
                      child: Image.asset(
                        'assets/images/logo.png',
                        fit: BoxFit.cover,
                      )),
                  const SizedBox(
                    height: 48,
                  ),
                  const Text(
                    'BOSS LAUNDRY SERVICE',
                    style: TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 30,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w700,
                      height: 0,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Email ID',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    controller: authCtrl.emailctrl,
                    cursorColor: Colors.black87,
                    decoration: const InputDecoration(
                        focusedBorder: OutlineInputBorder(
                            borderSide:
                                BorderSide(color: Color(0xff01B49E), width: 2)),
                        // enabledBorder: OutlineInputBorder(
                        //     borderSide: BorderSide(color: themeColor)),
                        hintText: 'Enter Your Email',
                        border: OutlineInputBorder(borderSide: BorderSide())),
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'Password',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    controller: authCtrl.passctrl,
                    obscureText: true,
                    cursorColor: Colors.black87,
                    decoration: const InputDecoration(
                        focusedBorder: OutlineInputBorder(
                            borderSide:
                                BorderSide(color: themeColor, width: 2)),
                        hintText: "Enter Your Password",
                        border: OutlineInputBorder()),
                  ),
                  const SizedBox(height: 25),
                  onSubmitLoad
                      ? const Center(
                          child: SizedBox(
                            height: 23,
                            width: 23,
                            child: CircularProgressIndicator(
                              strokeWidth: 3,
                              color: Color.fromARGB(255, 20, 189, 172),
                            ),
                          ),
                        )
                      : Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                  onPressed: () async {
                                    try {
                                      if (authCtrl.emailctrl.text.isEmpty ||
                                          authCtrl.passctrl.text.isEmpty) {
                                        const snackBar = SnackBar(
                                            content: Text(
                                                'Please fill all the required fields'));
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(snackBar);
                                        return;
                                      }

                                      setState(() {
                                        onSubmitLoad = true;
                                      });
                                      final res = await authCtrl.onLogin();
                                      setState(() {
                                        onSubmitLoad = false;
                                      });
                                      if (!res) return;
                                      if (context.mounted) {
                                        await Future.delayed(Duration.zero)
                                            .then((value) =>
                                                context.go(homeRoute));
                                      }
                                      authCtrl.emailctrl.clear();
                                      authCtrl.passctrl.clear();
                                    } on Exception catch (e) {
                                      debugPrint(e.toString());
                                    }
                                  },
                                  style: ButtonStyle(
                                      padding: const WidgetStatePropertyAll(
                                          EdgeInsets.symmetric(vertical: 15)),
                                      shape: WidgetStateProperty.all(
                                          RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(4))),
                                      backgroundColor:
                                          const WidgetStatePropertyAll(
                                              Color.fromARGB(
                                                  255, 20, 189, 172))),
                                  child: const Text(
                                    "Login",
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 16),
                                  )),
                            ),
                          ],
                        ),
                  const SizedBox(height: 20),
                  if (!onSubmitLoad)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text("Don't have an account?"),
                        const SizedBox(
                          width: 5,
                        ),
                        ElevatedButton(
                            onPressed: () {
                              authCtrl.emailctrl.clear();
                              authCtrl.passctrl.clear();
                              authCtrl.verifyPassCtrl.clear();
                              context.go(Routes.signup);
                            },
                            style: ButtonStyle(
                                shape: WidgetStateProperty.all(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(5))),
                                backgroundColor: const WidgetStatePropertyAll(
                                    Color.fromARGB(255, 20, 189, 172))),
                            child: const Text(
                              "Sign Up",
                              style: TextStyle(color: Colors.white),
                            )),
                      ],
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
 */
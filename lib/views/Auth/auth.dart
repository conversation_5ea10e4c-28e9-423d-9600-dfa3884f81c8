import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:laundry_resident/controllers/auth_ctrl.dart';
import 'package:laundry_resident/views/Auth/signup.dart';
import 'package:url_launcher/url_launcher_string.dart';
import '../../shared/theme.dart';
import 'login.dart';

class AuthPage extends StatefulWidget {
  const AuthPage({super.key});

  @override
  State<AuthPage> createState() => _AuthPageState();
}

class _AuthPageState extends State<AuthPage> {
  bool onSubmitLoad = false;
  @override
  void initState() {
    super.initState();
    needHelp();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    // AuthCtrl authCtrl = Get.find<AuthCtrl>();
    return Scaffold(
      backgroundColor: const Color(0xFF5EFFEF),
      body: GetBuilder<AuthCtrl>(builder: (authCtrl) {
        return Stack(
          children: [
            Align(
              alignment: Alignment.topCenter,
              child: Column(
                // mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 120),
                  SizedBox(
                    // width: 194,
                    height: 183,
                    child: Image.asset(
                      'assets/images/logo.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ],
              ),
            ),
            if (authCtrl.settings != null &&
                authCtrl.settings?.tutorialLink != null)
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: Align(
                    alignment: Alignment.topRight,
                    child: ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 10),
                          elevation: 0,
                          backgroundColor:
                              const Color.fromARGB(220, 255, 255, 255),
                          // side: BorderSide(
                          //     color: const Color.fromARGB(255, 71, 71, 71)),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6)),
                        ),
                        label: Text(
                          "How to use ?",
                          style: GoogleFonts.livvic(
                            color: Colors.black,
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        onPressed: () {
                          launchUrlString(authCtrl.settings!.tutorialLink!,
                              mode: LaunchMode.externalApplication);
                        },
                        icon: const Icon(
                          CupertinoIcons.play_rectangle,
                          color: Colors.black,
                          // size: 30,
                        )),
                  ),
                ),
              ),
            DraggableScrollableSheet(
                maxChildSize: .85,
                initialChildSize: .55,
                minChildSize: .4,
                // expand: true,
                builder: (context, sCtrl) {
                  return SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    controller: sCtrl,
                    child: Container(
                      clipBehavior: Clip.antiAlias,
                      width: double.maxFinite,
                      decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(30),
                              topRight: Radius.circular(30))),
                      child: DefaultTabController(
                          length: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Container(
                                      // height: 70,
                                      decoration: const BoxDecoration(
                                          color: Colors.white,
                                          boxShadow: [
                                            BoxShadow(
                                                blurRadius: 10,
                                                color: Colors.black12,
                                                spreadRadius: 5,
                                                offset: Offset(0, 0)),
                                          ]),
                                      child: TabBar(
                                        overlayColor:
                                            const WidgetStatePropertyAll(
                                                Colors.transparent),
                                        tabAlignment: TabAlignment.start,
                                        // dragStartBehavior: DragStartBehavior.down,
                                        // dragStartBehavior: ,
                                        labelPadding:
                                            const EdgeInsets.symmetric(
                                                vertical: 3),
                                        unselectedLabelStyle: TextStyle(
                                          color: Colors.grey.shade600,
                                          fontSize: 15,
                                        ),
                                        labelStyle: const TextStyle(
                                          color: themeColor2,
                                          fontSize: 16.5,
                                        ),

                                        // labelColor: Colors.black,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        padding: EdgeInsets.zero,
                                        isScrollable: true,
                                        indicatorColor: themeColor,
                                        indicatorSize: TabBarIndicatorSize.tab,
                                        tabs: const [
                                          Tab(
                                            child: SizedBox(
                                              width: 130,
                                              child: Center(
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Text(
                                                      'Sign In',
                                                      style: TextStyle(
                                                          fontSize: 18),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                          Tab(
                                            child: SizedBox(
                                              width: 130,
                                              child: Center(
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Text(
                                                      'Sign Up',
                                                      style: TextStyle(
                                                          fontSize: 18),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Container(
                                height: size.height * .8,
                                color: Colors.white,
                                child: TabBarView(
                                  physics: const NeverScrollableScrollPhysics(),
                                  children: [
                                    Login(sctrl: sCtrl),
                                    SignUp(sctrl: sCtrl),
                                  ],
                                ),
                              ),
                            ],
                          )),
                    ),
                  );
                })
          ],
        );
      }),
    );
  }

  needHelp() async {
    await Future.delayed(const Duration(milliseconds: 1500));
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Welcome!',
                  style: GoogleFonts.mulish(
                      fontSize: 28,
                      color: const Color.fromARGB(255, 0, 154, 138)),
                ),
                const SizedBox(height: 20),
                Text(
                  'Quick guide to get started.',
                  maxLines: 2,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.mulish(
                    fontSize: 16.5,
                    color: const Color(0xff3E3E3E),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 20),

                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text(
                          "Skip",
                          style: TextStyle(
                              color: Color.fromARGB(255, 174, 174, 174)),
                        )),
                    const SizedBox(width: 8),
                    ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            elevation: 0,
                            padding: const EdgeInsets.symmetric(
                                vertical: 0, horizontal: 10),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4)),
                            backgroundColor:
                                const Color.fromARGB(255, 0, 154, 138),
                            surfaceTintColor:
                                const Color.fromARGB(255, 0, 154, 138),
                            foregroundColor: Colors.white),
                        onPressed: () {
                          launchUrlString(
                              Get.find<AuthCtrl>().settings!.tutorialLink!,
                              mode: LaunchMode.externalApplication);
                          Navigator.of(context).pop();
                        },
                        child: const Row(
                          children: [
                            Text("Watch Now"),
                            SizedBox(width: 4),
                            Icon(
                              CupertinoIcons.play_rectangle,
                              color: Colors.white,
                            )
                          ],
                        )),
                  ],
                ),

                // Text(
                //   'Boss Laundry Services',
                //   textAlign: TextAlign.center,
                //   style: GoogleFonts.mulish(fontSize: 25),
                // ),
              ],
            ),
          ),
        );
      },
    );
  }
}

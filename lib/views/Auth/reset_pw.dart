import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../controllers/auth_ctrl.dart';

class ResetPassword extends StatefulWidget {
  const ResetPassword({super.key});

  @override
  State<ResetPassword> createState() => _ResetPasswordState();
}

class _ResetPasswordState extends State<ResetPassword> {
  bool onSubmitLoad = false;

  @override
  Widget build(BuildContext context) {
    AuthCtrl authCtrl = Get.find<AuthCtrl>();
    return Scaffold(
      backgroundColor: const Color(0xFF5EFFEF).withOpacity(.7),
      body: Stack(
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: Column(
              // mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 120),
                <PERSON>zed<PERSON>ox(
                  // width: 194,
                  height: 183,
                  child: Image.asset(
                    'assets/images/logo.png',
                    fit: BoxFit.cover,
                  ),
                ),
              ],
            ),
          ),
          DraggableScrollableSheet(
            maxChildSize: .5,
            initialChildSize: .30,
            minChildSize: .2,
            builder: (context, scrollController) {
              return Container(
                padding: const EdgeInsets.fromLTRB(20, 35, 20, 20),
                width: double.maxFinite,
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30))),
                child: SingleChildScrollView(
                  child: Column(
                    // mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "Please write your email to send an email.",
                        style: TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 15.5),
                      ),
                      const SizedBox(height: 15),
                      TextField(
                        controller: authCtrl.emailctrl,
                        cursorColor: Colors.black87,
                        autofocus: true,
                        decoration: const InputDecoration(
                            focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                    color: Color(0xff01B49E), width: 2)),
                            // enabledBorder: OutlineInputBorder(
                            //     borderSide: BorderSide(color: themeColor)),
                            hintText: 'Enter Email',
                            border:
                                OutlineInputBorder(borderSide: BorderSide())),
                      ),
                      const SizedBox(height: 15),
                      Center(
                        child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5)),
                              elevation: 0,
                              backgroundColor:
                                  const Color.fromARGB(255, 20, 189, 172),
                              foregroundColor: Colors.white,
                            ),
                            onPressed: () async {
                              await authCtrl.resetPassword();
                              if (context.mounted) {
                                context.pop();
                                authCtrl.emailctrl.clear();
                              }
                            },
                            child: const Text(
                              "Send Email",
                              style: TextStyle(fontSize: 16),
                            )),
                      ),
                      Center(
                        child: TextButton(
                          style: TextButton.styleFrom(
                            elevation: 0,
                            foregroundColor: Colors.black,
                          ),
                          onPressed: () => context.pop(),
                          child: const Text(
                            "Go Back",
                            style: TextStyle(fontSize: 14),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

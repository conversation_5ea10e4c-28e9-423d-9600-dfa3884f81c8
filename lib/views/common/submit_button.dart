import 'package:flutter/material.dart';

class CommonSubmitButton extends StatelessWidget {
  const CommonSubmitButton({
    super.key,
    required this.text,
    this.fontSize,
    required this.onPressed,
    required this.bgColor,
    this.verPadding = 20,
  });
  final String text;
  final Function() onPressed;
  final Color bgColor;
  final double verPadding;
  final double? fontSize;
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ButtonStyle(
          padding: WidgetStatePropertyAll(
              EdgeInsets.symmetric(vertical: verPadding)),
          backgroundColor: WidgetStatePropertyAll(bgColor),
          elevation: const WidgetStatePropertyAll(0),
          shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
          )),
      onPressed: onPressed,
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: TextStyle(
          letterSpacing: 2,
          color: Colors.white,
          fontSize: fontSize ?? 20,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w600,
          height: 0,
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';

class AlertButton extends StatelessWidget {
  const AlertButton(
      {super.key,
      required this.title,
      required this.content,
      required this.onTapYes,
      required this.onTapNo});
  final String title;
  final String content;
  final Function() onTapYes;
  final Function() onTapNo;
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      actionsAlignment: MainAxisAlignment.end,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      // backgroundColor: Colors.white,
      // surfaceTintColor: Colors.white,
      title: Text(title),
      content: Text(
        content,
        style: const TextStyle(fontSize: 15),
      ),
      actions: [
        TextButton(
          onPressed: onTapYes,
          child: const Text(
            "Yes",
          ),
        ),
        TextButton(
          onPressed: onTapNo,
          child: const Text(
            "No",
          ),
        ),
      ],
    );
  }
}

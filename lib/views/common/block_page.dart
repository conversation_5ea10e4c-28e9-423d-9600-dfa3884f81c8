import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/shared/firebse.dart';
import '../../shared/router.dart';

class BlockPage extends StatelessWidget {
  const BlockPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Text(
          "We caught an error while login.",
          style: TextStyle(color: Colors.red, fontSize: 23),
        ),
        const Text(
          "Request you to login again. If problem arises again please contact to support team.",
          textAlign: TextAlign.center,
        ),
        ElevatedButton.icon(
          onPressed: () async {
            await FBAuth.auth.signOut();
            if (context.mounted) context.go(Routes.auth);
          },
          icon: const Icon(CupertinoIcons.power),
          label: const Text('Back to login'),
        ),
      ],
    );
  }
}

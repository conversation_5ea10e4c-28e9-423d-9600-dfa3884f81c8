import 'package:flutter/material.dart';

class InputFields extends StatelessWidget {
  const InputFields({
    super.key,
    required this.ctrl,
    required this.txt1,
    required this.txt2,
    // required this.enabled,
  });
  final TextEditingController ctrl;
  final String txt1;
  final String txt2;
  // final bool enabled;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          txt1,
          style: const TextStyle(
            color: Color.fromARGB(255, 0, 0, 0),
            fontSize: 16,
            height: 0,
            fontWeight: FontWeight.w500,
            fontFamily: 'Poppins',
          ),
        ),
        const SizedBox(height: 10),
        SizedBox(
          // width: 380,
          // height: 50,
          child: TextField(
            controller: ctrl,
            style: const TextStyle(color: Colors.black),
            decoration: InputDecoration(
                // enabled: enabled,
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 12),
                hintText: txt2,
                border:
                    OutlineInputBorder(borderRadius: BorderRadius.circular(5))),
          ),
        ),
      ],
    );
  }
}

// ignore_for_file: no_wildcard_variable_uses

import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/main.dart';
import 'package:laundry_resident/shared/firebse.dart';
import 'package:laundry_resident/shared/methods.dart';
import 'package:laundry_resident/shared/theme.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher_string.dart';
import '../../models/pickup_schedules.dart';
import '../../shared/router.dart';

bool secondTime = false;

class HomePage extends StatefulWidget {
  const HomePage({super.key, this.showDialog});
  final bool? showDialog;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool shown = false;
  @override
  void initState() {
    super.initState();
    setupFcm();
    // showAlertDialog();
  }

  // showAlertDialog() async {
  //   if (shown) return;
  //   if (widget.showDialog ?? false) {
  //     shown = true;
  //     await Future.delayed(const Duration(milliseconds: 500));
  //     if (mounted) {
  //       showDialog(
  //         context: context,
  //         builder: (context) {
  //           return AlertDialog(
  //             shape: RoundedRectangleBorder(
  //                 borderRadius: BorderRadius.circular(15)),
  //             title: const Center(child: Text('Thanks')),
  //             content: const Text(
  //                 'Thanks for adding your cloths. Keep selected clothes ready for pickup!'),
  //             actionsAlignment: MainAxisAlignment.center,
  //             actions: [
  //               Row(
  //                 children: [
  //                   Expanded(
  //                     child: ElevatedButton(
  //                         style: ElevatedButton.styleFrom(
  //                           elevation: 0,
  //                           backgroundColor: const Color(0xFF01B49E),
  //                           foregroundColor: Colors.white,
  //                         ),
  //                         onPressed: () {
  //                           Navigator.of(context).pop();
  //                         },
  //                         child: const Text('Okay')),
  //                   ),
  //                 ],
  //               )
  //             ],
  //           );
  //         },
  //       );
  //     }
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    const totaltxt = '120';
    final size = MediaQuery.sizeOf(context);
    return Scaffold(
      body: GetBuilder<HomeCtrl>(builder: (_) {
        return !_.loaded
            ? const Center(
                child: CircularProgressIndicator(
                strokeWidth: 3.5,
              ))
            : Container(
                height: size.height,
                decoration: const BoxDecoration(
                    image: DecorationImage(
                        repeat: ImageRepeat.repeatY,
                        opacity: .7,
                        image: AssetImage(
                          'assets/images/Image.png',
                        ))),
                child: _homeWid(
                  context,
                  size,
                  _.nextHostelPickUpDate?.convertToDDMMYY() ?? "-",
                  _.nextHostelDeliveryDate?.convertToDDMMYY() ?? "-",
                  totaltxt,
                  _.currentPickupSchedule?.clothes.length ?? 0,
                  _.lastPickupedSchedules,
                  _.settings?.tutorialLink,
                  _.settings?.note,
                  _.settings?.noteLink,
                ),
              );
      }),
    );
  }

  int activeStep = 0;

  SingleChildScrollView _homeWid(
    BuildContext context,
    Size size,
    String pickdatetxt,
    String deldatetxt,
    String totaltxt,
    int clothNumber,
    List<PickUpScheduleModel> lastPickupedSchedules,
    String? tutorialLink,
    String? note,
    String? noteLink,
  ) {
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Padding(
        padding: EdgeInsets.fromLTRB(
            20, MediaQuery.paddingOf(context).top + 8, 20, 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                InkWell(
                  hoverColor: Colors.transparent,
                  onLongPress: () async {
                    final packageInfo = await PackageInfo.fromPlatform();
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                        content: Text(
                            'Boss Laundry, Version: ${packageInfo.version}, Build Number: ${packageInfo.buildNumber}')));
                  },
                  child: Image.asset(
                    'assets/images/bosslogo.png',
                    height: 30,
                  ),
                ),
                Row(
                  children: [
                    if (tutorialLink != null)
                      IconButton(
                          onPressed: () {
                            launchUrlString(tutorialLink,
                                mode: LaunchMode.externalApplication);
                          },
                          icon: const Icon(
                            CupertinoIcons.play_rectangle,
                            // size: 30,
                          )),
                    IconButton(
                        onPressed: () => context.push(Routes.notification),
                        icon: const Icon(
                          Icons.notifications_none,
                          // size: 30,
                        )),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (note != null && note.isNotEmpty) ...[
              Container(
                width: double.maxFinite,
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7),
                  border: Border.all(color: Colors.red.shade400, width: .7),
                  color: const Color.fromARGB(162, 255, 235, 238),
                  // color: Colors.white24,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text.rich(
                      TextSpan(
                        children: [
                          const TextSpan(
                            text: '* Note: ',
                            style: TextStyle(
                                color: Colors.red, fontWeight: FontWeight.bold),
                          ),
                          TextSpan(
                            text: note,
                            style: const TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                    ),
                    if (noteLink != null && noteLink.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(left: 0.0),
                        child: InkWell(
                          onTap: () {
                            launchUrlString(noteLink,
                                mode: LaunchMode.externalApplication);
                          },
                          child: Container(
                            padding: const EdgeInsets.only(top: 4, bottom: 1),
                            // decoration: const BoxDecoration(
                            //   border: Border(
                            //     bottom: BorderSide(
                            //       color: Color.fromARGB(255, 84, 84, 84),
                            //       width: .8,
                            //     ),
                            //   ),
                            // ),
                            child: Text(
                              noteLink,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                decoration: TextDecoration.underline,
                                decorationColor: Color(0xff1A0DAB),

                                fontSize: 13,
                                color: Color(0xff1A0DAB),
                                // color: Color.fromARGB(255, 84, 84, 84),
                              ),
                            ),
                          ),
                        ),
                      ),
                    const SizedBox(height: 5),
                  ],
                ),
              ),
              const SizedBox(height: 20),
            ],
            InkWell(
              highlightColor: Colors.transparent,
              overlayColor: const WidgetStatePropertyAll(Colors.transparent),
              onTap: () {
                context.push(Routes.addclothes);
              },
              child: Container(
                constraints: const BoxConstraints(maxHeight: 151),
                decoration: BoxDecoration(
                    // color: const Color(0xFF829CF8),
                    boxShadow: [
                      BoxShadow(
                          color: Colors.grey.shade100,
                          blurRadius: 10,
                          spreadRadius: 3,
                          offset: const Offset(0, 0)),
                    ],
                    gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xffFFB8B7),
                          Color(0xffF8ADAC),
                        ]),
                    borderRadius: BorderRadius.circular(12)),
                child: Stack(children: [
                  Align(
                    alignment: Alignment.bottomRight,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 15, right: 20),
                      child: Container(
                        padding: const EdgeInsets.all(3),
                        decoration: const BoxDecoration(
                            color: Colors.white, shape: BoxShape.circle),
                        child: const Icon(
                          CupertinoIcons.chevron_forward,
                          color: Color(0xffBDBDBD),
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 20, top: 30, bottom: 30),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                            width: 80,
                            height: double.maxFinite,
                            child:
                                Image.asset('assets/images/washclothes.png')),
                        const SizedBox(width: 20),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Submit',
                              style: TextStyle(
                                fontSize: 20,
                                color: Color(0xff333333),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 3),
                            const Text(
                              '(Give clothes for washing)',
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xff333333),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 6),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // const Text(
                                //   'Next Pickup',
                                //   style: TextStyle(
                                //     fontSize: 17,
                                //     color: Color(0xff333333),
                                //     fontWeight: FontWeight.w500,
                                //   ),
                                // ),
                                Text(
                                  pickdatetxt,
                                  style: const TextStyle(
                                    fontSize: 17,
                                    color: Color(0xff333333),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  )
                ]),
              ),
            ),
            const SizedBox(height: 15),
            InkWell(
              highlightColor: Colors.transparent,
              overlayColor: const WidgetStatePropertyAll(Colors.transparent),
              onTap: () {
                context.push(Routes.clothcollect);
              },
              child: Container(
                constraints: const BoxConstraints(maxHeight: 151),
                decoration: BoxDecoration(
                    // color: const Color(0xFF829CF8),
                    boxShadow: [
                      BoxShadow(
                          color: Colors.grey.shade100,
                          blurRadius: 10,
                          spreadRadius: 3,
                          offset: const Offset(0, 0)),
                    ],
                    gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xffF3E776),
                          Color(0xffFFDF58),
                        ]),
                    borderRadius: BorderRadius.circular(12)),
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.bottomRight,
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 15, right: 20),
                        child: Container(
                          padding: const EdgeInsets.all(3),
                          decoration: const BoxDecoration(
                              color: Colors.white, shape: BoxShape.circle),
                          child: const Icon(
                            CupertinoIcons.chevron_forward,
                            color: Color(0xffBDBDBD),
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 20, top: 30, bottom: 30),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                              width: 80,
                              height: double.maxFinite,
                              child: Image.asset(
                                  'assets/images/deliveryclothes.png')),
                          const SizedBox(width: 20),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Collect',
                                style: TextStyle(
                                  fontSize: 20,
                                  color: Color(0xff333333),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 3),
                              const Text(
                                '(Collect washed clothes)',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xff333333),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 6),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // const Text(
                                  //   'Next Delivery',
                                  //   style: TextStyle(
                                  //     fontSize: 17,
                                  //     color: Color(0xff333333),
                                  //     fontWeight: FontWeight.w500,
                                  //   ),
                                  // ),
                                  Text(
                                    deldatetxt,
                                    style: const TextStyle(
                                      fontSize: 17,
                                      color: Color(0xff333333),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
            const SizedBox(height: 15),
            StaggeredGrid.count(
              crossAxisCount: 2,
              crossAxisSpacing: 15,
              mainAxisSpacing: 15,
              children: [
                HomePageBoxContainer(
                  colors: const [
                    Color(0xff9BF3D5),
                    Color(0xff83FCD3),
                  ],
                  image: 'assets/images/clothimage.png',
                  text: 'My Wardrobe',
                  onTap: () {
                    context.push(Routes.clothes);
                  },
                ),
                HomePageBoxContainer(
                  onTap: () {
                    context.push(Routes.activities);
                  },
                  colors: const [
                    Color(0xffC9CEFF),
                    Color(0xffA9B1FF),
                  ],
                  // image: 'assets/images/profile copy.png',
                  image: 'assets/images/activity-final (1).png',
                  text: 'Activity Log',
                ),
                HomePageBoxContainer(
                  onTap: () {
                    context.push(Routes.account);
                  },
                  colors: const [
                    Color(0xffC2D0FF),
                    Color(0xff6993FF),
                  ],
                  image: 'assets/images/profile copy.png',
                  text: 'Account',
                ),
                HomePageBoxContainer(
                  onTap: () {
                    context.push(Routes.complain);
                  },
                  colors: const [
                    Color(0xffF9BEFE),
                    Color(0xffF9BEFE),
                  ],
                  image: 'assets/images/helpsupport.png',
                  text: 'Support',
                ),
              ],
            ),
            if (lastPickupedSchedules.isNotEmpty) const SizedBox(height: 20),
            if (lastPickupedSchedules.isNotEmpty)
              const Text(
                "Track your laundry",
                style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 18,
                    color: Color(0xff333333)),
              ),
            // const SizedBox(height: 10),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: lastPickupedSchedules.length,
              itemBuilder: (context, index) {
                bool washed = getWashed(lastPickupedSchedules[index]);
                bool tallied = lastPickupedSchedules[index].talliedAt != null;
                bool delivered =
                    lastPickupedSchedules[index].deliveryTime != null;
                // final pickedUpCount = lastPickupedSchedules[index]
                //     .clothes
                //     .where((e) => e.pickedUp == true)
                //     .toList()
                //     .length;
                // final talliedCount = lastPickupedSchedules[index]
                //     .clothes
                //     .where((e) => e.pickedUp == true)
                //     .where((e) => e.tallied == true)
                //     .toList()
                //     .length;
                return Container(
                  width: double.maxFinite,
                  padding:
                      const EdgeInsets.symmetric(vertical: 20, horizontal: 15),
                  decoration: BoxDecoration(
                    color: const Color.fromARGB(217, 255, 255, 255),
                    borderRadius: BorderRadius.circular(10),
                    border:
                        Border.all(color: const Color(0xffE0E0E0), width: 2),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Order: ${lastPickupedSchedules[index].pickedAt?.toDate().goodDayDate()} ${lastPickupedSchedules[index].pickedAt?.toDate().goodTime()}",
                        // "Order: ${lastPickupedSchedules[index].pickedAt?.toDate().goodDayDate()}",
                        style: const TextStyle(
                          color: Color(0xff4F4F4F),
                          fontSize: 17,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Text(
                      //   tallied
                      //       ? "$talliedCount clothes washed"
                      //       : "$pickedUpCount clothes picked",
                      //   style: const TextStyle(
                      //     color: Color(0xff333333),
                      //     fontSize: 16,
                      //     fontWeight: FontWeight.w600,
                      //   ),
                      // ),
                      // const SizedBox(height: 14),
                      SizedBox(
                        height: 70,
                        width: double.maxFinite,
                        child: Row(
                          children: [
                            Container(
                              height: 45,
                              width: 45,
                              decoration: const BoxDecoration(
                                  color: themeColor, shape: BoxShape.circle),
                              child: const Tooltip(
                                triggerMode: TooltipTriggerMode.tap,
                                message: 'Picked Up',
                                child: Icon(CupertinoIcons.cube_box,
                                    color: Colors.white),
                              ),
                            ),
                            Expanded(
                              child: Container(
                                height: 7,
                                width: double.maxFinite,
                                decoration:
                                    const BoxDecoration(color: themeColor),
                              ),
                            ),
                            Container(
                              height: 45,
                              width: 45,
                              decoration: BoxDecoration(
                                  color: washed
                                      ? themeColor
                                      : const Color(0xffEBEBEB),
                                  shape: BoxShape.circle),
                              child: Tooltip(
                                triggerMode: TooltipTriggerMode.tap,
                                message: 'Washing',
                                child: Icon(CupertinoIcons.wind,
                                    color:
                                        washed ? Colors.white : Colors.black),
                              ),
                            ),
                            Expanded(
                              child: Container(
                                height: 7,
                                width: double.maxFinite,
                                decoration:
                                    const BoxDecoration(color: themeColor),
                              ),
                            ),
                            Container(
                              height: 45,
                              width: 45,
                              decoration: BoxDecoration(
                                  color: tallied
                                      ? themeColor
                                      : const Color(0xffEBEBEB),
                                  shape: BoxShape.circle),
                              child: Tooltip(
                                triggerMode: TooltipTriggerMode.tap,
                                message: 'Sorting',
                                child: Icon(CupertinoIcons.square_stack_3d_up,
                                    color:
                                        tallied ? Colors.white : Colors.black),
                              ),
                            ),
                            Expanded(
                              child: Container(
                                height: 7,
                                width: double.maxFinite,
                                decoration: BoxDecoration(
                                    color: tallied
                                        ? themeColor
                                        : const Color(0xffEBEBEB)),
                              ),
                            ),
                            Container(
                              height: 45,
                              width: 45,
                              decoration: BoxDecoration(
                                  color: delivered
                                      ? themeColor
                                      : const Color(0xffEBEBEB),
                                  shape: BoxShape.circle),
                              child: Tooltip(
                                triggerMode: TooltipTriggerMode.tap,
                                message: 'Delivered',
                                child: Icon(CupertinoIcons.checkmark_alt,
                                    color: delivered
                                        ? Colors.white
                                        : Colors.black),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return const SizedBox(height: 15);
              },
            ),
          ],
        ),
      ),
    );
  }

  bool getWashed(PickUpScheduleModel pickedUpSchedule) {
    final pickedDate = pickedUpSchedule.pickedAt!.toDate();
    final today = DateTime.now();
    final difference = pickedDate.difference(today).inDays;
    if (difference < 0 || pickedUpSchedule.talliedAt != null) {
      return true;
    } else {
      return false;
    }
  }

  setupFcm() async {
    try {
      final ctrl = Get.find<HomeCtrl>();
      //flutterLocalNotificationsPlugin.cancelAll();
      FirebaseMessaging messaging = FirebaseMessaging.instance;
      await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        RemoteNotification? notification = message.notification;

        try {
          if (Platform.isAndroid) {
            flutterLocalNotificationsPlugin.show(
              notification.hashCode,
              notification!.title,
              notification.body,
              const NotificationDetails(
                android: androidPlatformChannelSpecifics,
                iOS: iOSPlatformChannelSpecifics,
              ),
            );
          }
        } catch (e) {
          debugPrint(e.toString());
        }
      });
      await unSubScribeFunction();

      messaging.subscribeToTopic('global');
      List<String> topics = [];
      if (ctrl.residentModel != null && ctrl.hostelModel != null) {
        messaging.subscribeToTopic(ctrl.hostelModel!.docId.substring(0, 6));

        messaging.subscribeToTopic(
            '${ctrl.residentModel!.hostelDocId.substring(0, 6)}${ctrl.residentModel?.floor}');
        topics.add(ctrl.hostelModel!.docId.substring(0, 6));
        topics.add(
            '${ctrl.residentModel!.hostelDocId.substring(0, 6)}${ctrl.residentModel?.floor}');
        await FBFireStore.residents.doc(ctrl.residentModel?.docId).update({
          'topics': topics,
        });
      }

      // FlutterAppBadger.removeBadge();
      // App opened from Notification

      // RemoteMessage? initialMessage =
      //     await FirebaseMessaging.instance.getInitialMessage();

      // if (initialMessage != null) {
      //   _handleMessage(initialMessage);
      // }

      FirebaseMessaging.onMessageOpenedApp.listen(
        (event) {
          // Get.to(() => const NotiesPage());
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> unSubScribeFunction() async {
    if (secondTime) return;
    secondTime = true;

    final ctrl = Get.find<HomeCtrl>();
    final toUnsubscribeTopics = ctrl.topics;
    // final hostels = await FBFireStore.hostels
    //     .get()
    //     .then((value) => value.docs.map((e) => e.id).toList());
    // hostels.removeWhere((element) => element == ctrl.hostelModel?.docId);
    for (var topic in toUnsubscribeTopics) {
      Future.delayed(const Duration(milliseconds: 20));
      // final res = await FirebaseMessaging.instance.getToken();
      await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
    }
  }
/* 
  bool isBetween = isDateBetween(DateTime.parse(schedules[0]),
      DateTime.parse(schedules[1]), DateTime.parse(lastPickupDate));

  bool isBtwn = isDateBetween(DateTime.parse(schedules[0]),
      DateTime.parse(schedules[1]), DateTime.parse(selectedDate!));

  String getNextPickupdate() {
    if (schedules.contains(lastPickupDate)) {
      if (lastPickupDate == schedules[0]) {
        return schedules[1];
      } else {
        return schedules[0];
      }
    } else if (selectedDate != null &&
        DateTime.parse(lastPickupDate)
            .isBefore(DateTime.parse(selectedDate!))) {
      if (isBtwn) {
        return schedules[0];
      } else if (!isBtwn) {
        if (DateTime.parse(selectedDate!)
                .isAfter(DateTime.parse(schedules[1])) ||
            DateTime.parse(selectedDate!)
                .isAtSameMomentAs(DateTime.parse(schedules[1]))) {
          return schedules[1];
        } else {
          return selectedDate!;
        }
      }
    } else {
      if (isBetween) {
        return schedules[1];
      } else {
        return schedules[0];
      }
    }
    return "";
  } */
}

class HomePageBoxContainer extends StatelessWidget {
  const HomePageBoxContainer({
    super.key,
    required this.colors,
    required this.image,
    required this.text,
    this.onTap,
  });
  final List<Color> colors;
  final String image;
  final String text;
  final Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      highlightColor: Colors.transparent,
      overlayColor: const WidgetStatePropertyAll(Colors.transparent),
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 150),
        decoration: BoxDecoration(
            // color: const Color(0xFF829CF8),
            boxShadow: [
              BoxShadow(
                  color: Colors.grey.shade100,
                  blurRadius: 10,
                  spreadRadius: 3,
                  offset: const Offset(0, 0)),
            ],
            gradient: LinearGradient(
                end: Alignment.topLeft,
                begin: Alignment.bottomRight,
                colors: colors),
            borderRadius: BorderRadius.circular(12)),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(image),
              const SizedBox(height: 10),
              Text(
                text,
                style: const TextStyle(
                  fontSize: 17,
                  color: Color(0xff333333),
                  fontWeight: FontWeight.w600,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class HomePage2 extends StatefulWidget {
  const HomePage2({super.key});

  @override
  State<HomePage2> createState() => _HomePage2State();
}

class _HomePage2State extends State<HomePage2> {
  @override
  Widget build(BuildContext context) {
    const totaltxt = '120';
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<HomeCtrl>(builder: (_) {
      return !_.loaded
          ? const CircularProgressIndicator()
          : _homeWid(size, _.nextHostelPickUpDate?.goodDayDate() ?? "-",
              context, totaltxt, _.currentPickupSchedule?.clothes.length ?? 0);
    });
  }

  SingleChildScrollView _homeWid(Size size, String datetxt,
      BuildContext context, String totaltxt, int clothNumber) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Boss Laundry Service',
                  style: TextStyle(
                    color: const Color(0xFF4F4F4F),
                    fontSize: size.width > 300 ? 25 : 25,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
                IconButton(
                    onPressed: () => context.push(Routes.notification),
                    icon: const Icon(Icons.notifications_none)),
              ],
            ),
            const SizedBox(height: 20),
            InkWell(
              onTap: () {
                context.push(Routes.addclothes);
                // Navigator.push(
                //     context,
                //     MaterialPageRoute(
                //         builder: (context) => const AddClothes()));
              },
              child: Container(
                //  constraints:
                //       BoxConstraints(maxHeight: size.height > 800 ? 330 : 280),

                constraints: const BoxConstraints(maxHeight: 280),
                decoration: BoxDecoration(
                    color: const Color(0xFF829CF8),
                    borderRadius: BorderRadius.circular(30)),
                child: Stack(children: [
                  Align(
                    alignment: Alignment.bottomRight,
                    child: SizedBox(
                        height: 150,
                        // width: 152,
                        child: Image.asset('assets/images/bag1.png')),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Spacer(),
                        Text(
                          'Submit your clothes',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: size.width > 350 ? 30 : 25,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const Spacer(flex: 2),
                        Text(
                          'Last date for adding',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: size.width > 350 ? 18 : 15,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          datetxt,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: size.width > 350 ? 24 : 20,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(flex: 4),
                        Text(
                          'Add Clothes ($clothNumber)',
                          style: TextStyle(
                            //decoration: TextDecoration.underline,
                            color: Colors.white,
                            fontSize: size.width > 350 ? 20 : 18,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(
                          width: 115,
                          child: Divider(
                            color: Colors.white,
                          ),
                        )
                      ],
                    ),
                  )
                ]),
              ),
            ),
            const SizedBox(height: 30),
            InkWell(
              onTap: () {
                context.push(Routes.clothcollect);
              },
              child: Container(
                // constraints:
                //     BoxConstraints(maxHeight: size.height > 800 ? 330 : 280),
                constraints: const BoxConstraints(maxHeight: 280),
                decoration: BoxDecoration(
                    color: const Color(0xFF28D0C6),
                    borderRadius: BorderRadius.circular(30)),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    Align(
                      alignment: Alignment.bottomLeft,
                      child: size.width > 300
                          ? SizedBox(
                              height: 220,
                              // width: 152,
                              child: Image.asset('assets/images/bag2.png'))
                          : SizedBox(
                              height: 220,
                              width: 122,
                              child: Image.asset('assets/images/bag2.png')),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          const Spacer(),
                          SizedBox(
                            width: 304,
                            child: Text(
                              'Collect your room clothes',
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: size.width > 350 ? 32 : 27,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const Spacer(flex: 2),
                          Text(
                            'Total Clothes',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: size.width > 300 ? 18 : 15,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          // const Spacer(),
                          // Text(
                          //   totaltxt,
                          //   style: TextStyle(
                          //     color: Colors.white,
                          //     fontSize: size.width > 300 ? 24 : 20,
                          //     fontFamily: 'Poppins',
                          //     fontWeight: FontWeight.w600,
                          //   ),
                          // ),
                          const Spacer(flex: 4),
                          SizedBox(
                            // width: 140,
                            // height: 42,
                            child: ElevatedButton(
                                style: ButtonStyle(
                                    padding: const WidgetStatePropertyAll(
                                        EdgeInsets.symmetric(
                                            vertical: 18, horizontal: 20)),
                                    shape: WidgetStatePropertyAll(
                                        RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(5)))),
                                onPressed: () {
                                  context.push(Routes.clothcollect);
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'Collect',
                                      style: TextStyle(
                                        color: const Color(0xFF333333),
                                        fontSize: size.width > 300 ? 16 : 14,
                                        fontFamily: 'League Spartan',
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    const SizedBox(width: 7),
                                    // const Spacer(),
                                    const Icon(
                                      Icons.arrow_forward,
                                      color: Colors.black,
                                    )
                                  ],
                                )),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

/* 
  bool isBetween = isDateBetween(DateTime.parse(schedules[0]),
      DateTime.parse(schedules[1]), DateTime.parse(lastPickupDate));

  bool isBtwn = isDateBetween(DateTime.parse(schedules[0]),
      DateTime.parse(schedules[1]), DateTime.parse(selectedDate!));

  String getNextPickupdate() {
    if (schedules.contains(lastPickupDate)) {
      if (lastPickupDate == schedules[0]) {
        return schedules[1];
      } else {
        return schedules[0];
      }
    } else if (selectedDate != null &&
        DateTime.parse(lastPickupDate)
            .isBefore(DateTime.parse(selectedDate!))) {
      if (isBtwn) {
        return schedules[0];
      } else if (!isBtwn) {
        if (DateTime.parse(selectedDate!)
                .isAfter(DateTime.parse(schedules[1])) ||
            DateTime.parse(selectedDate!)
                .isAtSameMomentAs(DateTime.parse(schedules[1]))) {
          return schedules[1];
        } else {
          return selectedDate!;
        }
      }
    } else {
      if (isBetween) {
        return schedules[1];
      } else {
        return schedules[0];
      }
    }
    return "";
  } */
}

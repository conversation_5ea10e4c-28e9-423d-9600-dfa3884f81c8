// ignore_for_file: no_wildcard_variable_uses

import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/models/clothes.dart';
import 'package:laundry_resident/shared/firebse.dart';
import 'package:laundry_resident/shared/razor_payment.dart';
import 'package:laundry_resident/shared/router.dart';
import 'package:laundry_resident/views/common/alert_button.dart';
import '../../models/payment_order.dart';
import '../../models/pickup_schedules.dart';
import '../../services/image_picker.dart';
import '../../shared/methods.dart';
import '../../shared/theme.dart';

class AddClothes extends StatefulWidget {
  const AddClothes({super.key});

  @override
  State<AddClothes> createState() => _AddClothesState();
}

class _AddClothesState extends State<AddClothes> {
  bool adding = false;
  int totalValue = 0;
  List<String> selectedClothesIds = [];
  // List<String> pastSelectedClothIds = [];
  SelectedImage? pickedImage;
  final List<Clothes> newClothList = [];
  bool waitingForPayment = false;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? paymentOrderStream;
  PaymentOrderModel? paymentOrder;
  @override
  void initState() {
    super.initState();
    // getPaymentOrder(
    //     ctrl.currentPickupSchedule?.docId, ctrl.residentModel?.docId);
  }

  getPaymentOrder(String? pickedupScheduleDocId, String? userDocId) async {
    if (pickedupScheduleDocId == null) {
      paymentOrder = null;
      return;
    }
    paymentOrderStream?.cancel();
    paymentOrderStream = FBFireStore.paymentOrder
        .where('uId', isEqualTo: userDocId)
        .where('pickUpScheduleDocId', isEqualTo: pickedupScheduleDocId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .listen(
      (event) {
        paymentOrder = PaymentOrderModel.fromDocumentSnapshot(event.docs.first);
      },
    );
    waitingForPayment = paymentOrder?.processed ?? false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<HomeCtrl>(builder: (_) {
      final currentSchedule = _.currentPickupSchedule;
      currentSchedule?.clothes.forEach((element) {
        if (!selectedClothesIds.contains(element.docId)) {
          selectedClothesIds.add(element.docId);
        }
      });
      // getPaymentOrder(currentSchedule?.docId, _.residentModel?.docId);
      // totalValue = _.currentPickupSchedule?.totalValue ?? 0;
      totalValue = 0;

      for (var clothDocId in selectedClothesIds) {
        final cloth = _.clothes
            .firstWhereOrNull((element) => element.docId == clothDocId);
        if (cloth != null) {
          final clothCat = _.settings?.clothtypes.firstWhereOrNull((element) =>
              element.title.toLowerCase() == cloth.category.toLowerCase());
          // print("Value: $totalValue");
          if (clothCat != null) totalValue += clothCat.value;
        }
      }

      final clothlist = _.clothes;
      // final categoryList =
      //     _.settings?.clothtypes.map((e) => e.title).toList() ?? [];
      final List<String> lastpickedUpclothIds = [];
      final lastpickedUpcloths = _.lastPickupedSchedules
          .map((e) =>
              e.clothes.where((element) => element.pickedUp == true).toList())
          .toList();
      for (var eleme in lastpickedUpcloths) {
        for (var elem in eleme) {
          lastpickedUpclothIds.add(elem.docId);
        }
      }
      selectedClothesIds
          .removeWhere((element) => lastpickedUpclothIds.contains(element));
      newClothList.clear();
      newClothList.addAll(clothlist);
      newClothList.removeWhere(
          (element) => lastpickedUpclothIds.contains(element.docId));
      newClothList.removeWhere((ele) =>
          currentSchedule?.clothes
              .firstWhereOrNull((element) => element.docId == ele.docId) !=
          null);

      return waitingForPayment
          ? const Scaffold(
              body: Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Center()
                      SizedBox(
                          height: 45,
                          width: 45,
                          child: Center(
                              child:
                                  CircularProgressIndicator(strokeWidth: 3.5))),
                      SizedBox(height: 15),
                      Text(
                        'Waiting for Payment Confirmation',
                        style: TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ),
            )
          : Scaffold(
              appBar: AppBar(
                backgroundColor: Colors.white,
                surfaceTintColor: Colors.white,
                shadowColor: Colors.white,
                titleSpacing: 0,
                leading: IconButton(
                  onPressed: () => context.pop(),
                  icon: const Icon(Icons.arrow_back),
                ),
                title: const Text(
                  'Clothes for washing',
                  style: TextStyle(
                    color: Color(0xFF4F4F4F),
                    // fontSize: 24,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              bottomNavigationBar: currentSchedule != null &&
                      currentSchedule.clothes.isNotEmpty
                  ? Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            blurRadius: 5,
                            // blurStyle: BlurStyle.,
                            color: Colors.grey,
                            offset: Offset(0, .5),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20.0, vertical: 25),
                      child: ElevatedButton(
                        style: ButtonStyle(
                            // padding: const WidgetStatePropertyAll(
                            //     EdgeInsets.symmetric(vertical: 10)),
                            backgroundColor:
                                const WidgetStatePropertyAll(themeColor),
                            elevation: const WidgetStatePropertyAll(0),
                            foregroundColor:
                                const WidgetStatePropertyAll(Colors.white),
                            shape:
                                WidgetStatePropertyAll(RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(7),
                            ))),
                        onPressed: () async {
                          return newClothList.isNotEmpty
                              ? _addMoreDialog(context, clothlist,
                                  lastpickedUpclothIds, _, currentSchedule)
                              : showAppSnackBar('No more clothes to add');
                        },
                        child: const Padding(
                          padding: EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            "Add More",
                            style: TextStyle(fontSize: 18),
                          ),
                        ),
                      ),
                    )
                  : const SizedBox(),
              body: Container(
                padding: const EdgeInsets.all(20.0),
                height: size.height,
                decoration: const BoxDecoration(
                    image: DecorationImage(
                        repeat: ImageRepeat.repeatY,
                        opacity: .5,
                        image: AssetImage(
                          'assets/images/Image.png',
                        ))),
                child: clothlist.isEmpty
                    ? const Center(
                        child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "No Clothes are uploaded yet!",
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 12),
                          Text(
                            "1. Go Back",
                            style: TextStyle(fontSize: 18),
                          ),
                          Text(
                            "2. Go to My Wardrobe",
                            style: TextStyle(fontSize: 18),
                          ),
                          Text(
                            "3. Click on Upload to submit your Clothes",
                            style: TextStyle(fontSize: 18),
                          ),
                        ],
                      ))
                    : currentSchedule == null || currentSchedule.clothes.isEmpty
                        ? Center(
                            child: TextButton.icon(
                              style: ButtonStyle(
                                  // padding: const WidgetStatePropertyAll(
                                  //     EdgeInsets.symmetric(vertical: 10)),
                                  // backgroundColor:
                                  //     const WidgetStatePropertyAll(themeColor),
                                  elevation: const WidgetStatePropertyAll(0),
                                  // backgroundColor:
                                  //     const WidgetStatePropertyAll(Colors.white),
                                  foregroundColor:
                                      const WidgetStatePropertyAll(themeColor),
                                  shape: WidgetStatePropertyAll(
                                      RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(7),
                                  ))),
                              onPressed: () async {
                                return _addMoreDialog(context, clothlist,
                                    lastpickedUpclothIds, _, currentSchedule);
                              },
                              icon: const Icon(CupertinoIcons.add),
                              label: const Padding(
                                padding: EdgeInsets.symmetric(vertical: 8.0),
                                child: Text(
                                  "Select Clothes",
                                  style: TextStyle(fontSize: 18),
                                ),
                              ),
                            ),
                          )
                        : SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (currentSchedule.clothes.isNotEmpty)
                                  Container(
                                    decoration: ShapeDecoration(
                                      color: const Color(0x1901B49E),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 15.0, horizontal: 20),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        size.width > 300
                                            ? Row(
                                                children: [
                                                  Text(
                                                    'Bucket',
                                                    style: TextStyle(
                                                      color:
                                                          const Color.fromARGB(
                                                              255, 0, 0, 0),
                                                      fontSize: size.width > 335
                                                          ? 18
                                                          : 15,
                                                      fontFamily: 'Poppins',
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                  Text(
                                                    ' (Clothes selected for pickup)',
                                                    style: TextStyle(
                                                      color: const Color(
                                                          0xFF828282),
                                                      fontSize: size.width > 335
                                                          ? 15
                                                          : 12,
                                                      fontFamily: 'Poppins',
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                  )
                                                ],
                                              )
                                            : Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Bucket',
                                                    style: TextStyle(
                                                      color: const Color(
                                                          0xFF333333),
                                                      fontSize: size.width > 335
                                                          ? 18
                                                          : 15,
                                                      fontFamily: 'Poppins',
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                  Text(
                                                    '(Clothes selected for pickup)',
                                                    style: TextStyle(
                                                      color: const Color(
                                                          0xFF828282),
                                                      fontSize: size.width > 335
                                                          ? 15
                                                          : 12,
                                                      fontFamily: 'Poppins',
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                        const SizedBox(height: 10),
                                        Txt(
                                          txt: 'Total Clothes: ',
                                          txt2: currentSchedule.clothes.length
                                              .toString(),
                                          // txt2: pickupclothesnum.toString(),
                                        ),
                                        const SizedBox(height: 10),
                                        Txt(
                                            txt: 'Total Value: ',
                                            txt2: currentSchedule.totalValue
                                                .toString()),
                                        const SizedBox(height: 10),
                                      ],
                                    ),
                                  ),

                                const SizedBox(height: 10),
                                // if (currentSchedule.clothes.isNotEmpty)
                                //   Padding(
                                //     padding: const EdgeInsets.symmetric(
                                //         horizontal: 5.0),
                                //     child: Row(
                                //       children: [
                                //         Text.rich(TextSpan(children: [
                                //           const TextSpan(
                                //               text: 'Todays free value limit: ',
                                //               style: TextStyle(
                                //                 fontSize: 15,
                                //                 fontWeight: FontWeight.w500,
                                //               )),
                                //           TextSpan(
                                //               text: (_.nextSchedule!
                                //                           .maxClothValue ??
                                //                       _.settings!
                                //                           .maxClothesPerDay)
                                //                   .toString(),
                                //               style: const TextStyle(
                                //                   fontSize: 18,
                                //                   fontWeight: FontWeight.w600,
                                //                   color: Color(0xFF01B49E)))
                                //         ])),
                                //         const Spacer(),
                                //         Text.rich(TextSpan(children: [
                                //           const TextSpan(
                                //               text: 'Extra added value: ',
                                //               style: TextStyle(
                                //                 fontSize: 15,
                                //                 fontWeight: FontWeight.w500,
                                //               )),
                                //           TextSpan(
                                //               text: (currentSchedule
                                //                           .totalValue) >
                                //                       (_.nextSchedule!
                                //                               .maxClothValue ??
                                //                           _.settings!
                                //                               .maxClothesPerDay)
                                //                   ? ((currentSchedule
                                //                               .totalValue) -
                                //                           (_.nextSchedule!
                                //                                   .maxClothValue ??
                                //                               _.settings!
                                //                                   .maxClothesPerDay))
                                //                       .toString()
                                //                   : '0',
                                //               style: const TextStyle(
                                //                   fontSize: 18,
                                //                   fontWeight: FontWeight.w600,
                                //                   color: Colors.redAccent)),
                                //         ])),
                                //       ],
                                //     ),
                                //   ),
                                // const SizedBox(height: 20),

                                if (currentSchedule.clothes.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 10.0),
                                    child: StaggeredGrid.extent(
                                        maxCrossAxisExtent: 200,
                                        mainAxisSpacing: 10,
                                        crossAxisSpacing: 10,
                                        children: [
                                          ...List.generate(
                                              currentSchedule.clothes.length,
                                              (index) {
                                            // final selCloth = _.clothes.firstWhereOrNull(
                                            //     (element) =>
                                            //         element.docId ==
                                            //         selectedClothesIds[index]);
                                            return Stack(
                                              children: [
                                                Container(
                                                    clipBehavior:
                                                        Clip.antiAlias,
                                                    decoration: ShapeDecoration(
                                                      color: const Color(
                                                          0xE0E0E0E0),
                                                      // color: Colors.white,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        // side: const BorderSide(
                                                        //     width: 1, color: Color(0xFFE0E0E0)),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10),
                                                      ),
                                                    ),
                                                    height: 150,
                                                    width: double.maxFinite,
                                                    child:
                                                        // currentSchedule
                                                        //             .clothes[index]
                                                        //             .photoUrl ==
                                                        //         null
                                                        //     ? const Icon(Icons.error)
                                                        //     :
                                                        CachedNetworkImage(
                                                      imageUrl: currentSchedule
                                                          .clothes[index]
                                                          .photoUrl,
                                                      // selCloth!.photoUrl,
                                                      fit: BoxFit.cover,
                                                      width: double.infinity,
                                                    )),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 8.0, top: 8),
                                                  child: Align(
                                                    alignment:
                                                        Alignment.topRight,
                                                    child: InkWell(
                                                      onTap: () {
                                                        showDialog(
                                                          context: context,
                                                          builder: (context) {
                                                            return AlertButton(
                                                              title: "Remove",
                                                              content:
                                                                  "Are you sure you want to remove?",
                                                              onTapYes:
                                                                  () async {
                                                                try {
                                                                  List<ClothPickupModel>
                                                                      newClothsList =
                                                                      [];
                                                                  newClothsList.addAll(
                                                                      currentSchedule
                                                                          .clothes);
                                                                  final newTotalValue = currentSchedule
                                                                          .totalValue -
                                                                      currentSchedule
                                                                          .clothes[
                                                                              index]
                                                                          .categoryValue;
                                                                  newClothsList.removeWhere((element) =>
                                                                      element
                                                                          .docId ==
                                                                      currentSchedule
                                                                          .clothes[
                                                                              index]
                                                                          .docId);
                                                                  selectedClothesIds.removeWhere((element) =>
                                                                      element ==
                                                                      currentSchedule
                                                                          .clothes[
                                                                              index]
                                                                          .docId);
                                                                  await FBFireStore
                                                                      .pickUpSchedules
                                                                      .doc(currentSchedule
                                                                          .docId)
                                                                      .update({
                                                                    "clothes": newClothsList
                                                                        .map((e) =>
                                                                            e.toJson()),
                                                                    "totalValue":
                                                                        newTotalValue,
                                                                    "totalCloths":
                                                                        newClothsList
                                                                            .length,
                                                                  });
                                                                  _.update();
                                                                } catch (e) {
                                                                  debugPrint(e
                                                                      .toString());
                                                                }
                                                                if (context
                                                                    .mounted) {
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                }
                                                              },
                                                              onTapNo: () =>
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop(),
                                                            );
                                                          },
                                                        );
                                                      },
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(1.2),
                                                        decoration:
                                                            const BoxDecoration(
                                                          color: Colors.white,
                                                          boxShadow: [
                                                            BoxShadow(
                                                                blurRadius: 7,
                                                                spreadRadius:
                                                                    .2,
                                                                blurStyle:
                                                                    BlurStyle
                                                                        .outer,
                                                                color:
                                                                    Colors.grey,
                                                                offset: Offset(
                                                                    .1, 1))
                                                          ],
                                                          shape:
                                                              BoxShape.circle,
                                                        ),
                                                        child: const Icon(
                                                            CupertinoIcons
                                                                .xmark,
                                                            color:
                                                                Colors.black54,
                                                            size: 20),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            );
                                          })
                                        ]),
                                  ),

                                // Shhet

                                /* StaggeredGrid.extent(
                                        // maxCrossAxisExtent: 150,
                                        maxCrossAxisExtent: 200,
                                        mainAxisSpacing: 10,
                                        crossAxisSpacing: 10,
                                        children: [
                                          ...List.generate(clothlist.length, (index) {
                      final bool ischeck =
                          selectedClothesIds.contains(clothlist[index].docId);
                      bool alreadyPicked =
                          lastpickedUpclothIds.contains(clothlist[index].docId);
                      bool inBucket = _.currentPickupSchedule?.clothes
                              .firstWhereOrNull((element) =>
                                  element.docId == clothlist[index].docId) !=
                          null;
                      return Opacity(
                        opacity: alreadyPicked ? 0.45 : 1,
                        child: Stack(children: [
                          InkWell(
                            onTap: alreadyPicked
                                ? null
                                : () {
                                    // final val = _.settings?.clothtypes
                                    //         .firstWhereOrNull((element) =>
                                    //             element.title ==
                                    //             clothlist[index].category)
                                    //         ?.value ??
                                    //     1;
                                    if (inBucket) {
                                      showAppSnackBar(
                                          "Please, remove from buket first!");
                                      return;
                                    }
                                    setState(() {
                                      if (ischeck == false) {
                                        // print(selectedClothesIds.length);
                                        selectedClothesIds
                                            .add(clothlist[index].docId);
                                        // totalValue += val;
                                      } else {
                                        // print(selectedClothesIds.length);
                                        // totalValue -= val;
                                        selectedClothesIds.removeWhere(
                                            (element) =>
                                                element ==
                                                clothlist[index].docId);
                                      }
                                    });
                                  },
                            child: Container(
                              clipBehavior: Clip.antiAlias,
                              decoration: ShapeDecoration(
                                color: const Color(0xE0E0E0E0),
                                shape: RoundedRectangleBorder(
                                  // side: const BorderSide(
                                  //     width: 1, color: Color(0xFFE0E0E0)),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              height: 150,
                              child: CachedNetworkImage(
                                imageUrl: clothlist[index].photoUrl,
                                fit: BoxFit.cover,
                                width: double.maxFinite,
                              ),
                            ),
                          ),
                          Checkbox(
                            side:
                                // const BorderSide(color: Color(0xFF01B49E)),
                                const BorderSide(color: Colors.black),
                            checkColor: Colors.white,
                            fillColor: alreadyPicked
                                ? const WidgetStatePropertyAll(Colors.white)
                                : inBucket
                                    ? WidgetStatePropertyAll(
                                        Colors.grey.shade700)
                                    : WidgetStatePropertyAll(ischeck
                                        ? const Color(0xFF01B49E)
                                        : Colors.white),
                            value: alreadyPicked ? false : ischeck,
                            onChanged: alreadyPicked
                                ? (value) {}
                                : (newValue) {
                                    /*   print(newValue);
                                      final selCloth = _.clothes.firstWhereOrNull(
                                          (element) =>
                                              element.docId == clothlist[index].docId);
                                      final selClothCategory = selCloth!.category;
                      
                                      final clothType = _.settings?.clothtypes
                                          .firstWhereOrNull((element) =>
                                              element.title == selClothCategory);
                      
                                      final selClothValue = clothType?.value ?? 0; */
                      
                                    // final val = _.settings?.clothtypes
                                    //         .firstWhereOrNull((element) =>
                                    //             element.title ==
                                    //             clothlist[index].category)
                                    //         ?.value ??
                                    //     1;
                                    if (inBucket) {
                                      showAppSnackBar(
                                          "Please, remove from buket first!");
                                      return;
                                    }
                      
                                    setState(() {
                                      if (newValue == true) {
                                        // print(selectedClothesIds.length);
                                        selectedClothesIds
                                            .add(clothlist[index].docId);
                                        // totalValue += val;
                                      } else {
                                        // print(selectedClothesIds.length);
                      
                                        // totalValue -= val;
                                        selectedClothesIds.removeWhere(
                                            (element) =>
                                                element ==
                                                clothlist[index].docId);
                                      }
                                    });
                                  },
                          )
                        ]),
                      );
                                          })
                                        ],
                                      ),
                                       */
                                // SizedBox(height: size.height < 800 ? 20 : 20 + h),
                                // const SizedBox(height: 30),
                              ],
                            ),
                          ),
              ),
/*         bottomNavigationBar: selectedClothesIds.isNotEmpty &&
                _.nextSchedule != null &&
                _.nextSchedule?.maxClothValue != null
            ? Container(
                decoration: const BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      blurRadius: 10,
                      blurStyle: BlurStyle.outer,
                      color: Colors.grey,
                      offset: Offset(0, 1),
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 20.0, vertical: 15),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text.rich(TextSpan(children: [
                            const TextSpan(
                                text: 'Total Cloth: ',
                                style: TextStyle(fontSize: 16)),
                            TextSpan(
                                text: selectedClothesIds.length.toString(),
                                style: const TextStyle(
                                    fontSize: 18, fontWeight: FontWeight.w600)),
                          ])),
                          Text.rich(TextSpan(children: [
                            const TextSpan(
                                text: 'Total Value: ',
                                style: TextStyle(fontSize: 16)),
                            TextSpan(
                                text: totalValue.toString(),
                                style: const TextStyle(
                                    fontSize: 18, fontWeight: FontWeight.w600)),
                          ])),
                        ],
                      ),
                      const SizedBox(height: 3),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                  fixedSize: const Size.fromHeight(50),
                                  backgroundColor: totalValue >
                                          _.nextSchedule!.maxClothValue!
                                      ? Colors.red
                                      : const Color(0xFF01B49E),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5))),
                              onPressed: totalValue >
                                      _.nextSchedule!.maxClothValue!
                                  ? () {}
                                  : () async {
                                      bool listsAreEqual = false;
                                      if (currentSchedule != null &&
                                          selectedClothesIds.length ==
                                              currentSchedule.clothes.length) {
                                        for (var element
                                            in currentSchedule.clothes) {
                                          if (selectedClothesIds
                                              .contains(element.docId)) {
                                            listsAreEqual = true;
                                          } else {
                                            listsAreEqual = false;
                                          }
                                        }
                                      }
                                      if (listsAreEqual) return;

                                      /*         if (currentSchedule != null &&
                                        currentSchedule.clothes.isNotEmpty) {
                                      FBFireStore.pickUpSchedules
                                          .doc(currentSchedule.docId)
                                          .update({
                                        'clothes': [],
                                      });
                                    } */

                                      final list = _.clothes
                                          .where((element) => selectedClothesIds
                                              .contains(element.docId))
                                          .toList();
                                      setState(() {
                                        adding = true;
                                      });
                                      try {
                                        await FBFireStore.fb.runTransaction(
                                            (transaction) async {
                                          int clothesValue = 0;
                                          for (var oldCloth in list) {
                                            final val = _.settings?.clothtypes
                                                    .firstWhereOrNull(
                                                        (element) =>
                                                            element.title ==
                                                            oldCloth.category)
                                                    ?.value ??
                                                1;
                                            clothesValue += val;
                                          }
                                          final lastPickupSchedule =
                                              _.lastPickupedSchedules.isNotEmpty
                                                  ? _.lastPickupedSchedules
                                                      .first.pickedAt
                                                      ?.toDate()
                                                  : null;
                                          final data = <String, dynamic>{
                                            "uId": _.residentModel!.docId,
                                            "hostelDocId":
                                                _.residentModel!.hostelDocId,
                                            "wing": _.residentModel!.wing,
                                            "floor": _.residentModel!.floor,
                                            "roomNo": _.residentModel!.roomNo,
                                            "bedNo": _.residentModel!.bedNo,
                                            "residentName":
                                                _.residentModel!.name,
                                            "bunchDocId": null,
                                            "totalCloths": list.length,
                                            "totalValue": clothesValue,
                                            "paidForCloth": null,
                                            "paidForValue": null,
                                            "paidAmount": null,
                                            "paymentTime": null,
                                            "lastUpdate": null,
                                            "perValueAmount":
                                                _.settings!.chargesPerValue,
                                            "missingCount": null,
                                            "missingValue": null,
                                            "complaintDocId": null,
                                            "complaintSolved": false,
                                            "createdAt": Timestamp.now(),
                                            "pickedUpCount": null,
                                            "pickedUpValue": null,
                                            "pickedUp": false,
                                            "delivered": false,
                                            "isPaid": false,
                                            "lastPickupScheduleDate":
                                                lastPickupSchedule == null
                                                    ? null
                                                    : DateTime(
                                                        lastPickupSchedule.year,
                                                        lastPickupSchedule
                                                            .month,
                                                        lastPickupSchedule.day),
                                            "pickedAt": null,
                                            "deliveryTime": null,
                                            "needApproval": false,
                                            "collectedByName": null,
                                            "collectedByDocId": null,
                                            "pickedUpByDocId": null,
                                            "pickedUpByName": null,
                                            "enableApprove": false,
                                            "deliveredByDocId": null,
                                            "deliveredByName": null,
                                            "talliedByName": null,
                                            "talliedByDocId": null,
                                            "talliedAt": null,
                                            "clothes": list.map((e) => {
                                                  "docId": e.docId,
                                                  "photoUrl": e.photoUrl,
                                                  "categoryValue": _
                                                          .settings?.clothtypes
                                                          .firstWhereOrNull(
                                                              (element) =>
                                                                  element
                                                                      .title ==
                                                                  e.category)
                                                          ?.value ??
                                                      1,
                                                  "categoryName": e.category,
                                                  "tallied": null,
                                                  "pickedUp": false,
                                                  "collected": null
                                                })
                                          };
                                          currentSchedule != null
                                              ? transaction.update(
                                                  FBFireStore.pickUpSchedules
                                                      .doc(currentSchedule
                                                          .docId),
                                                  data)
                                              : transaction.set(
                                                  FBFireStore.pickUpSchedules
                                                      .doc(),
                                                  data);
                                        });
                                        if (context.mounted) {
                                          showAppSnackBar('Bucket Updated');
                                          context.go(homeRoute);
                                        }
                                      } catch (e) {
                                        debugPrint(e.toString());
                                      }
                                      setState(() {
                                        adding = false;
                                      });
                                      setState(() {
                                        // selectedClothesIds.clear();
                                        totalValue = 0;
                                      });
                                    },
                              child: adding
                                  ? const SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 3,
                                      ),
                                    )
                                  : Text(
                                      totalValue >
                                              _.nextSchedule!.maxClothValue!
                                          ? "Pay and add"
                                          : currentSchedule != null
                                              ? currentSchedule.clothes.isEmpty
                                                  ? 'Add to bucket (${selectedClothesIds.length.toString()})'
                                                  : 'Update bucket (${selectedClothesIds.length.toString()})'
                                              : 'Add to bucket (${selectedClothesIds.length.toString()})',
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              )
            : const SizedBox(), */
            );
    });
  }

  Future<void> _addMoreDialog(
      BuildContext context,
      List<Clothes> clothlist,
      List<String> lastpickedUpclothIds,
      HomeCtrl _,
      PickUpScheduleModel? currentSchedule) {
    selectedClothesIds.clear();
    final List<String> tmpList = [];
    tmpList.clear();
    return showModalBottomSheet(
      // backgroundColor: Colors.grey[100],
      showDragHandle: false,
      backgroundColor: Colors.white,
      useRootNavigator: true,
      isScrollControlled: true,
      context: context,
      enableDrag: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          final perValueCharge = _.settings!.chargesPerValue;

          final balanceAmountUserHave = (_.residentModel?.balance ?? 0);
          final normallyAcceptedClothes =
              (_.nextSchedule?.maxClothValue ?? _.settings!.maxClothesPerDay);
          num acceptedValueforPickup = normallyAcceptedClothes +
              (balanceAmountUserHave / perValueCharge).floor();
          return DraggableScrollableSheet(
            maxChildSize: .8,
            initialChildSize: .75,
            minChildSize: .2,
            expand: false,
            builder: (context, scrollController) {
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30))),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          "Select Clothes",
                          style: TextStyle(
                              fontSize: 20, fontWeight: FontWeight.w600),
                        ),
                        _.nextSchedule != null &&
                                _.nextSchedule?.maxClothValue != null &&
                                tmpList.isNotEmpty
                            ? ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    // fixedSize: const Size.fromHeight(0),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 30),
                                    backgroundColor:
                                        totalValue > acceptedValueforPickup
                                            ? Colors.red
                                            : const Color(0xFF01B49E),
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(5))),
                                onPressed: () async {
                                  bool listsAreEqual = false;
                                  if (currentSchedule != null &&
                                      selectedClothesIds.length ==
                                          currentSchedule.clothes.length) {
                                    for (var element
                                        in currentSchedule.clothes) {
                                      if (selectedClothesIds
                                          .contains(element.docId)) {
                                        listsAreEqual = true;
                                      } else {
                                        listsAreEqual = false;
                                      }
                                    }
                                  }

                                  if (listsAreEqual) return;

                                  final pickedClothList = _.clothes
                                      .where((element) => selectedClothesIds
                                          .contains(element.docId))
                                      .toList();
                                  final String pickUpScheduleDocId =
                                      currentSchedule != null
                                          ? currentSchedule.docId
                                          : FBFireStore.pickUpSchedules
                                              .doc()
                                              .id;
                                  setState2(() {
                                    adding = true;
                                  });

                                  try {
                                    await FBFireStore.fb
                                        .runTransaction((transaction) async {
                                      int clothesValue = 0;
                                      for (var oldCloth in pickedClothList) {
                                        final val = _.settings?.clothtypes
                                                .firstWhereOrNull((element) =>
                                                    element.title ==
                                                    oldCloth.category)
                                                ?.value ??
                                            1;
                                        clothesValue += val;
                                      }

                                      final lastPickupSchedule =
                                          _.lastPickupedSchedules.isNotEmpty
                                              ? _.lastPickupedSchedules.first
                                                  .pickedAt
                                                  ?.toDate()
                                              : null;
                                      final data = <String, dynamic>{
                                        "uId": _.residentModel!.docId,
                                        "hostelDocId":
                                            _.residentModel!.hostelDocId,
                                        "wing": _.residentModel!.wing,
                                        "floor": _.residentModel!.floor,
                                        "roomNo": _.residentModel!.roomNo,
                                        "bedNo": _.residentModel!.bedNo,
                                        "residentName": _.residentModel!.name,
                                        "bunchDocId": null,
                                        "totalCloths": pickedClothList.length,
                                        "totalValue": clothesValue,
                                        "paidForCloth": 0,
                                        "paidForValue": 0,
                                        "paidAmount": null,
                                        "paymentTime": null,
                                        "lastUpdate": null,
                                        "perValueAmount": perValueCharge,
                                        "missingCount": null,
                                        "missingValue": null,
                                        "complaintDocId": null,
                                        "complaintSolved": false,
                                        "createdAt": Timestamp.now(),
                                        "pickedUpCount": null,
                                        "pickedUpValue": null,
                                        "pickedUp": false,
                                        "delivered": false,
                                        "isPaid": false,
                                        "lastPickupScheduleDate":
                                            lastPickupSchedule == null
                                                ? null
                                                : DateTime(
                                                    lastPickupSchedule.year,
                                                    lastPickupSchedule.month,
                                                    lastPickupSchedule.day),
                                        "pickedAt": null,
                                        "deliveryTime": null,
                                        "needApproval": false,
                                        "collectedByName": null,
                                        "collectedByDocId": null,
                                        "pickedUpByDocId": null,
                                        "pickedUpByName": null,
                                        "enableApprove": false,
                                        "deliveredByDocId": null,
                                        "deliveredByName": null,
                                        "talliedByName": null,
                                        "talliedByDocId": null,
                                        "talliedAt": null,
                                        "foundClothes": null,
                                        "clothes": pickedClothList.map((e) => {
                                              "docId": e.docId,
                                              "photoUrl": e.photoUrl,
                                              "categoryValue": _
                                                      .settings?.clothtypes
                                                      .firstWhereOrNull(
                                                          (element) =>
                                                              element.title ==
                                                              e.category)
                                                      ?.value ??
                                                  1,
                                              "categoryName": e.category,
                                              "tallied": null,
                                              "pickedUp": false,
                                              "collected": null
                                            })
                                      };
                                      if (totalValue <=
                                          acceptedValueforPickup) {
                                        currentSchedule != null
                                            ? transaction.update(
                                                FBFireStore.pickUpSchedules
                                                    .doc(pickUpScheduleDocId),
                                                data)
                                            : transaction.set(
                                                FBFireStore.pickUpSchedules
                                                    .doc(pickUpScheduleDocId),
                                                data);
                                      } else if (totalValue >
                                          acceptedValueforPickup) {
                                        final toPayAmount = ((totalValue -
                                                acceptedValueforPickup) *
                                            perValueCharge);

                                        final paymentOrderData =
                                            <String, dynamic>{
                                          'uId': FBAuth.auth.currentUser?.uid,
                                          'pickUpScheduleDocId':
                                              pickUpScheduleDocId,
                                          'createdAt': Timestamp.now(),
                                          'processed': false,
                                          'amount': toPayAmount,
                                          'clothes':
                                              pickedClothList.map((e) => {
                                                    "docId": e.docId,
                                                    "photoUrl": e.photoUrl,
                                                    "categoryValue": _.settings
                                                            ?.clothtypes
                                                            .firstWhereOrNull(
                                                                (element) =>
                                                                    element
                                                                        .title ==
                                                                    e.category)
                                                            ?.value ??
                                                        1,
                                                    "categoryName": e.category,
                                                    "tallied": null,
                                                    "pickedUp": false,
                                                    "collected": null
                                                  }),
                                          'chargesPerValue': perValueCharge,
                                          'paidForCloth': (totalValue -
                                              acceptedValueforPickup),
                                          'paidForValue': (totalValue -
                                              acceptedValueforPickup),
                                          'hostelDocId':
                                              _.residentModel?.hostelDocId,
                                          'hostelName': _.hostelModel?.name,
                                          'lastPickupScheduleDate':
                                              lastPickupSchedule == null
                                                  ? null
                                                  : '${lastPickupSchedule.year}-${lastPickupSchedule.month}-${lastPickupSchedule.day}',
                                        };
                                        final payOrderRef = await FBFireStore
                                            .paymentOrder
                                            .add(paymentOrderData);
                                        // waitingForPayment = true;
                                        setState(() {});
                                        final pg = PaymentGatewayClass();
                                        try {
                                          await pg.createRazorpayOrder(
                                            pickupScheduleDocId:
                                                pickUpScheduleDocId,
                                            receiptNo: payOrderRef.id,
                                            amount: toPayAmount.toDouble(),
                                          );
                                        } on Exception catch (e) {
                                          debugPrint(e.toString());
                                          setState2(() {
                                            adding = false;
                                          });
                                          return;
                                        }
                                      } else {
                                        return;
                                      }
                                    });
                                    if (context.mounted) {
                                      context.go(homeRoute, extra: true);
                                    }
                                    /*  
                                   if (context.mounted) {
                                      final paymentSuccess =
                                          RazorPayPaymentClass().paymentSuccess;
                                      if (totalValue > acceptedValueforPickup &&
                                          paymentSuccess!) {
                                        context.pop();
                                        showAppSnackBar('Feature Coming Soon');
                                      } else {
                                        context.go(homeRoute, extra: true);
                                        showAppSnackBar(
                                            'Thanks for adding your cloths. Keep selected clothes ready for pickup!',
                                            duration:
                                                const Duration(seconds: 5));
                                      }
                                    }
                                     */
                                  } catch (e) {
                                    debugPrint(e.toString());
                                  }
                                  adding = false;
                                  totalValue = 0;
                                  setState2(() {});
                                },
                                child: adding
                                    ? const SizedBox(
                                        width: 25,
                                        height: 25,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 3,
                                        ),
                                      )
                                    : Text(
                                        totalValue > acceptedValueforPickup
                                            ? "Pay ₹${((totalValue - acceptedValueforPickup) * perValueCharge)}"
                                            : "Add (${tmpList.length.toString()})",
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontFamily: 'Poppins',
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                              )
                            : const SizedBox(),
                      ],
                    ),

                    if (_.nextSchedule != null &&
                        _.nextSchedule?.maxClothValue != null &&
                        tmpList.isNotEmpty &&
                        totalValue > acceptedValueforPickup) ...[
                      const SizedBox(height: 3),
                      Text.rich(
                        TextSpan(children: [
                          TextSpan(
                              text: 'Extra Cloth Value ',
                              style: GoogleFonts.livvic(fontSize: 12)),
                          TextSpan(
                              text: '(${totalValue - normallyAcceptedClothes})',
                              style: const TextStyle(fontSize: 14)),
                          TextSpan(
                              text: ' x ',
                              style: GoogleFonts.livvic(fontSize: 14)),
                          TextSpan(
                              text: 'Price per value ',
                              style: GoogleFonts.livvic(fontSize: 12)),
                          TextSpan(
                              text: '(₹$perValueCharge)',
                              style: const TextStyle(fontSize: 14)),
                          TextSpan(
                              text: ' = ',
                              style: GoogleFonts.livvic(fontSize: 14)),
                          TextSpan(
                              text:
                                  '₹${(totalValue - normallyAcceptedClothes) * perValueCharge}',
                              style: const TextStyle(fontSize: 14)),
                          TextSpan(
                              text: ' - ',
                              style: GoogleFonts.livvic(fontSize: 14)),
                          TextSpan(
                              text: '₹$balanceAmountUserHave ',
                              style: const TextStyle(fontSize: 14)),
                          TextSpan(
                              text: '(Balance)',
                              style: GoogleFonts.livvic(fontSize: 12)),
                        ]),
                      ),
                      // Text(
                      //     "Extra Cloth (${totalValue - normallyAcceptedClothes}) X Price per value (₹$perValueCharge) = ${(totalValue - normallyAcceptedClothes) * perValueCharge} - Balance $balanceAmountUserHave"),
                    ],
                    const SizedBox(height: 10),
                    const Divider(
                      height: 0,
                      thickness: 2,
                    ),
                    // const SizedBox(height: 9),
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.only(top: 12),
                        controller: scrollController,
                        physics: const ClampingScrollPhysics(),
                        child: Column(
                          children: [
                            StaggeredGrid.extent(
                              // maxCrossAxisExtent: 150,
                              maxCrossAxisExtent: 150,
                              mainAxisSpacing: 10,
                              crossAxisSpacing: 10,
                              children: [
                                ...List.generate(newClothList.length, (index) {
                                  final bool ischeck = selectedClothesIds
                                      .contains(newClothList[index].docId);

                                  return Stack(
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          setState2(
                                            () {
                                              if (ischeck == false) {
                                                selectedClothesIds.add(
                                                    newClothList[index].docId);
                                                tmpList.add(
                                                    newClothList[index].docId);
                                                setState(() {});
                                              } else {
                                                selectedClothesIds.removeWhere(
                                                    (element) =>
                                                        element ==
                                                        newClothList[index]
                                                            .docId);
                                                tmpList.removeWhere((element) =>
                                                    element ==
                                                    newClothList[index].docId);
                                                setState(() {});
                                              }
                                            },
                                          );
                                        },
                                        child: Container(
                                          clipBehavior: Clip.antiAlias,
                                          decoration: ShapeDecoration(
                                            color: const Color(0xE0E0E0E0),
                                            shape: RoundedRectangleBorder(
                                              // side: const BorderSide(
                                              //     width: 1, color: Color(0xFFE0E0E0)),
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                          ),
                                          height: 130,
                                          child: CachedNetworkImage(
                                            imageUrl:
                                                newClothList[index].photoUrl,
                                            fit: BoxFit.cover,
                                            width: double.maxFinite,
                                          ),
                                        ),
                                      ),
                                      Checkbox(
                                        side:
                                            // const BorderSide(color: Color(0xFF01B49E)),
                                            const BorderSide(
                                                color: Colors.black),
                                        checkColor: Colors.white,
                                        fillColor: WidgetStatePropertyAll(
                                            ischeck
                                                ? const Color(0xFF01B49E)
                                                : Colors.white),
                                        value: ischeck,
                                        onChanged: (newValue) {
                                          setState2(() {
                                            if (newValue == true) {
                                              // print(selectedClothesIds.length);
                                              selectedClothesIds.add(
                                                  newClothList[index].docId);
                                              tmpList.add(
                                                  newClothList[index].docId);
                                              setState(() {});
                                            } else {
                                              selectedClothesIds.removeWhere(
                                                  (element) =>
                                                      element ==
                                                      newClothList[index]
                                                          .docId);
                                              tmpList.removeWhere((element) =>
                                                  element ==
                                                  newClothList[index].docId);
                                              setState(() {});
                                            }
                                          });
                                        },
                                      )
                                    ],
                                  );
                                })
                              ],
                            ),
                            /*  const SizedBox(height: 10),
                            selectedClothesIds.isNotEmpty &&
                                    _.nextSchedule != null &&
                                    _.nextSchedule?.maxClothValue != null
                                ? Padding(
                                    padding: const EdgeInsets.symmetric(
                                        // horizontal: 20.0,
                                        vertical: 15),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text.rich(TextSpan(children: [
                                              const TextSpan(
                                                  text: 'Total Cloth: ',
                                                  style:
                                                      TextStyle(fontSize: 16)),
                                              TextSpan(
                                                  text: selectedClothesIds
                                                      .length
                                                      .toString(),
                                                  style: const TextStyle(
                                                      fontSize: 18,
                                                      fontWeight:
                                                          FontWeight.w600)),
                                            ])),
                                            Text.rich(TextSpan(children: [
                                              const TextSpan(
                                                  text: 'Total Value: ',
                                                  style:
                                                      TextStyle(fontSize: 16)),
                                              TextSpan(
                                                  text: totalValue.toString(),
                                                  style: const TextStyle(
                                                      fontSize: 18,
                                                      fontWeight:
                                                          FontWeight.w600)),
                                            ])),
                                          ],
                                        ),
                                        const SizedBox(height: 3),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: ElevatedButton(
                                                style: ElevatedButton.styleFrom(
                                                    fixedSize:
                                                        const Size.fromHeight(
                                                            50),
                                                    backgroundColor: totalValue >
                                                            _.nextSchedule!
                                                                .maxClothValue!
                                                        ? Colors.red
                                                        : const Color(
                                                            0xFF01B49E),
                                                    shape:
                                                        RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        5))),
                                                onPressed: totalValue >
                                                        _.nextSchedule!
                                                            .maxClothValue!
                                                    ? () {}
                                                    : () async {
                                                        bool listsAreEqual =
                                                            false;
                                                        if (currentSchedule !=
                                                                null &&
                                                            selectedClothesIds
                                                                    .length ==
                                                                currentSchedule
                                                                    .clothes
                                                                    .length) {
                                                          for (var element
                                                              in currentSchedule
                                                                  .clothes) {
                                                            if (selectedClothesIds
                                                                .contains(element
                                                                    .docId)) {
                                                              listsAreEqual =
                                                                  true;
                                                            } else {
                                                              listsAreEqual =
                                                                  false;
                                                            }
                                                          }
                                                        }
                                                        if (listsAreEqual)
                                                          return;
                                            
                                                        /*         if (currentSchedule != null &&
                                                currentSchedule.clothes.isNotEmpty) {
                                              FBFireStore.pickUpSchedules
                                                  .doc(currentSchedule.docId)
                                                  .update({
                                                'clothes': [],
                                              });
                                            } */
                                            
                                                        final list = _.clothes
                                                            .where((element) =>
                                                                selectedClothesIds
                                                                    .contains(
                                                                        element
                                                                            .docId))
                                                            .toList();
                                                        setState2(() {
                                                          adding = true;
                                                        });
                                                        try {
                                                          await FBFireStore.fb
                                                              .runTransaction(
                                                                  (transaction) async {
                                                            int clothesValue =
                                                                0;
                                                            for (var oldCloth
                                                                in list) {
                                                              final val = _
                                                                      .settings
                                                                      ?.clothtypes
                                                                      .firstWhereOrNull((element) =>
                                                                          element
                                                                              .title ==
                                                                          oldCloth
                                                                              .category)
                                                                      ?.value ??
                                                                  1;
                                                              clothesValue +=
                                                                  val;
                                                            }
                                                            final lastPickupSchedule = _
                                                                    .lastPickupedSchedules
                                                                    .isNotEmpty
                                                                ? _
                                                                    .lastPickupedSchedules
                                                                    .first
                                                                    .pickedAt
                                                                    ?.toDate()
                                                                : null;
                                                            final data =
                                                                <String,
                                                                    dynamic>{
                                                              "uId": _
                                                                  .residentModel!
                                                                  .docId,
                                                              "hostelDocId": _
                                                                  .residentModel!
                                                                  .hostelDocId,
                                                              "wing": _
                                                                  .residentModel!
                                                                  .wing,
                                                              "floor": _
                                                                  .residentModel!
                                                                  .floor,
                                                              "roomNo": _
                                                                  .residentModel!
                                                                  .roomNo,
                                                              "bedNo": _
                                                                  .residentModel!
                                                                  .bedNo,
                                                              "residentName": _
                                                                  .residentModel!
                                                                  .name,
                                                              "bunchDocId":
                                                                  null,
                                                              "totalCloths":
                                                                  list.length,
                                                              "totalValue":
                                                                  clothesValue,
                                                              "paidForCloth":
                                                                  null,
                                                              "paidForValue":
                                                                  null,
                                                              "paidAmount":
                                                                  null,
                                                              "paymentTime":
                                                                  null,
                                                              "lastUpdate":
                                                                  null,
                                                              "perValueAmount": _
                                                                  .settings!
                                                                  .chargesPerValue,
                                                              "missingCount":
                                                                  null,
                                                              "missingValue":
                                                                  null,
                                                              "complaintDocId":
                                                                  null,
                                                              "complaintSolved":
                                                                  false,
                                                              "createdAt":
                                                                  Timestamp
                                                                      .now(),
                                                              "pickedUpCount":
                                                                  null,
                                                              "pickedUpValue":
                                                                  null,
                                                              "pickedUp": false,
                                                              "delivered":
                                                                  false,
                                                              "isPaid": false,
                                                              "lastPickupScheduleDate": lastPickupSchedule ==
                                                                      null
                                                                  ? null
                                                                  : DateTime(
                                                                      lastPickupSchedule
                                                                          .year,
                                                                      lastPickupSchedule
                                                                          .month,
                                                                      lastPickupSchedule
                                                                          .day),
                                                              "pickedAt": null,
                                                              "deliveryTime":
                                                                  null,
                                                              "needApproval":
                                                                  false,
                                                              "collectedByName":
                                                                  null,
                                                              "collectedByDocId":
                                                                  null,
                                                              "pickedUpByDocId":
                                                                  null,
                                                              "pickedUpByName":
                                                                  null,
                                                              "enableApprove":
                                                                  false,
                                                              "deliveredByDocId":
                                                                  null,
                                                              "deliveredByName":
                                                                  null,
                                                              "talliedByName":
                                                                  null,
                                                              "talliedByDocId":
                                                                  null,
                                                              "talliedAt": null,
                                                              "clothes":
                                                                  list
                                                                      .map(
                                                                          (e) =>
                                                                              {
                                                                                "docId": e.docId,
                                                                                "photoUrl": e.photoUrl,
                                                                                "categoryValue": _.settings?.clothtypes.firstWhereOrNull((element) => element.title == e.category)?.value ?? 1,
                                                                                "categoryName": e.category,
                                                                                "tallied": null,
                                                                                "pickedUp": false,
                                                                                "collected": null
                                                                              })
                                                            };
                                                            currentSchedule !=
                                                                    null
                                                                ? transaction.update(
                                                                    FBFireStore
                                                                        .pickUpSchedules
                                                                        .doc(currentSchedule
                                                                            .docId),
                                                                    data)
                                                                : transaction.set(
                                                                    FBFireStore
                                                                        .pickUpSchedules
                                                                        .doc(),
                                                                    data);
                                                          });
                                                          if (context.mounted) {
                                                            showAppSnackBar(
                                                                'Bucket Updated');
                                                            context
                                                                .go(homeRoute);
                                                          }
                                                        } catch (e) {
                                                          debugPrint(
                                                              e.toString());
                                                        }
                                                        setState2(() {
                                                          adding = false;
                                                        });
                                                        setState2(() {
                                                          // selectedClothesIds.clear();
                                                          totalValue = 0;
                                                        });
                                                      },
                                                child: adding
                                                    ? const SizedBox(
                                                        width: 25,
                                                        height: 25,
                                                        child:
                                                            CircularProgressIndicator(
                                                          color: Colors.white,
                                                          strokeWidth: 3,
                                                        ),
                                                      )
                                                    : Text(
                                                        totalValue >
                                                                _.nextSchedule!
                                                                    .maxClothValue!
                                                            ? "Pay and add"
                                                            : currentSchedule !=
                                                                    null
                                                                ? currentSchedule
                                                                        .clothes
                                                                        .isEmpty
                                                                    ? 'Add to bucket (${selectedClothesIds.length.toString()})'
                                                                    : 'Update bucket (${selectedClothesIds.length.toString()})'
                                                                : 'Add to bucket (${selectedClothesIds.length.toString()})',
                                                        textAlign:
                                                            TextAlign.center,
                                                        style: const TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 16,
                                                          fontFamily: 'Poppins',
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  )
                                : const SizedBox(), */
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        });
      },
    );
  }

/* 
  Future<dynamic> newClothes(
      BuildContext context, List<String> namelist, size, String? selectedType) {
    // getData();
    bool loading2 = false;
    return showDialog(
        context: context,
        builder: (BuildContext context) =>
            StatefulBuilder(builder: (context, setState2) {
              return AlertDialog(
                title: const Text('Add New Cloth',
                    style: TextStyle(
                      color: Color(0xFF4F4F4F),
                      fontSize: 20,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                    )),
                content: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () async {
                        showModalBottomSheet(
                          constraints: const BoxConstraints(maxHeight: 100),
                          context: context,
                          builder: (context) {
                            return Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Expanded(
                                  child: InkWell(
                                    onTap: () async {
                                      Navigator.of(context).pop();
                                      await imagePicker();
                                      setState2(() {});
                                    },
                                    child: const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: Center(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              CupertinoIcons.photo_on_rectangle,
                                              size: 30,
                                              color: Color.fromARGB(
                                                  255, 236, 198, 6),
                                            ),
                                            Text(
                                              'Gallery',
                                              style: TextStyle(fontSize: 15),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                // const VerticalDivider(),
                                Expanded(
                                  child: InkWell(
                                    onTap: () async {
                                      Navigator.of(context).pop();
                                      await getImageFromCamera();
                                      setState2(() {});
                                    },
                                    child: const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: Center(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              CupertinoIcons.camera,
                                              size: 30,
                                              color: Color.fromARGB(
                                                  255, 28, 99, 223),
                                            ),
                                            Text(
                                              'Camera',
                                              style: TextStyle(fontSize: 15),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        );
                      },
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: const Color(0xFF828282)
                                        // color: const Color.fromARGB(
                                        //     255, 185, 183, 183),
                                        ),
                                borderRadius: BorderRadius.circular(5),
                              ),
                              height: 200,
                              child: pickedImage != null
                                  ? AspectRatio(
                                      aspectRatio: 1,
                                      child: Image.memory(
                                        pickedImage!.uInt8List,
                                        fit: BoxFit.cover,
                                        width: double.maxFinite,
                                        height: 160,
                                      ),
                                    )
                                  : IntrinsicHeight(
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: InkWell(
                                              onTap: () async {
                                                await getImageFromCamera();
                                                setState2(() {});
                                              },
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    CupertinoIcons.camera,
                                                    color: Color(0xFF828282),
                                                  ),
                                                  Text(
                                                    'Camera',
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: Color(0xFF828282),
                                                      fontSize: 16,
                                                      fontFamily: 'Poppins',
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                          const VerticalDivider(width: 0),
                                          Expanded(
                                            child: InkWell(
                                              onTap: () async {
                                                await imagePicker();
                                                setState2(() {});
                                              },
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    CupertinoIcons.photo,
                                                    color: Color(0xFF828282),
                                                  ),
                                                  Text(
                                                    'Gallery',
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: Color(0xFF828282),
                                                      fontSize: 16,
                                                      fontFamily: 'Poppins',
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    DropdownButtonFormField(
                      decoration: InputDecoration(
                          focusedBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Color(0xFF828282),
                              ),
                              borderRadius: BorderRadius.circular(5)),
                          border: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Color(0xFF828282),
                              ),
                              borderRadius: BorderRadius.circular(5))),
                      items: namelist.map((name) {
                        return DropdownMenuItem(
                          value: name.toString(),
                          child: Text(name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        selectedType = value;
                        setState2(() {});
                      },
                      hint: Text(
                        'Select Category',
                        style: TextStyle(
                          fontSize: size.width > 300 ? 15 : 12,
                          color: const Color(0xFF828282),
                        ),
                      ),
                    ),
                  ],
                ),
                actions: [
                  loading2
                      ? const Center(
                          child: SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: themeColor,
                              strokeWidth: 3,
                            ),
                          ),
                        )
                      : SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                              onPressed: () async {
                                if (pickedImage == null ||
                                    selectedType == null) {
                                  return;
                                }
                                setState2(() {
                                  loading2 = true;
                                });
                                try {
                                  final photoUrl =
                                      await uploadClothFile(pickedImage!);

                                  final data = <String, dynamic>{
                                    "photoUrl": photoUrl,
                                    "category": selectedType,
                                  };
                                  await FBFireStore.residents
                                      .doc(Get.find<HomeCtrl>()
                                          .residentModel!
                                          .docId)
                                      .collection("clothes")
                                      .add(data);
                                  setState2(() {
                                    loading2 = false;
                                  });
                                  // setState(() {});
                                  if (context.mounted) {
                                    Navigator.of(context).pop();
                                  }
                                  showAppSnackBar('New Cloth Added');
                                } catch (e) {
                                  debugPrint(e.toString());
                                }
                              },
                              style: ButtonStyle(
                                  backgroundColor: const WidgetStatePropertyAll(
                                      Color(0xFF01B49E)),
                                  shape: WidgetStatePropertyAll(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(5)))),
                              child: const Text(
                                'Save',
                                style: TextStyle(color: Colors.white),
                              )),
                        ),
                ],
              );
            }));
  }

  Future imagePicker() async {
    final res =
        await ImagePickerService().pickImageNew(context, useCompressor: true);
    if (res != null) {
      pickedImage = res;
    }
    setState(() {});
  }

  Future getImageFromCamera() async {
    final resCam = await ImagePickerService()
        .pickImageNewCamera(context, useCompressor: true);

    setState(() {
      if (resCam != null) {
        pickedImage = resCam;
      }
    });
  }*/
}

class Txt extends StatelessWidget {
  const Txt({
    super.key,
    required this.txt,
    required this.txt2,
  });
  final String txt;
  final String txt2;
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Row(
      children: [
        Text(
          txt,
          style: TextStyle(
            color: const Color(0xFF4F4F4F),
            fontSize: size.width > 335 ? 18 : 15,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
            height: 0,
          ),
        ),
        Text(
          txt2,
          style: TextStyle(
            color: const Color(0xFF01B49E),
            fontSize: size.width > 335 ? 24 : 20,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
            height: 0,
          ),
        )
      ],
    );
  }
}

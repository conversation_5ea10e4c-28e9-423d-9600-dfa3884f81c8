import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/shared/firebse.dart';
import 'package:laundry_resident/shared/methods.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  List<DisplayNotifyModel> notificationList = [];
  bool loadingData = false;

  @override
  void initState() {
    super.initState();
    getNotificationData();
  }

  getNotificationData() async {
    loadingData = true;
    setState(() {});
    final resident = Get.find<HomeCtrl>().residentModel;
    final globalNotificationList = await FBFireStore.notifies
        .where('hostel', isEqualTo: resident?.hostelDocId)
        .orderBy('time', descending: true)
        .limit(5)
        .get()
        .then(
          (value) => value.docs
              .map((e) => DisplayNotifyModel(
                  isGlobal: true,
                  notiDocId: e.id,
                  title: e['title'],
                  desc: e['desc'],
                  date: e['time'].toDate(),
                  time: e['time'].toDate()))
              .toList(),
        );
    final userNotification = await FBFireStore.residents
        .doc(resident?.docId)
        .collection('notifications')
        .where('type', isNotEqualTo: ActivityType.clothesTallied)
        .orderBy('time', descending: true)
        .limit(7)
        .get()
        .then(
      (value) {
        return value.docs
            .map((e) => DisplayNotifyModel(
                isGlobal: true,
                notiDocId: e.id,
                title: e['title'],
                desc: e['desc'],
                date: e['time'].toDate(),
                time: e['time'].toDate()))
            .toList();
      },
    );
    notificationList.clear();
    notificationList.addAll(globalNotificationList);
    notificationList.addAll(userNotification);
    notificationList.sort((a, b) => b.date.compareTo(a.date));
    loadingData = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shadowColor: Colors.white,
        leading: IconButton(
            onPressed: () {
              context.pop();
            },
            icon: const Icon(Icons.arrow_back)),
        title: const Text(
          'Notification',
          style: TextStyle(
            color: Color(0xFF4F4F4F),
            // fontSize: 24,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: Container(
        height: size.height,
        // padding: EdgeInsets.symmetric(vertical: 8),
        decoration: const BoxDecoration(
            image: DecorationImage(
                repeat: ImageRepeat.repeatY,
                opacity: .7,
                image: AssetImage('assets/images/Image.png'))),
        child: loadingData
            ? const Center(
                child: CircularProgressIndicator(
                strokeWidth: 3.5,
              ))
            : ListView.separated(
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(vertical: 7),
                physics: const ClampingScrollPhysics(),
                separatorBuilder: (context, index) {
                  return const SizedBox(height: 7);
                },
                itemCount: notificationList.length,
                itemBuilder: (context, index) {
                  return NotificationCard(
                    notification: notificationList[index],
                  );
                },
              ),
      ),
    );
  }
}

class NotificationCard extends StatelessWidget {
  const NotificationCard({
    super.key,
    required this.notification,
  });
  final DisplayNotifyModel notification;
  @override
  Widget build(BuildContext context) {
    return ListTile(
      // minLeadingWidth: 1,
      contentPadding: const EdgeInsets.symmetric(horizontal: 10),
      // padding: EdgeInsets.symmetric(horizontal: 4.0),
      leading: const Icon(CupertinoIcons.bell_circle, size: 32),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            notification.title,
            style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
          ),
          Row(
            children: [
              Text(
                notification.date.convertToMMDD(),
                style: const TextStyle(fontSize: 12),
              ),
              const SizedBox(width: 5),
              Container(
                width: 1,
                height: 10,
                color: Colors.black,
              ),
              const SizedBox(width: 5),
              Text(
                notification.date.goodTime(),
                style: const TextStyle(fontSize: 12),
              )
            ],
          )
        ],
      ),
      subtitle: Text(
        notification.desc,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}

class DisplayNotifyModel {
  bool isGlobal;
  String notiDocId;
  String title;
  String desc;
  DateTime date;
  DateTime time;

  DisplayNotifyModel({
    required this.isGlobal,
    required this.notiDocId,
    required this.title,
    required this.desc,
    required this.date,
    required this.time,
  });
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/models/complaints_model.dart';
import 'package:laundry_resident/models/pickup_schedules.dart';
import 'package:laundry_resident/shared/firebse.dart';
import 'package:laundry_resident/shared/methods.dart';
import 'package:laundry_resident/shared/theme.dart';
import 'package:laundry_resident/views/common/submit_button.dart';

class ClothesCollection extends StatefulWidget {
  const ClothesCollection({super.key});

  @override
  State<ClothesCollection> createState() => _ClothesCollectionState();
}

final ctrl = Get.find<HomeCtrl>();

class _ClothesCollectionState extends State<ClothesCollection>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  List<String> tabStrings = ['Washed Clothes', 'Missing Clothes'];
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Scaffold(
      // backgroundColor: const Color(0xFFF2F2F2),
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shadowColor: Colors.white,
        titleSpacing: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
        title: const Text(
          'Collect Clothes',
          style: TextStyle(
            color: Color(0xFF4F4F4F),
            // fontSize: 24,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: Container(
        height: size.height,
        width: double.maxFinite,
        decoration: const BoxDecoration(
            image: DecorationImage(
                repeat: ImageRepeat.repeatY,
                opacity: .5,
                image: AssetImage('assets/images/Image.png'))),
        child: Container(
          padding: const EdgeInsets.only(bottom: 15),
          child: Column(
            children: [
              TabBar(
                controller: _tabController,
                indicatorSize: TabBarIndicatorSize.tab,
                labelPadding: const EdgeInsets.symmetric(vertical: 15),
                tabs: [
                  Text(
                    tabStrings[0],
                  ),
                  Text(
                    tabStrings[1],
                  ),
                ],
              ),
              Expanded(
                child: TabBarView(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: _tabController,
                  children: const [
                    WashedClothes(),
                    MissingClothes(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class WashedClothes extends StatelessWidget {
  const WashedClothes({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<PickUpScheduleModel>>(
        stream: FBFireStore.pickUpSchedules
            .where("hostelDocId", isEqualTo: ctrl.residentModel!.hostelDocId)
            .where("roomNo", isEqualTo: ctrl.residentModel!.roomNo)
            .where("pickedUp", isEqualTo: true)
            .where("delivered", isEqualTo: false)
            .snapshots()
            .map((event) => event.docs
                .map((e) => PickUpScheduleModel.fromSnap(e))
                .toList()),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          if (snapshot.hasError) {
            debugPrint(snapshot.error.toString());
          }
          if (snapshot.hasData) {
            final pickupScheduleList = snapshot.data ?? [];
            pickupScheduleList.sort(
              (a, b) => a.bedNo.compareTo(b.bedNo),
            );
            return pickupScheduleList.isEmpty
                ? const NoDataWid()
                : SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(25.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...List.generate(
                              pickupScheduleList.length,
                              (index) => IndividualSet(
                                    ctx: context,
                                    pickUpScheduleModel:
                                        pickupScheduleList[index],
                                  ))
                        ],
                      ),
                    ),
                  );
          } else {
            return const NoDataWid();
          }
        });
  }
}

class MissingClothes extends StatelessWidget {
  const MissingClothes({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<PickUpScheduleModel>>(
        stream: FBFireStore.pickUpSchedules
            // .where("hostelDocId", isEqualTo: ctrl.residentModel!.hostelDocId)
            // .where("roomNo", isEqualTo: ctrl.residentModel!.roomNo)
            .where("pickedUp", isEqualTo: true)
            .where("delivered", isEqualTo: true)
            .where('missingCount', isGreaterThan: 0)
            .where('foundClothes', isNull: false)
            .where(Filter.and(
              Filter.or(
                  Filter('hostelDocId',
                      isEqualTo: ctrl.residentModel!.hostelDocId),
                  Filter('roomNo', isEqualTo: ctrl.residentModel!.roomNo)),
              Filter('uId', isEqualTo: FBAuth.auth.currentUser!.uid),
            ))
            .snapshots()
            .map((event) {
          return event.docs
              .map((e) => PickUpScheduleModel.fromSnap(e))
              .toList();
        }),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          if (snapshot.hasError) {
            debugPrint(snapshot.error.toString());
          }
          if (snapshot.hasData) {
            final pickupScheduleList = snapshot.data ?? [];
            pickupScheduleList.sort(
              (a, b) => a.bedNo.compareTo(b.bedNo),
            );
            return pickupScheduleList.isEmpty
                ? const NoDataWid()
                : SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(25.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...List.generate(
                              pickupScheduleList.length,
                              (index) => MissingDeliveryIndividualSet(
                                    ctx: context,
                                    pickUpScheduleModel:
                                        pickupScheduleList[index],
                                  ))
                        ],
                      ),
                    ),
                  );
          } else {
            return const NoDataWid();
          }
        });
  }
}

class NoDataWid extends StatelessWidget {
  const NoDataWid({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            "Clothes Delivered",
            style: TextStyle(fontSize: 20),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
              style: ButtonStyle(
                  shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8))),
                  backgroundColor:
                      const WidgetStatePropertyAll(Color(0xff01B49E)),
                  elevation: const WidgetStatePropertyAll(0),
                  padding: const WidgetStatePropertyAll(
                      EdgeInsets.symmetric(vertical: 12, horizontal: 20))),
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                "Go Back",
                style: TextStyle(fontSize: 18, color: Colors.white),
              )),
        ],
      ),
    );
  }
}

class IndividualSet extends StatefulWidget {
  const IndividualSet({
    super.key,
    required this.pickUpScheduleModel,
    required this.ctx,
  });
  final PickUpScheduleModel? pickUpScheduleModel;
  final BuildContext ctx;

  @override
  State<IndividualSet> createState() => _IndividualSetState();
}

class _IndividualSetState extends State<IndividualSet> {
  List<ClothPickupModel> pickedClothList = [];
  List<String> removedClothesIds = [];

  // @override
  // void initState() {
  //   super.initState();
  //   if (widget.pickUpScheduleModel != null) {
  //     removerClothesIds = widget.pickUpScheduleModel!.clothes
  //         .where((element) => element.tallied != true)
  //         .toList();
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    pickedClothList = widget.pickUpScheduleModel?.clothes
            .where((element) => element.pickedUp == true)
            .toList() ??
        [];
    final unTalliedClothesCount = pickedClothList
        .where((element) => element.tallied != true)
        .toList()
        .length;
    // print(talliedClothesCount);
    bool waiting = widget.pickUpScheduleModel?.needApproval ?? false;
    return widget.pickUpScheduleModel != null
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Bed ${widget.pickUpScheduleModel!.bedNo}",
                style: const TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  Expanded(
                    child: StaggeredGrid.extent(
                      maxCrossAxisExtent: 280,
                      crossAxisSpacing: 18,
                      mainAxisSpacing: 18,
                      children: [
                        ...List.generate(pickedClothList.length, (index) {
                          final cloth = pickedClothList[index];
                          bool accept =
                              !removedClothesIds.contains(cloth.docId) &&
                                  cloth.collected != false;
                          return Stack(
                            children: [
                              AspectRatio(
                                aspectRatio: 1,
                                child: Container(
                                  // constraints: const BoxConstraints(maxHeight: 150),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      color: const Color(0xFFF2F2F2),
                                      image: DecorationImage(
                                          image: NetworkImage(cloth.photoUrl),
                                          fit: BoxFit.cover)),
                                ),
                              ),
                              if (widget.pickUpScheduleModel?.talliedAt != null)
                                Align(
                                  alignment: const Alignment(-1, -1),
                                  child: Padding(
                                    padding: const EdgeInsets.all(10.0),
                                    child: Transform.scale(
                                      scale: 1.4,
                                      child: Checkbox(
                                        fillColor: WidgetStatePropertyAll(
                                            accept && cloth.tallied == true
                                                ? const Color(0xFF01B49E)
                                                : Colors.white),
                                        side: const BorderSide(
                                            color: Color(0xFF01B49E)),
                                        value: accept && cloth.tallied == true,
                                        onChanged: (value) {
                                          if (cloth.tallied == false ||
                                              widget.pickUpScheduleModel
                                                      ?.needApproval ==
                                                  true) return;
                                          setState(() {
                                            if (value == true) {
                                              removedClothesIds
                                                  .remove(cloth.docId);
                                            } else {
                                              removedClothesIds
                                                  .add(cloth.docId);
                                            }
                                          });
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          );
                          /*  ImageCard(
                              imageName: widget.pickUpScheduleModel!
                                  .clothes[index].photoUrl); */
                        })
                      ],
                    ),
                  ),
                ],
              ),
              if (widget.pickUpScheduleModel!.enableApprove || waiting)
                const SizedBox(height: 20),
              if (widget.pickUpScheduleModel!.enableApprove || waiting)
                Row(
                  children: [
                    Expanded(
                      child: waiting
                          ? Container(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: const Color(0xff828282),
                                    width: 1,
                                  ),
                                  color: const Color.fromARGB(50, 1, 180, 159)),
                              child: const Center(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      CupertinoIcons.hourglass,
                                      color: Color(0xff828282),
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      "Waiting for approval",
                                      style: TextStyle(
                                          color: Color(0xff828282),
                                          fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                            )

                          /* ElevatedButton(
                              style: ButtonStyle(
                                  padding: const WidgetStatePropertyAll(
                                      EdgeInsets.symmetric(vertical: 20)),
                                  backgroundColor:
                                      WidgetStatePropertyAll(bgColor),
                                  elevation: const WidgetStatePropertyAll(0),
                                  shape: WidgetStatePropertyAll(
                                    RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6)),
                                  )),
                              onPressed: onPressed,
                              child: Text(
                                text,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  letterSpacing: 2,
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w600,
                                  height: 0,
                                ),
                              ),
                            ) */
                          : CommonSubmitButton(
                              text:
                                  'Collect (${(pickedClothList.length - unTalliedClothesCount) - removedClothesIds.length})',

                              ///* - (accpetedClothList.length - talliedClothesCount))  */
                              onPressed: () async {
                                onPressedSubmit(widget.ctx);
                              },
                              bgColor: const Color(0xFF01B49E),
                            ),
                    )
                  ],
                ),
              const SizedBox(height: 30),
            ],
          )
        : Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  "Clothes Delivered",
                  style: TextStyle(fontSize: 25),
                ),
                const SizedBox(height: 15),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text(
                    "Go Back",
                    style: TextStyle(fontSize: 20),
                  ),
                ),
              ],
            ),
          );
  }

  onPressedSubmit(BuildContext ctx) async {
    try {
      List<ClothPickupModel> unTalliedClothes = [];

      int totalMissingValue = 0;
      unTalliedClothes =
          pickedClothList.where((element) => element.tallied != true).toList();
      for (var element in unTalliedClothes) {
        totalMissingValue += element.categoryValue;
      }

      final res = await FBFireStore.fb.runTransaction((transaction) async {
        transaction.update(
            FBFireStore.pickUpSchedules.doc(widget.pickUpScheduleModel!.docId),
            {
              "needApproval": removedClothesIds.isNotEmpty,
              "enableApprove": false,
              "delivered": removedClothesIds.isEmpty,
              "collectedByDocId": ctrl.residentModel?.docId,
              "collectedByName": ctrl.residentModel?.name,
              "deliveryTime": FieldValue.serverTimestamp(),
              "missingCount":
                  unTalliedClothes.isNotEmpty ? unTalliedClothes.length : null,
              "missingValue":
                  unTalliedClothes.isNotEmpty ? totalMissingValue : null,
              "clothes": pickedClothList
                  .map((e) => e.toJson3(e.tallied != true
                      ? e.collected
                      : !removedClothesIds.contains(e.docId)))
                  .toList(),
            });

        return true;
      });
      if (res == true) {
        if (removedClothesIds.isEmpty) {
          await initiateActivity(
            uId: widget.pickUpScheduleModel?.uId,
            type: ActivityType.clothesDelivered,
            typeDocId: widget.pickUpScheduleModel?.docId,
            desc:
                'Your ${pickedClothList.length - unTalliedClothes.length} out of ${pickedClothList.length}  clothes has been delivered on ${DateTime.now().convertToDDMMYY()} at ${DateTime.now().goodTime()}.',
            userType: UserTypes.resident,
            title: 'Clothes Delivered',
          );
        }
        // TODO: check this code once
        // -----------------------
        /*  final lst = pickedClothList
            .map((e) => e.toJson3(e.tallied != true
                ? e.collected
                : !removedClothesIds.contains(e.docId)))
            .toList();
        final newList = List<ClothPickupModel>.from(
            lst.map((item) => ClothPickupModel.fromJson(item))); */
        // ------------------------
        if (/* (newList.where((element) => element.collected == true).length <
                pickedClothList.length) && */
            removedClothesIds.isEmpty && unTalliedClothes.isNotEmpty) {
          final randomId = getRandomId(8).toUpperCase();
          final res2 = await initiateComplaint(
            autoGenerate: true,
            uId: widget.pickUpScheduleModel!.uId,
            hostelDocId: widget.pickUpScheduleModel!.hostelDocId,
            wing: widget.pickUpScheduleModel!.wing,
            floor: widget.pickUpScheduleModel!.floor,
            roomNo: widget.pickUpScheduleModel!.roomNo,
            bedNo: widget.pickUpScheduleModel!.bedNo,
            residentName: widget.pickUpScheduleModel!.residentName,
            bunchDocId: widget.pickUpScheduleModel!.bunchDocId ?? "",
            pickUpScheduleDocId: widget.pickUpScheduleModel!.docId,
            complainType: ["Missing clothes"],
            complainDesc: 'Missing cloth complain',
            missingClothes: unTalliedClothes.map((e) => e.docId).toList(),
            clothesUrl: [],
            pickupDate: widget.pickUpScheduleModel!.pickedAt!.toDate(),
            randomId: randomId,
          );
          if (res2 == true && ctx.mounted && removedClothesIds.isEmpty) {
            return showDialog(
              context: ctx,
              builder: (context) {
                return AlertDialog(
                  title: const Text('Ticket Raised'),
                  content: Text(
                      'Your missing clothes complain has been raised under Id: $randomId. We will surely comeback to you within 2 days.'),
                  actions: [
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            style: TextButton.styleFrom(
                              // backgroundColor: const Color.fromARGB(20, 0, 0, 0),
                              backgroundColor: themeColor,
                              elevation: 0,
                            ),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: const Text(
                              'OK',
                              style:
                                  TextStyle(color: Colors.white, fontSize: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            );
          }
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}

class MissingDeliveryIndividualSet extends StatefulWidget {
  const MissingDeliveryIndividualSet({
    super.key,
    required this.pickUpScheduleModel,
    required this.ctx,
  });
  final PickUpScheduleModel? pickUpScheduleModel;
  final BuildContext ctx;

  @override
  State<MissingDeliveryIndividualSet> createState() =>
      _MissingDeliveryIndividualSetState();
}

class _MissingDeliveryIndividualSetState
    extends State<MissingDeliveryIndividualSet> {
  List<FoundClothPickupModel> missingClothList = [];
  List<String> notFoundClothesDocId = [];

  // @override
  // void initState() {
  //   super.initState();
  //   if (widget.pickUpScheduleModel != null) {
  //     removerClothesIds = widget.pickUpScheduleModel!.clothes
  //         .where((element) => element.tallied != true)
  //         .toList();
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    missingClothList = widget.pickUpScheduleModel?.foundClothes ?? [];
    notFoundClothesDocId = missingClothList
        .where((element) => !element.found)
        .map((e) => e.docId)
        .toList();
    // print(talliedClothesCount);
    // bool waiting = widget.pickUpScheduleModel?.needApproval ?? false;
    return widget.pickUpScheduleModel != null
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Bed ${widget.pickUpScheduleModel!.bedNo}",
                style: const TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  Expanded(
                    child: StaggeredGrid.extent(
                      maxCrossAxisExtent: 280,
                      crossAxisSpacing: 18,
                      mainAxisSpacing: 18,
                      children: [
                        ...List.generate(missingClothList.length, (index) {
                          final cloth = missingClothList[index];
                          bool accept = cloth.found;
                          return Stack(
                            children: [
                              AspectRatio(
                                aspectRatio: 1,
                                child: Container(
                                  // constraints: const BoxConstraints(maxHeight: 150),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      color: const Color(0xFFF2F2F2),
                                      image: DecorationImage(
                                          image: NetworkImage(cloth.photoUrl),
                                          fit: BoxFit.cover)),
                                ),
                              ),
                              Align(
                                alignment: const Alignment(-1, -1),
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: Transform.scale(
                                    scale: 1.4,
                                    child: Checkbox(
                                        fillColor: WidgetStatePropertyAll(accept
                                            ? const Color(0xFF01B49E)
                                            : Colors.white),
                                        side: const BorderSide(
                                            color: Color(0xFF01B49E)),
                                        value: accept,
                                        onChanged: null),
                                  ),
                                ),
                              ),
                            ],
                          );
                          /*  ImageCard(
                              imageName: widget.pickUpScheduleModel!
                                  .clothes[index].photoUrl); */
                        })
                      ],
                    ),
                  ),
                ],
              ),
              // if (widget.pickUpScheduleModel!.enableApprove || waiting)
              const SizedBox(height: 20),
              // if (widget.pickUpScheduleModel!.enableApprove || waiting)
              Row(
                children: [
                  Expanded(
                    child:
                        /*  waiting
                          ? Container(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: const Color(0xff828282),
                                    width: 1,
                                  ),
                                  color: const Color.fromARGB(50, 1, 180, 159)),
                              child: const Center(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      CupertinoIcons.hourglass,
                                      color: Color(0xff828282),
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      "Waiting for approval",
                                      style: TextStyle(
                                          color: Color(0xff828282),
                                          fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                            )

                          /* ElevatedButton(
                              style: ButtonStyle(
                                  padding: const WidgetStatePropertyAll(
                                      EdgeInsets.symmetric(vertical: 20)),
                                  backgroundColor:
                                      WidgetStatePropertyAll(bgColor),
                                  elevation: const WidgetStatePropertyAll(0),
                                  shape: WidgetStatePropertyAll(
                                    RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6)),
                                  )),
                              onPressed: onPressed,
                              child: Text(
                                text,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  letterSpacing: 2,
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w600,
                                  height: 0,
                                ),
                              ),
                            ) */
                          :  */
                        CommonSubmitButton(
                      text:
                          'Collect (${missingClothList.length - notFoundClothesDocId.length})',

                      ///* - (accpetedClothList.length - talliedClothesCount))  */
                      onPressed: () async {
                        onPressedSubmit(widget.ctx);
                      },
                      bgColor: const Color(0xFF01B49E),
                    ),
                  )
                ],
              ),
              const SizedBox(height: 30),
            ],
          )
        : Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  "Clothes Delivered",
                  style: TextStyle(fontSize: 25),
                ),
                const SizedBox(height: 15),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text(
                    "Go Back",
                    style: TextStyle(fontSize: 20),
                  ),
                ),
              ],
            ),
          );
  }

  onPressedSubmit(BuildContext ctx) async {
    try {
      int totalMissingValue = 0;
      final notFoundClothes = missingClothList
          .where((element) => notFoundClothesDocId.contains(element.docId))
          .toList();
      for (var element in notFoundClothes) {
        totalMissingValue += element.categoryValue;
      }
      await FBFireStore.fb.runTransaction((transaction) async {
        transaction.update(
            FBFireStore.pickUpSchedules.doc(widget.pickUpScheduleModel!.docId),
            {
              // "delivered": removedClothesIds.isEmpty,
              // "collectedByDocId": ctrl.residentModel?.docId,
              // "collectedByName": ctrl.residentModel?.name,
              // "deliveryTime": FieldValue.serverTimestamp(),
              "needApproval": false,
              "enableApprove": false,
              "missingCount": notFoundClothesDocId.isNotEmpty
                  ? notFoundClothesDocId.length
                  : null,
              "missingValue":
                  notFoundClothesDocId.isNotEmpty ? totalMissingValue : null,
              'foundClothes': null,
              "clothes": widget.pickUpScheduleModel!.clothes
                  .map((e) => e.toJson3(notFoundClothesDocId.contains(e.docId)
                          ? e.collected
                          : true
                      // e.tallied != true
                      //   ? e.collected
                      //   : !removedClothesIds.contains(e.docId)
                      ))
                  .toList(),
              'complaintSolved': true,
            });

        if (notFoundClothesDocId.isEmpty) {
          final complainSnap = await FBFireStore.complaints
              .where('pickUpScheduleDocId',
                  isEqualTo: widget.pickUpScheduleModel!.docId)
              .get();
          if (complainSnap.docs.isNotEmpty) {
            final complain =
                ComplaintsModel.fromDocSnap(complainSnap.docs.first);
            transaction.update(FBFireStore.complaints.doc(complain.docId), {
              'resolved': true,
              'status': 'Resolved',
              'remark': 'cloth found and delivered',
              'resolvedAt': FieldValue.serverTimestamp(),
            });
            initiateActivity(
              uId: widget.pickUpScheduleModel?.uId,
              type: ActivityType.complain,
              typeDocId: complain.docId,
              desc:
                  'Your complain raised under ID: ${complain.randomId} has been resolved on date ${DateTime.now().convertToDDMMYY()} at ${DateTime.now().goodTime()}.',
              userType: UserTypes.admin,
              title: 'Complain Resolved',
            );
          }
        }
        //  if (removedClothesIds.isEmpty) {
        await initiateActivity(
          uId: widget.pickUpScheduleModel?.uId,
          type: ActivityType.clothesDelivered,
          typeDocId: widget.pickUpScheduleModel?.docId,
          desc:
              'Your ${missingClothList.length - notFoundClothes.length} out of ${missingClothList.length} clothes has been delivered on ${DateTime.now().convertToDDMMYY()} at ${DateTime.now().goodTime()}.',
          userType: UserTypes.resident,
          title: 'Clothes Delivered',
        );
        // }
        return true;
      });
      /*   if (res == true) {
        final lst = widget.pickUpScheduleModel!.clothes
            .map((e) => e.toJson3(e.tallied != true
                ? e.collected
                : !removedClothesIds.contains(e.docId)))
            .toList();
        final newList = List<ClothPickupModel>.from(
            lst.map((item) => ClothPickupModel.fromJson(item)));
        if (newList.where((element) => element.collected == true).length <
                widget.pickUpScheduleModel!.clothes
                    .map((e) => e.pickedUp == true)
                    .length &&
            removedClothesIds.isEmpty) {
          final randomId = getRandomId(8).toUpperCase();
          final res2 = await initiateComplaint(
            autoGenerate: true,
            uId: widget.pickUpScheduleModel!.uId,
            hostelDocId: widget.pickUpScheduleModel!.hostelDocId,
            wing: widget.pickUpScheduleModel!.wing,
            floor: widget.pickUpScheduleModel!.floor,
            roomNo: widget.pickUpScheduleModel!.roomNo,
            bedNo: widget.pickUpScheduleModel!.bedNo,
            residentName: widget.pickUpScheduleModel!.residentName,
            bunchDocId: widget.pickUpScheduleModel!.bunchDocId ?? "",
            pickUpScheduleDocId: widget.pickUpScheduleModel!.docId,
            complainType: ["Missing clothes"],
            complainDesc: 'Missing cloth complain',
            missingClothes: notFoundClothes.map((e) => e.docId).toList(),
            clothesUrl: [],
            randomId: randomId,
          );
          if (res2 == true && ctx.mounted && removedClothesIds.isEmpty) {
            return showDialog(
              context: ctx,
              builder: (context) {
                return AlertDialog(
                  title: const Text('Ticket Raised'),
                  content: Text(
                      'Your missing clothes complain has been raised under Id: $randomId. We will surely comeback to you within 2 days.'),
                  actions: [
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            style: TextButton.styleFrom(
                              // backgroundColor: const Color.fromARGB(20, 0, 0, 0),
                              backgroundColor: themeColor,
                              elevation: 0,
                            ),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: const Text(
                              'OK',
                              style:
                                  TextStyle(color: Colors.white, fontSize: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            );
          }
        }
      } */
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}

/* 
class ImageCard extends StatefulWidget {
  const ImageCard({super.key, required this.imageName});
  final String imageName;
  @override
  State<ImageCard> createState() => _ImageCardState();
}

class _ImageCardState extends State<ImageCard> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AspectRatio(
          aspectRatio: 1,
          child: Container(
            // constraints: const BoxConstraints(maxHeight: 150),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: const Color(0xFFF2F2F2),
                image: DecorationImage(
                    image: NetworkImage(widget.imageName), fit: BoxFit.cover)),
          ),
        ),
        Align(
          alignment: const Alignment(-1, -1),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Transform.scale(
              scale: 1.4,
              child: Checkbox(
                fillColor: WidgetStatePropertyAll(
                    accept ? const Color(0xFF01B49E) : Colors.white),
                side: const BorderSide(color: Color(0xFF01B49E)),
                value: accept,
                onChanged: (value) {
                  setState(() {
                    accept = value!;
                  });
                },
              ),
            ),
          ),
        ),
      ],
    );
  }
}
 */

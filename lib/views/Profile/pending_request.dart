import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/models/hostel_model_ori.dart';
import 'package:laundry_resident/models/resident.dart';
import 'package:laundry_resident/models/review_model.dart';
import 'package:laundry_resident/services/image_picker.dart';
import 'package:laundry_resident/shared/firebse.dart';

class ReviewPendingPage extends StatefulWidget {
  const ReviewPendingPage({super.key, this.resident});
  final ResidentModel? resident;

  @override
  State<ReviewPendingPage> createState() => _ReviewPendingPageState();
}

class _ReviewPendingPageState extends State<ReviewPendingPage> {
  String? originalname;
  SelectedImage? pickedImage;
  bool loading = false;
  List<HostelModel> hostels = [];
  HostelModel? selectedHostel;
  int? selectedFloorNew;
  int? selectedRoomNew;
  int? selectedBedNew;
  String? dropdownValueFloor;
  String? dropdownValueHostel;
  String? dropdownValueRoom;
  String? dropdownValueBednum;
  final namectrl = TextEditingController();
  final enrollidctrl = TextEditingController();
  final mobileCtrl = TextEditingController();
  bool enabled = false;
  ReviewModel? reviewExist;
  @override
  void initState() {
    super.initState();
    getHostels();
    assignValue();
  }

  getHostels() async {
    try {
      //  final res = await FBFireStore.hostels.get();
      final res =
          await FBFireStore.hostels.where('blocked', isEqualTo: false).get();
      hostels = res.docs.map((e) => HostelModel.fromSnap(e)).toList();
      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
    final HostelModel? reviewHostel = hostels.firstWhereOrNull(
        (element) => element.docId == reviewExist?.hostelDocId);
    selectedHostel = reviewHostel;
  }

  assignValue() {
    final ctrl = Get.find<HomeCtrl>();
    reviewExist = ctrl.review;
    // print(hostels.length);

    // print(reviewHostel?.docId);
    if (widget.resident != null && reviewExist != null) {
      originalname = widget.resident!.name;
      namectrl.text = reviewExist!.name;
      mobileCtrl.text = reviewExist?.mobile ?? "";
      enrollidctrl.text = reviewExist!.hostelId;
      selectedFloorNew = reviewExist!.floor;
      selectedRoomNew = reviewExist!.roomNo;
      selectedBedNew = reviewExist!.bedNo;
    }
  }

  WingModel? getSelectedWing() {
    if (selectedHostel == null ||
        selectedFloorNew == null ||
        selectedRoomNew == null) {
      return null;
    }
    WingModel? selectedWing;
    selectedHostel?.wings.forEach((element) {
      element.floors.sort((a, b) => a.fnumber.compareTo(b.fnumber));
      final tmp =
          element.floors[selectedFloorNew! - 1].rooms.firstWhereOrNull((ele) {
        // print(ele.rnumber);
        // print(selectedRoomNew);
        return ele.rnumber == selectedRoomNew.toString();
      });
      // print(tmp.)

      if (tmp != null) {
        selectedWing = element;
      }
    });

    return selectedWing;
  }

  @override
  Widget build(BuildContext context) {
/*  DO NOT DELETE
    // if (hostels.isNotEmpty) {
    //   // print(selectedHostel?.wings.first.floors.length);
    //   // total rooms on a floor
    //   // print(selectedHostel?.wings
    //   //     .map((e) => e.floors.first.rooms.length)
    //   //     .reduce((value, element) => value + element));
    //   // print(selectedHostel?.name);
    //   // print(selectedFloorNew);
    //   // print(selectedRoomNew);
    //   // print(selectedBedNew);

    //   // print(hostels.first.wings
    //   //     .map((e) => e.floors.first.rooms.length)
    //   //     .reduce((value, element) => value + element));
    //   // selectedHostel = hostels.first;
    //   // find selected room
    //   final rm = getSelectedWing()
    //       ?.floors[selectedFloorNew! - 1]
    //       .rooms
    //       .firstWhereOrNull(
    //           (element) => element.rnumber == selectedRoomNew.toString());
    //   // Bed count of selected room
    //   rm?.bedCount;
    // }
     */
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shadowColor: Colors.white,
        titleSpacing: 0,
        leading: IconButton(
            onPressed: () => context.pop(), icon: const Icon(Icons.arrow_back)),
        title: const Text(
          'Requested',
          style: TextStyle(
            color: Color(0xFF4F4F4F),
            // fontSize: 24,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: reviewExist != null
          ? SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(15, 20, 15, 0),
                child: Center(
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 350),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Image.asset('assets/images/profile.png'),
                        const SizedBox(height: 30),
                        const Text(
                          'Your requested details',
                          style: TextStyle(
                            color: Color(0xFF333333),
                            fontSize: 30,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w600,
                            height: 0,
                          ),
                        ),
                        const SizedBox(height: 5),
                        const Text(
                          '* Requested changes are marked with red border',
                          style: TextStyle(fontSize: 13.5),
                        ),
                        const SizedBox(height: 20),
                        InputFields(
                          ctrl: namectrl,
                          txt1: 'Full Name',
                          txt2: 'Enter Your Full Name',
                          enabled: widget.resident != null ? enabled : true,
                          color: originalname != namectrl.text
                              ? Colors.red
                              : const Color(0xffF2EEEE),
                        ),

                        const SizedBox(height: 20),

                        InputFields(
                          ctrl: enrollidctrl,
                          txt1: 'Application No.',
                          txt2: 'Enter your Application No.',
                          enabled: widget.resident != null ? enabled : true,
                          color:
                              widget.resident!.hostelId != reviewExist!.hostelId
                                  ? Colors.red
                                  : const Color(0xffF2EEEE),
                        ),
                        const SizedBox(height: 20),
                        InputFields(
                          ctrl: mobileCtrl,
                          txt1: 'Mobile No',
                          txt2: 'Enter your Mobile No.',
                          enabled: widget.resident != null ? enabled : true,
                          color: widget.resident!.mobile != reviewExist!.mobile
                              ? Colors.red
                              : const Color(0xffF2EEEE),
                        ),
                        const SizedBox(height: 20),

                        // Select Hostel DropDown
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Select Hostel',
                              style: TextStyle(
                                color: Color.fromARGB(255, 0, 0, 0),
                                fontSize: 16,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w500,
                                height: 0,
                              ),
                            ),
                            const SizedBox(height: 10),
                            DropdownButtonHideUnderline(
                              child: DropdownButtonFormField<String>(
                                value: selectedHostel?.docId,
                                style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.black),
                                decoration: InputDecoration(
                                  // disabledBorder: OutlineInputBorder(
                                  //     borderSide: BorderSide(color: Colors.black\\)),
                                  enabled:
                                      widget.resident != null ? enabled : true,
                                  contentPadding: const EdgeInsets.symmetric(
                                      vertical: 15, horizontal: 12),
                                  hintText: "Select Hostel",
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(5)),
                                  disabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: widget.resident!.hostelDocId !=
                                                reviewExist!.hostelDocId
                                            ? Colors.red
                                            : const Color(0xffF2EEEE),
                                      ),
                                      borderRadius: BorderRadius.circular(5)),
                                ),
                                items: List.generate(
                                    hostels.length,
                                    (index) => DropdownMenuItem(
                                        enabled: widget.resident != null
                                            ? enabled
                                            : true,
                                        value: hostels[index].docId,
                                        child: Text(hostels[index].name))),
                                onChanged: (value) {
                                  if (value == null) return;
                                  selectedHostel = hostels.firstWhereOrNull(
                                      (element) => element.docId == value);
                                  selectedFloorNew = null;
                                  selectedRoomNew = null;
                                  selectedBedNew = null;
                                  setState(() {});
                                },
                              ),
                            ),
                          ],
                        ),
                        /*  if (selectedHostel != null) const SizedBox(height: 20),
                  if (selectedHostel != null)
          
                    // Select floor DropDown
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Select Floor',
                          style: TextStyle(
                            color: Color.fromARGB(255, 0, 0, 0),
                            fontSize: 16,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                            height: 0,
                          ),
                        ),
                        const SizedBox(height: 10),
                        DropdownButtonHideUnderline(
                          child: DropdownButtonFormField(
                            style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                color: Colors.black),
                            decoration: InputDecoration(
                                enabled:
                                    widget.resident != null ? enabled : true,
                                contentPadding: const EdgeInsets.symmetric(
                                    vertical: 15, horizontal: 12),
                                hintText: 'Select Floor',
                                border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(5))),
                            /* items: selectedHostel?.wings.first.floors
                              .map((e) => DropdownMenuItem(
                                    value: e.fnumber,
                                    child: Text((e.fnumber).toString()),
                                  ))
                              .toList(), */
                            value: selectedFloorNew,
                            items: List.generate(
                                selectedHostel?.wings.first.floors.length ?? 0,
                                (index) => DropdownMenuItem(
                                      enabled: widget.resident != null
                                          ? enabled
                                          : true,
                                      value: index + 1,
                                      child: Text((index + 1).toString()),
                                    )),
                            onChanged: (value) {
                              if (value == null) return;
                              selectedFloorNew = value;
                              selectedRoomNew = null;
                              selectedBedNew = null;
                              setState(() {});
                            },
                          ),
                        ),
                      ],
                    ),
           */
                        if (selectedHostel != null) const SizedBox(height: 20),
                        if (selectedHostel != null)
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Select Floor',
                                      style: TextStyle(
                                        color: Color.fromARGB(255, 0, 0, 0),
                                        fontSize: 16,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w500,
                                        height: 0,
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    DropdownButtonHideUnderline(
                                      child: DropdownButtonFormField(
                                        style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w400,
                                            color: Colors.black),
                                        decoration: InputDecoration(
                                            enabled: widget.resident != null
                                                ? enabled
                                                : true,
                                            contentPadding:
                                                const EdgeInsets.symmetric(
                                                    vertical: 15,
                                                    horizontal: 12),
                                            hintText: 'Select Floor',
                                            disabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: widget.resident!
                                                              .floor !=
                                                          reviewExist!.floor
                                                      ? Colors.red
                                                      : const Color(0xffF2EEEE),
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(5)),
                                            border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(5))),
                                        /* items: selectedHostel?.wings.first.floors
                              .map((e) => DropdownMenuItem(
                                    value: e.fnumber,
                                    child: Text((e.fnumber).toString()),
                                  ))
                              .toList(), */
                                        value: selectedFloorNew,
                                        items: List.generate(
                                            selectedHostel?.wings.first.floors
                                                    .length ??
                                                0,
                                            (index) => DropdownMenuItem(
                                                  enabled:
                                                      widget.resident != null
                                                          ? enabled
                                                          : true,
                                                  value: index + 1,
                                                  child: Text(
                                                      (index + 1).toString()),
                                                )),
                                        onChanged: (value) {
                                          if (value == null) return;
                                          selectedFloorNew = value;
                                          selectedRoomNew = null;
                                          selectedBedNew = null;
                                          setState(() {});
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Select Room',
                                      style: TextStyle(
                                        color: Color.fromARGB(255, 0, 0, 0),
                                        fontSize: 16,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w500,
                                        height: 0,
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    DropdownButtonHideUnderline(
                                        child: DropdownButtonFormField(
                                      style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black),
                                      decoration: InputDecoration(
                                          enabled: widget.resident != null
                                              ? enabled
                                              : true,
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  vertical: 15, horizontal: 12),
                                          hintText: "Select Room",
                                          disabledBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: widget
                                                            .resident!.roomNo !=
                                                        reviewExist!.roomNo
                                                    ? Colors.red
                                                    : const Color(0xffF2EEEE),
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(5)),
                                          border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(5))),
                                      value: selectedRoomNew,
                                      items: List.generate(
                                          // selectedHostel?.wings.length ?? 0,
                                          // hostels.length,
                                          (selectedHostel?.wings
                                                  .map((e) => e.floors.first
                                                      .rooms.length)
                                                  .reduce((value, element) =>
                                                      value + element)) ??
                                              0,
                                          (index) => DropdownMenuItem(
                                              enabled: widget.resident != null
                                                  ? enabled
                                                  : true,
                                              value: (100 * selectedFloorNew!) +
                                                  (index + 1),
                                              child: Text(
                                                  ((100 * selectedFloorNew!) +
                                                          (index + 1))
                                                      .toString()))),
                                      onChanged: (value) {
                                        if (value == null) return;
                                        selectedRoomNew = value;
                                        selectedBedNew = null;

                                        setState(() {});
                                      },
                                    )),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Select Bed',
                                      style: TextStyle(
                                        color: Color.fromARGB(255, 0, 0, 0),
                                        fontSize: 16,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w500,
                                        height: 0,
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    DropdownButtonHideUnderline(
                                        child: DropdownButtonFormField(
                                      style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black),
                                      decoration: InputDecoration(
                                          enabled: widget.resident != null
                                              ? enabled
                                              : true,
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  vertical: 15, horizontal: 12),
                                          hintText: "Select Bed",
                                          disabledBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: widget.resident!.bedNo !=
                                                        reviewExist!.bedNo
                                                    ? Colors.red
                                                    : const Color(0xffF2EEEE),
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(5)),
                                          border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(5))),
                                      value: selectedBedNew,
                                      items: List.generate(
                                          getSelectedWing()
                                                  ?.floors[
                                                      selectedFloorNew! - 1]
                                                  .rooms
                                                  .firstWhereOrNull((element) =>
                                                      element.rnumber ==
                                                      selectedRoomNew
                                                          .toString())!
                                                  .bedCount ??
                                              0,
                                          (index) => DropdownMenuItem(
                                              enabled: widget.resident != null
                                                  ? enabled
                                                  : true,
                                              value: index + 1,
                                              child: Text("Bed ${index + 1}"))),
                                      onChanged: (value) {
                                        if (value == null) return;
                                        selectedBedNew = value;
                                        setState(() {});
                                      },
                                    )),
                                  ],
                                ),
                              ),
                            ],
                          ),

                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 20),
                            const Text(
                              'Uploaded Image',
                              style: TextStyle(
                                color: Color.fromARGB(255, 0, 0, 0),
                                fontSize: 16,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w500,
                                height: 0,
                              ),
                            ),
                            // const Text(
                            //   'Note:- Please upload document for proof',
                            //   style: TextStyle(
                            //     color: Color.fromARGB(255, 0, 0, 0),
                            //     fontSize: 12,
                            //     height: 0,
                            //   ),
                            // ),
                            const SizedBox(height: 10),
                            InkWell(
                              child: Container(
                                height: 150,
                                width: double.maxFinite,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  border:
                                      Border.all(color: Colors.grey, width: 1),
                                ),
                                clipBehavior: Clip.antiAlias,
                                child: CachedNetworkImage(
                                  imageUrl: reviewExist?.imageUrl ?? "",
                                  fit: BoxFit.cover,
                                  errorWidget: (context, url, error) {
                                    return const Text("No image to display");
                                  },
                                ),
                              ),
                            ),
                            // const SizedBox(height: 20),
                          ],
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            )
          : const SizedBox(),
    );
  }

  Future imagePicker() async {
    final res =
        await ImagePickerService().pickImageNew(context, useCompressor: true);
    if (res != null) {
      pickedImage = res;
    }
    setState(() {});
  }

  Future getImageFromCamera() async {
    final resCam = await ImagePickerService()
        .pickImageNewCamera(context, useCompressor: true);

    setState(() {
      if (resCam != null) {
        pickedImage = resCam;
      }
    });
  }
}

class InputFields extends StatelessWidget {
  const InputFields(
      {super.key,
      required this.ctrl,
      required this.txt1,
      required this.txt2,
      required this.enabled,
      required this.color});
  final TextEditingController ctrl;
  final String txt1;
  final String txt2;
  final bool enabled;
  final Color color;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          txt1,
          style: const TextStyle(
            color: Color.fromARGB(255, 0, 0, 0),
            fontSize: 16,
            height: 0,
            fontWeight: FontWeight.w500,
            fontFamily: 'Poppins',
          ),
        ),
        const SizedBox(height: 10),
        SizedBox(
          // width: 380,
          // height: 50,
          child: TextField(
            controller: ctrl,
            style: const TextStyle(color: Colors.black),
            decoration: InputDecoration(
              enabled: enabled,
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 15, horizontal: 12),
              hintText: txt2,
              border: OutlineInputBorder(
                borderSide: BorderSide(color: color),
                borderRadius: BorderRadius.circular(5),
              ),
              disabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: color),
                borderRadius: BorderRadius.circular(5),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/* 
class DropDown extends StatelessWidget {
  const DropDown({
    Key? key,
    required this.value,
    required this.list,
    required this.funct,
    required this.headtxt,
    required this.fieldtxt,
  }) : super(key: key);

  final String headtxt;
  final String fieldtxt;
  final List<DropdownMenuItem<String>> list;
  final Function(String?) funct;
  final String? value;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            headtxt,
            style: const TextStyle(
              color: Color.fromARGB(255, 0, 0, 0),
              fontSize: 16,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
              height: 0,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          DropdownButtonFormField<String>(
            hint: Text(
              fieldtxt,
              style: const TextStyle(color: Color.fromARGB(255, 91, 88, 88)),
            ),
            decoration: InputDecoration(
                border:
                    OutlineInputBorder(borderRadius: BorderRadius.circular(0))),
            value: value,
            onChanged: funct,
            items: list,
          ),
        ],
      ),
    );
  }
}
 */

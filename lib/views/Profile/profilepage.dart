import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/models/hostel_model_ori.dart';
import 'package:laundry_resident/models/resident.dart';
import 'package:laundry_resident/models/review_model.dart';
import 'package:laundry_resident/services/image_picker.dart';
import 'package:laundry_resident/shared/firebse.dart';
import 'package:laundry_resident/shared/methods.dart';
import 'package:laundry_resident/shared/router.dart';
import 'package:laundry_resident/views/common/input_fields.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:unique_identifier/unique_identifier.dart';
import '../../shared/theme.dart';

/* class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key, this.emptyProfile = false});
  // final ResidentModel? resident;
  final bool emptyProfile;

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  String? existingDataDocId;
  UserSavedModel? savedData;

  SelectedImage? proofPickedImage;
  SelectedImage? profilePickedImage;

  // bool fetchHostelLoading = false;
  bool fetchSaveDataLoading = false;
  bool onLoadSubmit = false;

  List<HostelModel> hostels = [];
  HostelModel? selectedHostel;

  int? selectedFloorNew;
  int? selectedRoomNew;
  int? selectedBedNew;
  String? dropdownValueFloor;
  String? dropdownValueHostel;
  String? dropdownValueRoom;
  String? dropdownValueBednum;

  final namectrl = TextEditingController();
  final enrollidctrl = TextEditingController();
  final mobileCtrl = TextEditingController();
  bool uploadProof = false;
  // ReviewModel? reviewExist;

  @override
  void initState() {
    super.initState();
    // getHostels();
  }

  // assignValue() {
  //   if (widget.resident != null) {
  //     final ctrl = Get.find<HomeCtrl>();
  //     namectrl.text = widget.resident!.name;
  //     enrollidctrl.text = widget.resident!.hostelId;
  //     selectedHostel = ctrl.hostelModel;
  //     selectedFloorNew = widget.resident!.floor;
  //     selectedRoomNew = widget.resident!.roomNo;
  //     selectedBedNew = widget.resident!.bedNo;
  //     mobileCtrl.text = widget.resident?.mobile ?? "";
  //     // reviewExist = ctrl.review;
  //   }
  // }

  getHostels() async {
    try {
      // setState(() {
      // fetchHostelLoading = true;
      // });
      final res =
          await FBFireStore.hostels.where('blocked', isEqualTo: false).get();

      hostels = res.docs.map((e) => HostelModel.fromSnap(e)).toList();
      // setState(() {
      //   fetchHostelLoading = false;
      // });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  WingModel? getSelectedWing() {
    if (selectedHostel == null ||
        selectedFloorNew == null ||
        selectedRoomNew == null) {
      return null;
    }
    WingModel? selectedWing;
    selectedHostel?.wings.forEach((element) {
      // SORTED DATA TO GET SELECTED WING
      element.floors.sort((a, b) => a.fnumber.compareTo(b.fnumber));
      final tmp = element.floors[selectedFloorNew! - 1].rooms
          .firstWhereOrNull((element) {
        return element.rnumber == selectedRoomNew.toString();
      });

      if (tmp != null) {
        selectedWing = element;
      }
    });
    // print("selected Wing====== ${selectedWing?.name}");
    return selectedWing;
  }

  getDataFromHostelId() async {
    try {
      if (fetchSaveDataLoading) return;
      setState(() {
        fetchSaveDataLoading = true;
      });
      final res = await FBFireStore.savedusers
          .where('hostelId', isEqualTo: enrollidctrl.text.trim())
          // .where('validity',
          //     isGreaterThanOrEqualTo: DateTime(selectedDate!.year,
          //         selectedDate!.month, selectedDate!.day, 0, 0, 0))
          .where('validity',
              isGreaterThanOrEqualTo: DateTime(DateTime.now().year,
                  DateTime.now().month, DateTime.now().day - 1, 23, 59, 59))
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();
      await getHostels();
      if (res.docs.isNotEmpty) {
        savedData = UserSavedModel.fromSnap(res.docs.first);
        namectrl.text = savedData!.name;
        mobileCtrl.text = savedData!.mobile;
        selectedHostel = hostels.firstWhereOrNull((element) {
          return element.docId == savedData!.hostelDocId;
        });

        selectedFloorNew = savedData!.floor;
        selectedRoomNew = savedData!.roomNo;
        selectedBedNew = savedData!.bedNo;
      } else {
        savedData = null;
        showAppSnackBar('Unregistered enroll id');
      }

      setState(() {
        fetchSaveDataLoading = false;
      });
    } catch (e) {
      setState(() {
        fetchSaveDataLoading = false;
      });
      debugPrint(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold();
    // final ctrl = Get.find<HomeCtrl>();
/*  DO NOT DELETE
    // if (hostels.isNotEmpty) {
    //   // print(selectedHostel?.wings.first.floors.length);
    //   // total rooms on a floor
    //   // print(selectedHostel?.wings
    //   //     .map((e) => e.floors.first.rooms.length)
    //   //     .reduce((value, element) => value + element));
    //   // print(selectedHostel?.name);
    //   // print(selectedFloorNew);
    //   // print(selectedRoomNew);
    //   // print(selectedBedNew);

    //   // print(hostels.first.wings
    //   //     .map((e) => e.floors.first.rooms.length)
    //   //     .reduce((value, element) => value + element));
    //   // selectedHostel = hostels.first;
    //   // find selected room
    //   final rm = getSelectedWing()
    //       ?.floors[selectedFloorNew! - 1]
    //       .rooms
    //       .firstWhereOrNull(
    //           (element) => element.rnumber == selectedRoomNew.toString());
    //   // Bed count of selected room
    //   rm?.bedCount;
    // }
     */
    return Scaffold(
      // appBar: widget.emptyProfile ||
      //         (widget.resident != null && widget.resident!.deleted)
      //     ? null
      //     : AppBar(
      //         backgroundColor: Colors.white,
      //         surfaceTintColor: Colors.white,
      //         shadowColor: Colors.white,
      //         titleSpacing: 0,
      //         leading: IconButton(
      //             onPressed: () => context.pop(),
      //             icon: const Icon(Icons.arrow_back)),
      //         title: const Text(
      //           'Profile',
      //           style: TextStyle(
      //             color: Color(0xFF4F4F4F),
      //             // fontSize: 24,
      //             fontFamily: 'Poppins',
      //             fontWeight: FontWeight.w500,
      //           ),
      //         ),
      //       ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.fromLTRB(
              15, MediaQuery.paddingOf(context).top + 8, 15, 0),
          child: Center(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 350),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.emptyProfile) const SizedBox(height: 30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Add your Details',
                        style: TextStyle(
                          color: Color(0xFF333333),
                          fontSize: 32,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                          height: 0,
                        ),
                      ),
                      IconButton(
                          onPressed: () async {
                            await FBAuth.auth.signOut();
                            if (context.mounted) {
                              context.go(Routes.auth);
                            }
                          },
                          icon: const Icon(CupertinoIcons.square_arrow_right,
                              size: 32, color: Colors.red))
                    ],
                  ),
                  const SizedBox(height: 20),
                  InputFields(
                    ctrl: enrollidctrl,
                    txt1: 'Application No',
                    txt2: 'Enter your Application No',
                  ),
                  const SizedBox(height: 10),
                  ElevatedButton(
                      onPressed: () async {
                        await getDataFromHostelId();
                      },
                      style: ButtonStyle(
                        fixedSize: const WidgetStatePropertyAll(Size(100, 40)),
                        shape: WidgetStateProperty.all(RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5))),
                        backgroundColor:
                            const WidgetStatePropertyAll(Color(0xFF01B49E)),
                      ),
                      child: fetchSaveDataLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: Center(
                                child: CircularProgressIndicator(
                                    color: Colors.white, strokeWidth: 1.8),
                              ),
                            )
                          : const Text(
                              'Fetch',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w500,
                                height: 0,
                              ),
                            )),
                  if (savedData != null) ...[
                    const SizedBox(height: 20),
                    InputFields(
                      ctrl: namectrl,
                      txt1: 'Full Name',
                      txt2: 'Enter your Full Name',
                    ),

                    const SizedBox(height: 20),
                    InputFields(
                      ctrl: mobileCtrl,
                      txt1: 'Mobile No',
                      txt2: 'Enter your Mobile No',
                    ),
                    const SizedBox(height: 20),

                    // Select Hostel DropDown
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Select Hostel',
                          style: TextStyle(
                            color: Color.fromARGB(255, 0, 0, 0),
                            fontSize: 16,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                            height: 0,
                          ),
                        ),
                        const SizedBox(height: 10),
                        DropdownButtonHideUnderline(
                          child: DropdownButtonFormField<String>(
                            value: selectedHostel?.docId,
                            style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                color: Colors.black),
                            decoration: InputDecoration(

                                // disabledBorder: OutlineInputBorder(
                                //     borderSide: BorderSide(color: Colors.black\\)),
                                contentPadding: const EdgeInsets.symmetric(
                                    vertical: 15, horizontal: 12),
                                hintText: "Select Hostel",
                                border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(5))),
                            items: List.generate(
                                hostels.length,
                                (index) => DropdownMenuItem(
                                    value: hostels[index].docId,
                                    child: Text(hostels[index].name))),
                            onChanged: (value) {
                              if (value == null) return;
                              selectedHostel = hostels.firstWhereOrNull(
                                  (element) => element.docId == value);
                              selectedFloorNew = null;
                              selectedRoomNew = null;
                              selectedBedNew = null;
                              setState(() {});
                            },
                          ),
                        ),
                      ],
                    ),
                    /*  if (selectedHostel != null) const SizedBox(height: 20),
                  if (selectedHostel != null)

                    // Select floor DropDown
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Select Floor',
                          style: TextStyle(
                            color: Color.fromARGB(255, 0, 0, 0),
                            fontSize: 16,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                            height: 0,
                          ),
                        ),
                        const SizedBox(height: 10),
                        DropdownButtonHideUnderline(
                          child: DropdownButtonFormField(
                            style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                color: Colors.black),
                            decoration: InputDecoration(
                                enabled:
                                    widget.resident != null ? enabled : true,
                                contentPadding: const EdgeInsets.symmetric(
                                    vertical: 15, horizontal: 12),
                                hintText: 'Select Floor',
                                border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(5))),
                            /* items: selectedHostel?.wings.first.floors
                              .map((e) => DropdownMenuItem(
                                    value: e.fnumber,
                                    child: Text((e.fnumber).toString()),
                                  ))
                              .toList(), */
                            value: selectedFloorNew,
                            items: List.generate(
                                selectedHostel?.wings.first.floors.length ?? 0,
                                (index) => DropdownMenuItem(
                                      enabled: widget.resident != null
                                          ? enabled
                                          : true,
                                      value: index + 1,
                                      child: Text((index + 1).toString()),
                                    )),
                            onChanged: (value) {
                              if (value == null) return;
                              selectedFloorNew = value;
                              selectedRoomNew = null;
                              selectedBedNew = null;
                              setState(() {});
                            },
                          ),
                        ),
                      ],
                    ),
 */
                    if (selectedHostel != null) const SizedBox(height: 20),
                    if (selectedHostel != null)
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Select Floor',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 0, 0, 0),
                                    fontSize: 16,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w500,
                                    height: 0,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                DropdownButtonHideUnderline(
                                  child: DropdownButtonFormField(
                                    style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                        color: Colors.black),
                                    decoration: InputDecoration(
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                                vertical: 15, horizontal: 12),
                                        hintText: 'Floor',
                                        border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(5))),
                                    /* items: selectedHostel?.wings.first.floors
                              .map((e) => DropdownMenuItem(
                                    value: e.fnumber,
                                    child: Text((e.fnumber).toString()),
                                  ))
                              .toList(), */
                                    value: selectedFloorNew,
                                    items: List.generate(
                                        selectedHostel
                                                ?.wings.first.floors.length ??
                                            0,
                                        (index) => DropdownMenuItem(
                                              value: index + 1,
                                              child:
                                                  Text((index + 1).toString()),
                                            )),
                                    onChanged: (value) {
                                      if (value == null) return;
                                      selectedFloorNew = value;
                                      selectedRoomNew = null;
                                      selectedBedNew = null;
                                      setState(() {});
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (selectedFloorNew != null)
                            const SizedBox(width: 10),
                          if (selectedFloorNew != null)
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Select Room',
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 0, 0, 0),
                                      fontSize: 16,
                                      fontFamily: 'Poppins',
                                      fontWeight: FontWeight.w500,
                                      height: 0,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  DropdownButtonHideUnderline(
                                      child: DropdownButtonFormField(
                                    style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                        color: Colors.black),
                                    decoration: InputDecoration(
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                                vertical: 15, horizontal: 12),
                                        hintText: "Room",
                                        border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(5))),
                                    value: selectedRoomNew,
                                    items: List.generate(
                                        // selectedHostel?.wings.length ?? 0,
                                        // hostels.length,
                                        (selectedHostel?.wings
                                                .map((e) =>
                                                    e.floors.first.rooms.length)
                                                .reduce((value, element) =>
                                                    value + element)) ??
                                            0,
                                        (index) => DropdownMenuItem(
                                            value: (100 * selectedFloorNew!) +
                                                (index + 1),
                                            child: Text(
                                                ((100 * selectedFloorNew!) +
                                                        (index + 1))
                                                    .toString()))),
                                    onChanged: (value) {
                                      if (value == null) return;
                                      selectedRoomNew = value;
                                      selectedBedNew = null;

                                      setState(() {});
                                    },
                                  )),
                                ],
                              ),
                            ),
                          if (selectedRoomNew != null)
                            const SizedBox(width: 10),
                          if (selectedRoomNew != null)
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Select Bed',
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 0, 0, 0),
                                      fontSize: 16,
                                      fontFamily: 'Poppins',
                                      fontWeight: FontWeight.w500,
                                      height: 0,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  // Text(
                                  //     "${getSelectedWing()?.floors[selectedFloorNew! - 1].rooms.firstWhereOrNull((element) => element.rnumber == selectedRoomNew.toString())!.bedCount ?? 0}"),
                                  DropdownButtonHideUnderline(
                                    child: DropdownButtonFormField(
                                      style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w400,
                                          color: Colors.black),
                                      decoration: InputDecoration(
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  vertical: 15, horizontal: 12),
                                          hintText: "Bed",
                                          border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(5))),
                                      value: selectedBedNew,
                                      items: List.generate(
                                          getSelectedWing()
                                                  ?.floors[
                                                      selectedFloorNew! - 1]
                                                  .rooms
                                                  .firstWhereOrNull((element) =>
                                                      element.rnumber ==
                                                      selectedRoomNew
                                                          .toString())!
                                                  .bedCount ??
                                              0,
                                          (index) => DropdownMenuItem(
                                              value: index + 1,
                                              child: Text("Bed ${index + 1}"))),
                                      onChanged: (value) {
                                        if (value == null) return;
                                        selectedBedNew = value;
                                        uploadProof = false;
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),

                    if (uploadProof)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),
                          const Text(
                            'Upload Image',
                            style: TextStyle(
                              color: Color.fromARGB(255, 0, 0, 0),
                              fontSize: 16,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                              height: 0,
                            ),
                          ),
                          const Text(
                            'Note:- Please upload document for proof',
                            style: TextStyle(
                              color: Color.fromARGB(255, 0, 0, 0),
                              fontSize: 12,
                              height: 0,
                            ),
                          ),
                          const SizedBox(height: 10),
                          InkWell(
                            onTap: () {
                              showModalBottomSheet(
                                constraints:
                                    const BoxConstraints(maxHeight: 100),
                                context: context,
                                builder: (context) {
                                  return Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Expanded(
                                        child: InkWell(
                                          onTap: () async {
                                            Navigator.of(context).pop();
                                            await imagePicker(true);
                                            setState(() {});
                                          },
                                          child: const Padding(
                                            padding: EdgeInsets.all(8.0),
                                            child: Center(
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(
                                                    CupertinoIcons
                                                        .photo_on_rectangle,
                                                    size: 30,
                                                    color: Color.fromARGB(
                                                        255, 236, 198, 6),
                                                  ),
                                                  Text(
                                                    'Gallery',
                                                    style:
                                                        TextStyle(fontSize: 15),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      // const VerticalDivider(),
                                      Expanded(
                                        child: InkWell(
                                          onTap: () async {
                                            Navigator.of(context).pop();
                                            await getImageFromCamera(true);
                                            setState(() {});
                                          },
                                          child: const Padding(
                                            padding: EdgeInsets.all(8.0),
                                            child: Center(
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(
                                                    CupertinoIcons.camera,
                                                    size: 30,
                                                    color: Color.fromARGB(
                                                        255, 28, 99, 223),
                                                  ),
                                                  Text(
                                                    'Camera',
                                                    style:
                                                        TextStyle(fontSize: 15),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              );
                            },
                            child: Container(
                              height: 150,
                              clipBehavior: Clip.antiAlias,
                              width: double.maxFinite,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  border:
                                      Border.all(color: Colors.grey, width: 1)),
                              child: proofPickedImage != null
                                  ? Image.memory(
                                      proofPickedImage!.uInt8List,
                                      fit: BoxFit.cover,
                                    )
                                  : const Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Icon(CupertinoIcons.cloud_upload),
                                        SizedBox(height: 5),
                                        Text("Upload image"),
                                      ],
                                    ),
                            ),
                          ),
                        ],
                      ),

                    // if (widget.resident != null) const SizedBox(height: 20),
                    /*   if (widget.resident != null)
                    Row(
                      children: [
                        Expanded(
                          child: SizedBox(
                            height: 50,
                            child: ElevatedButton(
                                onPressed: () {
                                  if (reviewExist != null) {
                                    showAppSnackBar(
                                        'Unable to edit. Profile already under review');
                                    return;
                                  } else {
                                    setState(() {
                                      enabled = true;
                                    });
                                  }
                                },
                                style: ButtonStyle(
                                  overlayColor: const WidgetStatePropertyAll(
                                      Colors.white),
                                  surfaceTintColor:
                                      const WidgetStatePropertyAll(
                                          Colors.white),
                                  shape: WidgetStateProperty.all(
                                      RoundedRectangleBorder(
                                          side: const BorderSide(
                                              color: Color(0xFF01B49E)),
                                          borderRadius:
                                              BorderRadius.circular(5))),
                                  backgroundColor:
                                      const WidgetStatePropertyAll(
                                          Colors.white),
                                ),
                                child: const Text(
                                  'Edit Your Profile',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Color(0xFF01B49E),
                                    fontSize: 16,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w500,
                                    height: 0,
                                  ),
                                )),
                          ),
                        ),
                      ],
                    ),
 */

                    const SizedBox(height: 20),
                    onLoadSubmit
                        ? const Center(
                            child: SizedBox(
                              height: 30,
                              width: 30,
                              child: CircularProgressIndicator(),
                            ),
                          )
                        : Row(
                            children: [
                              Expanded(
                                child: SizedBox(
                                  height: 50,
                                  child: ElevatedButton(
                                      onPressed: onSave,
                                      style: ButtonStyle(
                                          shape: WidgetStateProperty.all(
                                              RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          5))),
                                          backgroundColor:
                                              const WidgetStatePropertyAll(
                                                  Color(0xFF01B49E))),
                                      child: const Text(
                                        'Save Your Profile',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontFamily: 'Poppins',
                                          fontWeight: FontWeight.w500,
                                          height: 0,
                                        ),
                                      )),
                                ),
                              ),
                            ],
                          ),
                    const SizedBox(height: 20),
                  ]
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Future<String?> _getId() async {
  //   var deviceInfo = DeviceInfoPlugin();
  //   if (Platform.isIOS) {
  //     // import 'dart:io'
  //     var iosDeviceInfo = await deviceInfo.iosInfo;
  //     return iosDeviceInfo.identifierForVendor; // unique ID on iOS
  //   } else if (Platform.isAndroid) {
  //     var androidDeviceInfo = await deviceInfo.androidInfo;

  //     return androidDeviceInfo.an; // unique ID on Android
  //   }
  // }

  onSave() async {
    try {
      if (onLoadSubmit) return;
      setState(() {
        onLoadSubmit = true;
      });

      // getExistingResident();
      // return;

      if (selectedHostel == null ||
          namectrl.text.isEmpty ||
          mobileCtrl.text.isEmpty ||
          enrollidctrl.text.isEmpty ||
          selectedFloorNew == null ||
          selectedRoomNew == null ||
          selectedBedNew == null) {
        setState(() {
          onLoadSubmit = false;
        });
        showAppSnackBar('All Feilds are required');

        return;
      }

      if (!checkAllSame() && proofPickedImage == null) {
        setState(() {
          uploadProof = true;
          onLoadSubmit = false;
        });
        showAppSnackBar('Please upload proof');
        return;
      }

      // bool enrollContains = await FBFireStore.enrollments
      //     .where('enrollmentList', arrayContains: enrollidctrl.text.trim())
      //     .get()
      //     .then((value) => value.docs.isNotEmpty);
      // if (!enrollContains) {
      //   setState(() {
      //     onLoadSubmit = false;
      //   });
      //   showAppSnackBar('Unregistered enroll id');
      //   return;
      // }
      // final mobile = mobileCtrl.text.contains('+91')
      //     ? mobileCtrl.text
      //     : '+91${mobileCtrl.text}';
      // if (mobile.length != 13) {
      //   setState(() {
      //     onLoadSubmit = false;
      //   });
      //   showAppSnackBar('Please enter valid mobile number');
      //   return;
      // }
      // if (uploadProof && proofPickedImage == null) {
      //   setState(() {
      //     onLoadSubmit = false;
      //   });
      //   showAppSnackBar('Please upload proof');
      //   return;
      // }

      if (!uploadProof) {
        await getExistingResident();
      }

      // if (!uploadProof) {
      // if (existingDataDocId != null && context.mounted) {
      //   setState(() {
      //     onLoadSubmit = false;
      //   });
      //   return showDialog(
      //     context: context,
      //     builder: (context) {
      //       return AlertDialog(
      //         title: const Text(
      //           'Data Exist',
      //           style: TextStyle(fontSize: 22),
      //         ),
      //         content: const Text(
      //             'Entered application no/selected hostel bed exist in our data. Request to upload proof, if entered data is correct.'),
      //         actions: [
      //           TextButton(
      //               onPressed: () {
      //                 if (context.mounted) {}
      //                 Navigator.of(context).pop();
      //                 setState(() {
      //                   uploadProof = true;
      //                 });
      //               },
      //               child: const Text('Upload Proof')),
      //           TextButton(
      //               onPressed: () => Navigator.of(context).pop(),
      //               child: const Text('Re-enter Data')),
      //         ],
      //       );
      //     },
      //   );
      // }
      // }

      bool previouslyReviewExist = await FBFireStore.review
          .where('status', isEqualTo: 'Active')
          .where('uId', isEqualTo: FBAuth.auth.currentUser?.uid)
          .get()
          .then((value) {
        if (value.docs.isEmpty) {
          return false;
        } else {
          return true;
        }
      });
      if (previouslyReviewExist) {
        setState(() {
          onLoadSubmit = false;
        });
        showAppSnackBar('Request already active.');
        return;
      }

      // return;
      // setState(() {
      //   onLoadSubmit = true;
      // });

      final proofImageUrl = proofPickedImage != null
          ? await uploadProofFile(proofPickedImage!)
          : '';
      final profileImageUrl = profilePickedImage != null
          ? await uploadProofFile(profilePickedImage!)
          : null;
      final currentDeviceId = await UniqueIdentifier.serial;
      final packageInfo = await PackageInfo.fromPlatform();
      // const currentDeviceId = null;
      // await GetMac.macAddress;

      final data = <String, dynamic>{
        'name': namectrl.text,
        'hostelDocId': selectedHostel!.docId,
        'hostelId': enrollidctrl.text,
        'floor': selectedFloorNew,
        'roomNo': selectedRoomNew,
        'bedNo': selectedBedNew,
        'blocked': false,
        'wing': getSelectedWing()!.name,
        'deleted': false,
        'profileImage': profileImageUrl,
        'mobile': mobileCtrl.text,
        'deviceId': currentDeviceId,
        'createdAt': FieldValue.serverTimestamp(),
      };

      final reviewData = <String, dynamic>{
        'name': namectrl.text,
        'hostelDocId': selectedHostel!.docId,
        'hostelId': enrollidctrl.text,
        'wing': getSelectedWing()!.name,
        'floor': selectedFloorNew,
        'roomNo': selectedRoomNew,
        'bedNo': selectedBedNew,
        'blocked': false,
        'mobile': mobileCtrl.text,
        'imageUrl': proofImageUrl,
        'uId': FBAuth.auth.currentUser?.uid,
        'email': FBAuth.auth.currentUser?.email,
        'createdAt': FieldValue.serverTimestamp(),
        'rejectionReason': '',
        'status': 'Active',
        'mobileVersion': packageInfo.buildNumber,
        'profileUnderReview': true,
      };

      final reviewDoc = FBFireStore.review.doc();
      uploadProof
          ? await FBFireStore.review.doc(reviewDoc.id).set(reviewData)
          : await FBFireStore.residents
              .doc(FBAuth.auth.currentUser?.uid)
              .set(data);
      if (uploadProof) {
        initiateActivity(
          uId: reviewData['uId'],
          type: ActivityType.profileRequest,
          typeDocId: reviewDoc.id,
          desc:
              'Your profile change request has been raised on ${DateTime.now().convertToDDMMYY()} at ${DateTime.now().goodTime()}.',
          userType: UserTypes.resident,
          title: 'Profile Change Request',
        );
        showAppSnackBar('Review request sent!');
      } else {
        await FBFireStore.residents
            .doc(existingDataDocId)
            .update({'deleted': true});
      }

      if (mounted) {
        setState(() {
          onLoadSubmit = false;
        });

        context.go(homeRoute);
      }
    } on Exception catch (e) {
      debugPrint(e.toString());
      setState(() {
        onLoadSubmit = false;
      });
    }
  }

  bool checkAllSame() {
    return savedData!.name == namectrl.text &&
        savedData!.hostelId == enrollidctrl.text &&
        savedData!.mobile == mobileCtrl.text &&
        savedData!.hostelDocId == selectedHostel!.docId &&
        savedData!.floor == selectedFloorNew &&
        savedData!.roomNo == selectedRoomNew &&
        savedData!.bedNo == selectedBedNew;
  }

  getExistingResident() async {
    existingDataDocId = await FBFireStore.residents
        .where(
          Filter.or(
              Filter.and(
                  Filter('hostelDocId', isEqualTo: selectedHostel?.docId),
                  Filter('floor', isEqualTo: selectedFloorNew),
                  Filter('roomNo', isEqualTo: selectedRoomNew),
                  Filter('bedNo', isEqualTo: selectedBedNew)),
              Filter('hostelId', isEqualTo: enrollidctrl.text)),
        )
        .where('deleted', isEqualTo: false)
        // .where('floor', isEqualTo: widget.reviewData?.floor)
        // .where('roomNo', isEqualTo: widget.reviewData?.roomNo)
        // .where('bedNo', isEqualTo: widget.reviewData?.bedNo)
        .get()
        .then((value) {
      final docss = value.docs;
      // return null;
      // print(ResidentModel.fromJson(docss.first.data()));

      return docss.isNotEmpty ? docss.first.id : null;
    });
  }

  Future imagePicker(bool proof) async {
    final res =
        await ImagePickerService().pickImageNew(context, useCompressor: true);
    if (res != null) {
      proof ? proofPickedImage = res : profilePickedImage = res;
    }
    setState(() {});
  }

  Future getImageFromCamera(bool proof) async {
    final resCam = await ImagePickerService()
        .pickImageNewCamera(context, useCompressor: true);

    setState(() {
      if (resCam != null) {
        proof ? proofPickedImage = resCam : profilePickedImage = resCam;
      }
    });
  }
}
 */
class EditProfilePage extends StatefulWidget {
  const EditProfilePage({super.key, this.resident, this.emptyProfile = false});
  final ResidentModel? resident;
  final bool emptyProfile;

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  ResidentModel? existingData;
  SelectedImage? proofPickedImage;
  SelectedImage? profilePickedImage;
  bool loading = false;
  bool onLoadSubmit = false;
  List<HostelModel> hostels = [];
  HostelModel? selectedHostel;
  int? selectedFloorNew;
  int? selectedRoomNew;
  int? selectedBedNew;
  String? dropdownValueFloor;
  String? dropdownValueHostel;
  String? dropdownValueRoom;
  String? dropdownValueBednum;
  final namectrl = TextEditingController();
  final enrollidctrl = TextEditingController();
  final mobileCtrl = TextEditingController();
  bool uploadProof = false;
  ReviewModel? reviewExist;

  @override
  void initState() {
    super.initState();
    getHostels();
    assignValue();
  }

  assignValue() {
    if (widget.resident != null) {
      final ctrl = Get.find<HomeCtrl>();
      namectrl.text = widget.resident!.name;
      enrollidctrl.text = widget.resident!.hostelId;
      selectedHostel = ctrl.hostelModel;
      selectedFloorNew = widget.resident!.floor;
      selectedRoomNew = widget.resident!.roomNo;
      selectedBedNew = widget.resident!.bedNo;
      mobileCtrl.text = widget.resident?.mobile ?? "";
      reviewExist = ctrl.review;
    }
  }

  getHostels() async {
    try {
      return;
      setState(() {
        loading = true;
      });
      final res =
          await FBFireStore.hostels.where('blocked', isEqualTo: false).get();

      hostels = res.docs.map((e) => HostelModel.fromSnap(e)).toList();
      setState(() {
        loading = false;
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  WingModel? getSelectedWing() {
    if (selectedHostel == null ||
        selectedFloorNew == null ||
        selectedRoomNew == null) {
      return null;
    }
    WingModel? selectedWing;
    selectedHostel?.wings.forEach((element) {
      // SORTED DATA TO GET SELECTED WING
      element.floors.sort((a, b) => a.fnumber.compareTo(b.fnumber));
      final tmp = element.floors[selectedFloorNew! - 1].rooms
          .firstWhereOrNull((element) {
        return element.rnumber == selectedRoomNew.toString();
      });

      if (tmp != null) {
        selectedWing = element;
      }
    });
    // print("selected Wing====== ${selectedWing?.name}");
    return selectedWing;
  }

  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
/*  DO NOT DELETE
    // if (hostels.isNotEmpty) {
    //   // print(selectedHostel?.wings.first.floors.length);
    //   // total rooms on a floor
    //   // print(selectedHostel?.wings
    //   //     .map((e) => e.floors.first.rooms.length)
    //   //     .reduce((value, element) => value + element));
    //   // print(selectedHostel?.name);
    //   // print(selectedFloorNew);
    //   // print(selectedRoomNew);
    //   // print(selectedBedNew);

    //   // print(hostels.first.wings
    //   //     .map((e) => e.floors.first.rooms.length)
    //   //     .reduce((value, element) => value + element));
    //   // selectedHostel = hostels.first;
    //   // find selected room
    //   final rm = getSelectedWing()
    //       ?.floors[selectedFloorNew! - 1]
    //       .rooms
    //       .firstWhereOrNull(
    //           (element) => element.rnumber == selectedRoomNew.toString());
    //   // Bed count of selected room
    //   rm?.bedCount;
    // }
     */
    return Scaffold(
      appBar: widget.emptyProfile ||
              (widget.resident != null && widget.resident!.deleted)
          ? null
          : AppBar(
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              shadowColor: Colors.white,
              titleSpacing: 0,
              leading: IconButton(
                  onPressed: () => context.pop(),
                  icon: const Icon(Icons.arrow_back)),
              title: const Text(
                'Profile',
                style: TextStyle(
                  color: Color(0xFF4F4F4F),
                  // fontSize: 24,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.fromLTRB(
              15, MediaQuery.paddingOf(context).top + 8, 15, 0),
          child: Center(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 350),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  widget.emptyProfile
                      ? Center(
                          child: SizedBox(
                            height: 120,
                            width: 120,
                            child: Stack(
                              children: [
                                Container(
                                  width: 120,
                                  height: 120,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    // color:
                                    //     widget.emptyProfile ? null : themeColor,
                                  ),
                                  child:
                                      // widget.emptyProfile &&
                                      profilePickedImage == null
                                          ? Image.asset(
                                              'assets/images/profile.png',
                                              fit: BoxFit.cover,
                                            )
                                          : profilePickedImage != null
                                              ? Image.memory(
                                                  profilePickedImage!.uInt8List)
                                              : CachedNetworkImage(
                                                  imageUrl: ctrl.residentModel
                                                          ?.profileImage ??
                                                      "",
                                                  fit: BoxFit.cover,
                                                  errorWidget:
                                                      (context, url, error) {
                                                    return Center(
                                                      child: Text(
                                                        ctrl.residentModel!
                                                            .name[0]
                                                            .toUpperCase(),
                                                        textAlign:
                                                            TextAlign.center,
                                                        style: const TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 40),
                                                      ),
                                                    );
                                                  },
                                                ),
                                ),
                                Align(
                                  // alignment: Alignment.bottomRight,
                                  alignment: const Alignment(1.25, 0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      CircleAvatar(
                                        radius: 20,
                                        backgroundColor: themeColor,
                                        child: IconButton(
                                          onPressed: () async {
                                            showModalBottomSheet(
                                              backgroundColor: Colors.white,
                                              constraints: const BoxConstraints(
                                                  maxHeight: 100),
                                              context: context,
                                              builder: (context) {
                                                return Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Expanded(
                                                      child: InkWell(
                                                        onTap: () async {
                                                          Navigator.of(context)
                                                              .pop();
                                                          await imagePicker(
                                                              false);
                                                          setState(() {});
                                                        },
                                                        child: const Padding(
                                                          padding:
                                                              EdgeInsets.all(
                                                                  8.0),
                                                          child: Center(
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              children: [
                                                                Icon(
                                                                  CupertinoIcons
                                                                      .photo_on_rectangle,
                                                                  size: 30,
                                                                  color: Color
                                                                      .fromARGB(
                                                                          255,
                                                                          236,
                                                                          198,
                                                                          6),
                                                                ),
                                                                Text(
                                                                  'Gallery',
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          15),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    // const VerticalDivider(),
                                                    Expanded(
                                                      child: InkWell(
                                                        onTap: () async {
                                                          Navigator.of(context)
                                                              .pop();
                                                          await getImageFromCamera(
                                                              false);
                                                          setState(() {});
                                                        },
                                                        child: const Padding(
                                                          padding:
                                                              EdgeInsets.all(
                                                                  8.0),
                                                          child: Center(
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              children: [
                                                                Icon(
                                                                  CupertinoIcons
                                                                      .camera,
                                                                  size: 30,
                                                                  color: Color
                                                                      .fromARGB(
                                                                          255,
                                                                          28,
                                                                          99,
                                                                          223),
                                                                ),
                                                                Text(
                                                                  'Camera',
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          15),
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              },
                                            );
                                          },
                                          icon: const Icon(Icons.edit_outlined,
                                              color: Colors.white, size: 23),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      : const SizedBox(),
                  if (widget.emptyProfile) const SizedBox(height: 30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        !(widget.emptyProfile)
                            ? 'Edit your profile'
                            : 'Add your Details',
                        style: const TextStyle(
                          color: Color(0xFF333333),
                          fontSize: 32,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                          height: 0,
                        ),
                      ),
                      if ((widget.emptyProfile &&
                          (widget.resident?.deleted ?? true)))
                        IconButton(
                            onPressed: () async {
                              await FBAuth.auth.signOut();
                              if (context.mounted) {
                                context.go(Routes.auth);
                              }
                            },
                            icon: const Icon(CupertinoIcons.square_arrow_right,
                                size: 32, color: Colors.red))
                    ],
                  ),
                  if (widget.resident != null &&
                      widget.resident?.deleted == true)
                    const SizedBox(height: 20),
                  if (widget.resident != null &&
                      widget.resident?.deleted == true)
                    const Text(
                      '*Your profile has been deleted. Request you to re-enter your details.',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 13,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w600,
                        height: 0,
                      ),
                    ),
                  const SizedBox(height: 20),
                  InputFields(
                    ctrl: namectrl,
                    txt1: 'Full Name',
                    txt2: 'Enter your Full Name',
                  ),

                  const SizedBox(height: 20),

                  InputFields(
                    ctrl: enrollidctrl,
                    txt1: 'Application No',
                    txt2: 'Enter your Application No',
                  ),
                  const SizedBox(height: 20),
                  InputFields(
                    ctrl: mobileCtrl,
                    txt1: 'Mobile No',
                    txt2: 'Enter your Mobile No',
                  ),
                  const SizedBox(height: 20),

                  // Select Hostel DropDown
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Select Hostel',
                        style: TextStyle(
                          color: Color.fromARGB(255, 0, 0, 0),
                          fontSize: 16,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                          height: 0,
                        ),
                      ),
                      const SizedBox(height: 10),
                      DropdownButtonHideUnderline(
                        child: DropdownButtonFormField<String>(
                          value: selectedHostel?.docId,
                          style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              color: Colors.black),
                          decoration: InputDecoration(

                              // disabledBorder: OutlineInputBorder(
                              //     borderSide: BorderSide(color: Colors.black\\)),
                              contentPadding: const EdgeInsets.symmetric(
                                  vertical: 15, horizontal: 12),
                              hintText: "Select Hostel",
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(5))),
                          items: List.generate(
                              hostels.length,
                              (index) => DropdownMenuItem(
                                  value: hostels[index].docId,
                                  child: Text(hostels[index].name))),
                          onChanged: (value) {
                            if (value == null) return;
                            selectedHostel = hostels.firstWhereOrNull(
                                (element) => element.docId == value);
                            selectedFloorNew = null;
                            selectedRoomNew = null;
                            selectedBedNew = null;
                            setState(() {});
                          },
                        ),
                      ),
                    ],
                  ),
                  /*  if (selectedHostel != null) const SizedBox(height: 20),
                  if (selectedHostel != null)

                    // Select floor DropDown
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Select Floor',
                          style: TextStyle(
                            color: Color.fromARGB(255, 0, 0, 0),
                            fontSize: 16,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                            height: 0,
                          ),
                        ),
                        const SizedBox(height: 10),
                        DropdownButtonHideUnderline(
                          child: DropdownButtonFormField(
                            style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                color: Colors.black),
                            decoration: InputDecoration(
                                enabled:
                                    widget.resident != null ? enabled : true,
                                contentPadding: const EdgeInsets.symmetric(
                                    vertical: 15, horizontal: 12),
                                hintText: 'Select Floor',
                                border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(5))),
                            /* items: selectedHostel?.wings.first.floors
                              .map((e) => DropdownMenuItem(
                                    value: e.fnumber,
                                    child: Text((e.fnumber).toString()),
                                  ))
                              .toList(), */
                            value: selectedFloorNew,
                            items: List.generate(
                                selectedHostel?.wings.first.floors.length ?? 0,
                                (index) => DropdownMenuItem(
                                      enabled: widget.resident != null
                                          ? enabled
                                          : true,
                                      value: index + 1,
                                      child: Text((index + 1).toString()),
                                    )),
                            onChanged: (value) {
                              if (value == null) return;
                              selectedFloorNew = value;
                              selectedRoomNew = null;
                              selectedBedNew = null;
                              setState(() {});
                            },
                          ),
                        ),
                      ],
                    ),
 */
                  if (selectedHostel != null) const SizedBox(height: 20),
                  if (selectedHostel != null)
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Select Floor',
                                style: TextStyle(
                                  color: Color.fromARGB(255, 0, 0, 0),
                                  fontSize: 16,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w500,
                                  height: 0,
                                ),
                              ),
                              const SizedBox(height: 10),
                              DropdownButtonHideUnderline(
                                child: DropdownButtonFormField(
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.black),
                                  decoration: InputDecoration(
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 15, horizontal: 12),
                                      hintText: 'Floor',
                                      border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(5))),
                                  /* items: selectedHostel?.wings.first.floors
                              .map((e) => DropdownMenuItem(
                                    value: e.fnumber,
                                    child: Text((e.fnumber).toString()),
                                  ))
                              .toList(), */
                                  value: selectedFloorNew,
                                  items: List.generate(
                                      selectedHostel
                                              ?.wings.first.floors.length ??
                                          0,
                                      (index) => DropdownMenuItem(
                                            value: index + 1,
                                            child: Text((index + 1).toString()),
                                          )),
                                  onChanged: (value) {
                                    if (value == null) return;
                                    selectedFloorNew = value;
                                    selectedRoomNew = null;
                                    selectedBedNew = null;
                                    setState(() {});
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (selectedFloorNew != null) const SizedBox(width: 10),
                        if (selectedFloorNew != null)
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Select Room',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 0, 0, 0),
                                    fontSize: 16,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w500,
                                    height: 0,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                DropdownButtonHideUnderline(
                                    child: DropdownButtonFormField(
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.black),
                                  decoration: InputDecoration(
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 15, horizontal: 12),
                                      hintText: "Room",
                                      border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(5))),
                                  value: selectedRoomNew,
                                  items: List.generate(
                                      // selectedHostel?.wings.length ?? 0,
                                      // hostels.length,
                                      (selectedHostel?.wings
                                              .map((e) =>
                                                  e.floors.first.rooms.length)
                                              .reduce((value, element) =>
                                                  value + element)) ??
                                          0,
                                      (index) => DropdownMenuItem(
                                          value: (100 * selectedFloorNew!) +
                                              (index + 1),
                                          child: Text(
                                              ((100 * selectedFloorNew!) +
                                                      (index + 1))
                                                  .toString()))),
                                  onChanged: (value) {
                                    if (value == null) return;
                                    selectedRoomNew = value;
                                    selectedBedNew = null;

                                    setState(() {});
                                  },
                                )),
                              ],
                            ),
                          ),
                        if (selectedRoomNew != null) const SizedBox(width: 10),
                        if (selectedRoomNew != null)
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Select Bed',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 0, 0, 0),
                                    fontSize: 16,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w500,
                                    height: 0,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                // Text(
                                //     "${getSelectedWing()?.floors[selectedFloorNew! - 1].rooms.firstWhereOrNull((element) => element.rnumber == selectedRoomNew.toString())!.bedCount ?? 0}"),
                                DropdownButtonHideUnderline(
                                  child: DropdownButtonFormField(
                                    style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                        color: Colors.black),
                                    decoration: InputDecoration(
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                                vertical: 15, horizontal: 12),
                                        hintText: "Bed",
                                        border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(5))),
                                    value: selectedBedNew,
                                    items: List.generate(
                                        getSelectedWing()
                                                ?.floors[selectedFloorNew! - 1]
                                                .rooms
                                                .firstWhereOrNull((element) =>
                                                    element.rnumber ==
                                                    selectedRoomNew.toString())!
                                                .bedCount ??
                                            0,
                                        (index) => DropdownMenuItem(
                                            value: index + 1,
                                            child: Text("Bed ${index + 1}"))),
                                    onChanged: (value) {
                                      if (value == null) return;
                                      selectedBedNew = value;
                                      uploadProof = false;
                                      setState(() {});
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),

                  if (uploadProof || !widget.emptyProfile)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),
                        const Text(
                          'Upload Image',
                          style: TextStyle(
                            color: Color.fromARGB(255, 0, 0, 0),
                            fontSize: 16,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                            height: 0,
                          ),
                        ),
                        const Text(
                          'Note:- Please upload document for proof',
                          style: TextStyle(
                            color: Color.fromARGB(255, 0, 0, 0),
                            fontSize: 12,
                            height: 0,
                          ),
                        ),
                        const SizedBox(height: 10),
                        InkWell(
                          onTap: () {
                            showModalBottomSheet(
                              constraints: const BoxConstraints(maxHeight: 100),
                              context: context,
                              builder: (context) {
                                return Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Expanded(
                                      child: InkWell(
                                        onTap: () async {
                                          Navigator.of(context).pop();
                                          await imagePicker(true);
                                          setState(() {});
                                        },
                                        child: const Padding(
                                          padding: EdgeInsets.all(8.0),
                                          child: Center(
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  CupertinoIcons
                                                      .photo_on_rectangle,
                                                  size: 30,
                                                  color: Color.fromARGB(
                                                      255, 236, 198, 6),
                                                ),
                                                Text(
                                                  'Gallery',
                                                  style:
                                                      TextStyle(fontSize: 15),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    // const VerticalDivider(),
                                    Expanded(
                                      child: InkWell(
                                        onTap: () async {
                                          Navigator.of(context).pop();
                                          await getImageFromCamera(true);
                                          setState(() {});
                                        },
                                        child: const Padding(
                                          padding: EdgeInsets.all(8.0),
                                          child: Center(
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  CupertinoIcons.camera,
                                                  size: 30,
                                                  color: Color.fromARGB(
                                                      255, 28, 99, 223),
                                                ),
                                                Text(
                                                  'Camera',
                                                  style:
                                                      TextStyle(fontSize: 15),
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            );
                          },
                          child: Container(
                            height: 150,
                            clipBehavior: Clip.antiAlias,
                            width: double.maxFinite,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5),
                                border:
                                    Border.all(color: Colors.grey, width: 1)),
                            child: proofPickedImage != null
                                ? Image.memory(
                                    proofPickedImage!.uInt8List,
                                    fit: BoxFit.cover,
                                  )
                                : const Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Icon(CupertinoIcons.cloud_upload),
                                      SizedBox(height: 5),
                                      Text("Upload image"),
                                    ],
                                  ),
                          ),
                        ),
                      ],
                    ),

                  // if (widget.resident != null) const SizedBox(height: 20),
                  /*   if (widget.resident != null)
                    Row(
                      children: [
                        Expanded(
                          child: SizedBox(
                            height: 50,
                            child: ElevatedButton(
                                onPressed: () {
                                  if (reviewExist != null) {
                                    showAppSnackBar(
                                        'Unable to edit. Profile already under review');
                                    return;
                                  } else {
                                    setState(() {
                                      enabled = true;
                                    });
                                  }
                                },
                                style: ButtonStyle(
                                  overlayColor: const WidgetStatePropertyAll(
                                      Colors.white),
                                  surfaceTintColor:
                                      const WidgetStatePropertyAll(
                                          Colors.white),
                                  shape: WidgetStateProperty.all(
                                      RoundedRectangleBorder(
                                          side: const BorderSide(
                                              color: Color(0xFF01B49E)),
                                          borderRadius:
                                              BorderRadius.circular(5))),
                                  backgroundColor:
                                      const WidgetStatePropertyAll(
                                          Colors.white),
                                ),
                                child: const Text(
                                  'Edit Your Profile',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Color(0xFF01B49E),
                                    fontSize: 16,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w500,
                                    height: 0,
                                  ),
                                )),
                          ),
                        ),
                      ],
                    ),
 */

                  const SizedBox(height: 20),
                  onLoadSubmit
                      ? const Center(
                          child: SizedBox(
                            height: 30,
                            width: 30,
                            child: CircularProgressIndicator(),
                          ),
                        )
                      : Row(
                          children: [
                            Expanded(
                              child: SizedBox(
                                height: 50,
                                child: ElevatedButton(
                                    onPressed: () async {
                                      try {
                                        if (onLoadSubmit) return;
                                        setState(() {
                                          onLoadSubmit = true;
                                        });
                                        // getExistingResident();
                                        // return;
                                        if (selectedHostel == null ||
                                            namectrl.text.isEmpty ||
                                            mobileCtrl.text.isEmpty ||
                                            enrollidctrl.text.isEmpty ||
                                            selectedFloorNew == null ||
                                            selectedRoomNew == null ||
                                            selectedBedNew == null) {
                                          setState(() {
                                            onLoadSubmit = false;
                                          });
                                          showAppSnackBar(
                                              'All Feilds are required');
                                          return;
                                        }

                                        bool enrollContains = await FBFireStore
                                            .enrollments
                                            .where('enrollmentList',
                                                arrayContains:
                                                    enrollidctrl.text.trim())
                                            .get()
                                            .then((value) =>
                                                value.docs.isNotEmpty);
                                        if (!enrollContains) {
                                          setState(() {
                                            onLoadSubmit = false;
                                          });
                                          showAppSnackBar(
                                              'Unregistered enroll id');
                                          return;
                                        }
                                        final mobile =
                                            mobileCtrl.text.contains('+91')
                                                ? mobileCtrl.text
                                                : '+91${mobileCtrl.text}';
                                        if (mobile.length != 13) {
                                          setState(() {
                                            onLoadSubmit = false;
                                          });
                                          showAppSnackBar(
                                              'Please enter valid mobile number');
                                          return;
                                        }
                                        if ((uploadProof ||
                                                !widget.emptyProfile) &&
                                            proofPickedImage == null) {
                                          setState(() {
                                            onLoadSubmit = false;
                                          });
                                          showAppSnackBar(
                                              'Please upload proof');
                                          return;
                                        }

                                        if (!uploadProof) {
                                          await getExistingResident();
                                        }

                                        if (!uploadProof) {
                                          if (existingData != null
                                              //  &&
                                              //     context.mounted
                                              ) {
                                            setState(() {
                                              onLoadSubmit = false;
                                            });
                                            return showDialog(
                                              context: context,
                                              builder: (context) {
                                                return AlertDialog(
                                                  title: const Text(
                                                    'Data Exist',
                                                    style:
                                                        TextStyle(fontSize: 22),
                                                  ),
                                                  content: const Text(
                                                      'Entered application no/selected hostel bed exist in our data. Request to upload proof, if entered data is correct.'),
                                                  actions: [
                                                    TextButton(
                                                        onPressed: () {
                                                          if (context
                                                              .mounted) {}
                                                          Navigator.of(context)
                                                              .pop();
                                                          setState(() {
                                                            uploadProof = true;
                                                          });
                                                        },
                                                        child: const Text(
                                                            'Upload Proof')),
                                                    TextButton(
                                                        onPressed: () =>
                                                            Navigator.of(
                                                                    context)
                                                                .pop(),
                                                        child: const Text(
                                                            'Re-enter Data')),
                                                  ],
                                                );
                                              },
                                            );
                                          }
                                        }
                                        bool previouslyReviewExist =
                                            await FBFireStore.review
                                                .where('status',
                                                    isEqualTo: 'Active')
                                                .where(
                                                  'uId',
                                                  isEqualTo: FBAuth
                                                      .auth.currentUser?.uid,
                                                )
                                                .get()
                                                .then((value) {
                                          if (value.docs.isEmpty) {
                                            return false;
                                          } else {
                                            return true;
                                          }
                                        });
                                        if (previouslyReviewExist) {
                                          setState(() {
                                            onLoadSubmit = false;
                                          });
                                          showAppSnackBar(
                                              'Request already active.');
                                          return;
                                        }

                                        // return;
                                        // setState(() {
                                        //   onLoadSubmit = true;
                                        // });

                                        final proofImageUrl =
                                            proofPickedImage != null
                                                ? await uploadProofFile(
                                                    proofPickedImage!)
                                                : '';
                                        final profileImageUrl =
                                            profilePickedImage != null
                                                ? await uploadProofFile(
                                                    profilePickedImage!)
                                                : null;
                                        final currentDeviceId =
                                            await UniqueIdentifier.serial;
                                        final packageInfo =
                                            await PackageInfo.fromPlatform();
                                        // const currentDeviceId = null;
                                        // await GetMac.macAddress;

                                        final data = <String, dynamic>{
                                          'name': namectrl.text,
                                          'hostelDocId': selectedHostel!.docId,
                                          'hostelId': enrollidctrl.text,
                                          'floor': selectedFloorNew,
                                          'roomNo': selectedRoomNew,
                                          'bedNo': selectedBedNew,
                                          'blocked': false,
                                          'wing': getSelectedWing()!.name,
                                          'deleted': false,
                                          'profileImage': profileImageUrl,
                                          'mobile': mobile,
                                          'deviceId': currentDeviceId,
                                          'createdAt':
                                              FieldValue.serverTimestamp(),
                                        };

                                        final reviewData = <String, dynamic>{
                                          'name': namectrl.text,
                                          'hostelDocId': selectedHostel!.docId,
                                          'hostelId': enrollidctrl.text,
                                          'wing': getSelectedWing()!.name,
                                          'floor': selectedFloorNew,
                                          'roomNo': selectedRoomNew,
                                          'bedNo': selectedBedNew,
                                          'blocked': false,
                                          'mobile': mobile,
                                          'imageUrl': proofImageUrl,
                                          'uId': FBAuth.auth.currentUser?.uid,
                                          'email':
                                              FBAuth.auth.currentUser?.email,
                                          'createdAt':
                                              FieldValue.serverTimestamp(),
                                          'rejectionReason': '',
                                          'status': 'Active',
                                          'mobileVersion':
                                              packageInfo.buildNumber,
                                          'profileUnderReview': true,
                                        };

                                        final reviewDoc =
                                            FBFireStore.review.doc();
                                        widget.resident != null
                                            ? await FBFireStore.review
                                                .doc(reviewDoc.id)
                                                .set(reviewData)
                                            : uploadProof
                                                ? FBFireStore.review
                                                    .doc(reviewDoc.id)
                                                    .set(reviewData)
                                                : await FBFireStore.residents
                                                    .doc(FBAuth
                                                        .auth.currentUser?.uid)
                                                    .set(data);
                                        if (widget.resident != null ||
                                            uploadProof) {
                                          initiateActivity(
                                            uId: reviewData['uId'],
                                            type: ActivityType.profileRequest,
                                            typeDocId: reviewDoc.id,
                                            desc:
                                                'Your profile change request has been raised on ${DateTime.now().convertToDDMMYY()} at ${DateTime.now().goodTime()}.',
                                            userType: UserTypes.resident,
                                            title: 'Profile Change Request',
                                          );
                                        }
                                        if (context.mounted) {
                                          widget.resident != null
                                              ? context.pop()
                                              : context.go(homeRoute);
                                        }
                                        if (widget.resident != null ||
                                            uploadProof) {
                                          showAppSnackBar(
                                              'Review request sent!');
                                        }
                                        setState(() {
                                          onLoadSubmit = false;
                                        });
                                      } on Exception catch (e) {
                                        debugPrint(e.toString());
                                        setState(() {
                                          onLoadSubmit = false;
                                        });
                                      }
                                    },
                                    style: ButtonStyle(
                                        shape: WidgetStateProperty.all(
                                            RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(5))),
                                        backgroundColor:
                                            const WidgetStatePropertyAll(
                                                Color(0xFF01B49E))),
                                    child: Text(
                                      widget.resident != null
                                          ? 'Send Request'
                                          : 'Save Your Profile',
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w500,
                                        height: 0,
                                      ),
                                    )),
                              ),
                            ),
                          ],
                        ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Future<String?> _getId() async {
  //   var deviceInfo = DeviceInfoPlugin();
  //   if (Platform.isIOS) {
  //     // import 'dart:io'
  //     var iosDeviceInfo = await deviceInfo.iosInfo;
  //     return iosDeviceInfo.identifierForVendor; // unique ID on iOS
  //   } else if (Platform.isAndroid) {
  //     var androidDeviceInfo = await deviceInfo.androidInfo;

  //     return androidDeviceInfo.an; // unique ID on Android
  //   }
  // }

  getExistingResident() async {
    existingData = await FBFireStore.residents
        .where(
          Filter.or(
              Filter.and(
                  Filter('hostelDocId', isEqualTo: selectedHostel?.docId),
                  Filter('floor', isEqualTo: selectedFloorNew),
                  Filter('roomNo', isEqualTo: selectedRoomNew),
                  Filter('bedNo', isEqualTo: selectedBedNew)),
              Filter('hostelId', isEqualTo: enrollidctrl.text)),
        )
        // .where('hostelDocId', isEqualTo: widget.reviewData?.hostelDocId)
        // .where('floor', isEqualTo: widget.reviewData?.floor)
        // .where('roomNo', isEqualTo: widget.reviewData?.roomNo)
        // .where('bedNo', isEqualTo: widget.reviewData?.bedNo)
        .get()
        .then((value) {
      final docss =
          value.docs.where((element) => element.id != widget.resident?.docId);
      // return null;
      // print(ResidentModel.fromJson(docss.first.data()));

      return docss.isNotEmpty ? ResidentModel.fromDocSnap(docss.first) : null;
    });
  }

  Future imagePicker(bool proof) async {
    final res =
        await ImagePickerService().pickImageNew(context, useCompressor: true);
    if (res != null) {
      proof ? proofPickedImage = res : profilePickedImage = res;
    }
    setState(() {});
  }

  Future getImageFromCamera(bool proof) async {
    final resCam = await ImagePickerService()
        .pickImageNewCamera(context, useCompressor: true);

    setState(() {
      if (resCam != null) {
        proof ? proofPickedImage = resCam : profilePickedImage = resCam;
      }
    });
  }
}

/* 
class DropDown extends StatelessWidget {
  const DropDown({
    Key? key,
    required this.value,
    required this.list,
    required this.funct,
    required this.headtxt,
    required this.fieldtxt,
  }) : super(key: key);

  final String headtxt;
  final String fieldtxt;
  final List<DropdownMenuItem<String>> list;
  final Function(String?) funct;
  final String? value;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            headtxt,
            style: const TextStyle(
              color: Color.fromARGB(255, 0, 0, 0),
              fontSize: 16,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
              height: 0,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          DropdownButtonFormField<String>(
            hint: Text(
              fieldtxt,
              style: const TextStyle(color: Color.fromARGB(255, 91, 88, 88)),
            ),
            decoration: InputDecoration(
                border:
                    OutlineInputBorder(borderRadius: BorderRadius.circular(0))),
            value: value,
            onChanged: funct,
            items: list,
          ),
        ],
      ),
    );
  }
}
 */

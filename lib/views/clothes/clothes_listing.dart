// ignore_for_file: no_wildcard_variable_uses

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/shared/firebse.dart';
import 'package:laundry_resident/views/common/alert_button.dart';
import '../../models/clothes.dart';
import '../../services/image_picker.dart';
import '../../shared/methods.dart';
import '../../shared/theme.dart';

class ClothesListing extends StatefulWidget {
  const ClothesListing({super.key});

  @override
  State<ClothesListing> createState() => _ClothesListingState();
}

class _ClothesListingState extends State<ClothesListing> {
  // final ctrl = Get.find<HomeCtrl>();

  SelectedImage? pickedImage;
  final picker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<HomeCtrl>(builder: (_) {
      final List<Clothes> clothes = [];
      final List<Clothes> lastgivencloths = [];
      final List<Clothes> currentPickupCloths = [];
      // clothes.addAll(_.clothes);
      final lastpickedUpclothes = _.lastPickupedSchedules
          .map((e) =>
              e.clothes.where((element) => element.pickedUp == true).toList())
          .toList();
      final List<String> lastpickedUpclothIds = [];
      for (var eleme in lastpickedUpclothes) {
        for (var elem in eleme) {
          lastpickedUpclothIds.add(elem.docId);
        }
      }
      for (var ele in _.clothes) {
        lastgivencloths.addIf(lastpickedUpclothIds.contains(ele.docId), ele);
      }
      final List<String> currentPickupCLothIds = [];
      if (_.currentPickupSchedule != null) {
        for (var elem in _.currentPickupSchedule!.clothes) {
          currentPickupCLothIds.add(elem.docId);
        }
      }

      for (var ele in _.clothes) {
        currentPickupCloths.addIf(
            currentPickupCLothIds.contains(ele.docId), ele);
      }
      for (var element in _.clothes) {
        clothes.addIf(
            !(currentPickupCLothIds.contains(element.docId) ||
                lastpickedUpclothIds.contains(element.docId)),
            element);
      }
      final categoryList =
          _.settings?.clothtypes.map((e) => e.title).toList() ?? [];
      return (_.settings == null)
          ? const Center(child: CircularProgressIndicator())
          : Scaffold(
              appBar: AppBar(
                backgroundColor: Colors.white,
                surfaceTintColor: Colors.white,
                shadowColor: Colors.white,
                titleSpacing: 0,
                leading: IconButton(
                  onPressed: () => context.pop(),
                  icon: const Icon(Icons.arrow_back),
                ),
                centerTitle: false,
                title: const Text(
                  'Wardrobe',
                  style: TextStyle(
                    color: Color(0xFF4F4F4F),
                    // fontSize: 24,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
                actions: [
                  if (_.clothes.length < _.settings!.maxClothes)
                    Padding(
                      padding: const EdgeInsets.only(right: 20.0),
                      child: ElevatedButton(
                        style: const ButtonStyle(
                            // fixedSize:
                            //     MaterialStateProperty.all(const Size(130, 36)),
                            padding: WidgetStatePropertyAll(EdgeInsets.zero),
                            backgroundColor:
                                WidgetStatePropertyAll(Color(0xFF01B49E))),
                        onPressed: () {
                          pickedImage = null;
                          setState(() {});
                          newClothes(context, categoryList, size, null);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 8.0, horizontal: 20),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // const Icon(
                              //   Icons.,
                              //   color: Colors.white,
                              // ),
                              // Image.asset(
                              //   'assets/images/cloth.png',
                              //   color: Colors.white,
                              // ),
                              // const SizedBox(width: 7),
                              size.width <= 335
                                  ? const Icon(
                                      CupertinoIcons.cloud_upload,
                                      color: Colors.white,
                                    )
                                  // Image.asset("assets/images/cloth.png")
                                  : const Text(
                                      'Upload',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 17,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                            ],
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              body: Container(
                height: size.height,
                decoration: const BoxDecoration(
                    image: DecorationImage(
                        repeat: ImageRepeat.repeatY,
                        opacity: .5,
                        image: AssetImage('assets/images/Image.png'))),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      /*  Row(
                        children: [
                          // if (size.width > 335)s
                          Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: InkWell(
                                onTap: () {
                                  context.pop();
                                },
                                child: const Icon(CupertinoIcons.arrow_left)),
                          ),
                          // const SizedBox(width: 10),
                          const Text(
                            'Clothes Listing',
                            style: TextStyle(
                              color: Color(0xFF4F4F4F),
                              fontSize: 24,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          if (_.clothes.length < _.settings!.maxClothes)
                            ElevatedButton(
                              style: const ButtonStyle(
                                  // fixedSize:
                                  //     MaterialStateProperty.all(const Size(130, 36)),
                                  padding:
                                      WidgetStatePropertyAll(EdgeInsets.zero),
                                  backgroundColor: WidgetStatePropertyAll(
                                      Color(0xFF01B49E))),
                              onPressed: () {
                                pickedImage = null;
                                setState(() {});
                                newClothes(context, categoryList, size, null);
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8.0, horizontal: 10),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.add,
                                      color: Colors.white,
                                    ),
                                    const SizedBox(width: 5),
                                    size.width <= 335
                                        ? const Icon(
                                            CupertinoIcons.cart,
                                            color: Colors.white,
                                          )
                                        // Image.asset("assets/images/cloth.png")
                                        : const Text(
                                            'Clothes',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 14,
                                              fontFamily: 'Poppins',
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      ), */
                      const SizedBox(height: 15),
                      if (lastgivencloths.isNotEmpty)
                        _textContainer('Given for processing'),
                      if (lastgivencloths.isNotEmpty)
                        const SizedBox(height: 15),
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 200,
                        mainAxisSpacing: 15,
                        crossAxisSpacing: 20,
                        children: lastgivencloths.asMap().entries.map((entry) {
                          final index = entry.key;
                          final cloth = entry.value;
                          return _clothCard(context, cloth, index,
                              lastpickedUpclothIds, currentPickupCLothIds);
                        }).toList(),
                      ),
                      if (lastgivencloths.isNotEmpty)
                        const SizedBox(height: 15),
                      if (currentPickupCloths.isNotEmpty)
                        _textContainer('In Bucket'),
                      if (currentPickupCloths.isNotEmpty)
                        const SizedBox(height: 15),
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 200,
                        mainAxisSpacing: 15,
                        crossAxisSpacing: 20,
                        children: [
                          ...List.generate(currentPickupCloths.length, (index) {
                            return _clothCard(
                                context,
                                currentPickupCloths[index],
                                index,
                                lastpickedUpclothIds,
                                currentPickupCLothIds);
                          })
                        ],
                      ),
                      if (currentPickupCloths.isNotEmpty)
                        const SizedBox(height: 15),
                      if (clothes.isNotEmpty) _textContainer('Clothes'),
                      if (clothes.isNotEmpty) const SizedBox(height: 15),
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 200,
                        mainAxisSpacing: 15,
                        crossAxisSpacing: 20,
                        children: [
                          ...List.generate(clothes.length, (index) {
                            return _clothCard(
                              context,
                              clothes[index],
                              index,
                              lastpickedUpclothIds,
                              currentPickupCLothIds,
                              true,
                            );
                          })
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
    });
  }

  Widget _textContainer(String txt) {
    return Text(
      txt,
      style: const TextStyle(
          color: Color(0xff4F4F4F), fontSize: 18, fontWeight: FontWeight.w600),
    );
  }

  Widget _clothCard(BuildContext context, Clothes cloth, int index,
      List<String> lastpickedUpclothIds, List<String> currentPickupCLothIds,
      [bool showXmark = false]) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          children: [
            InkWell(
              borderRadius: BorderRadius.circular(10),
              onTap: () {
                clothPopup(context, cloth);
              },
              child: Container(
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    color: const Color(0xE0E0E0E0),
                    // color: Colors.white,
                    shape: RoundedRectangleBorder(
                      // side: const BorderSide(
                      //     width: 1, color: Color(0xFFE0E0E0)),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  height: 150,
                  width: double.maxFinite,
                  child: CachedNetworkImage(
                    imageUrl: cloth.photoUrl,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[200],
                      child: const Center(
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[200],
                      child: const Icon(Icons.error, color: Colors.grey),
                    ),
                    memCacheWidth: 300, // Limit memory cache size
                    memCacheHeight: 300,
                  )),
            ),
            if (showXmark)
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 8.0, top: 8),
                  child: InkWell(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return AlertButton(
                            title: "Alert",
                            content: "Are you sure you want to delete?",
                            onTapYes: () async {
                              // .firstWhere((element) =>
                              //     element.photoUrl ==
                              //     _.clothes[index]
                              //         .photoUrl);
                              try {
                                if ((lastpickedUpclothIds
                                        .contains(cloth.docId) ||
                                    (currentPickupCLothIds
                                        .contains(cloth.docId)))) {
                                  const snackBar = SnackBar(
                                      duration: Duration(milliseconds: 1500),
                                      content: Text(
                                          'Unable to delete .Cloth not delivered yet or in bucket.'));
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(snackBar);
                                  Navigator.of(context).pop();
                                  return;
                                }
                                await FBFireStore.residents
                                    .doc(Get.find<HomeCtrl>()
                                        .residentModel!
                                        .docId)
                                    .collection('clothes')
                                    .doc(cloth.docId)
                                    .delete();
                                await FBStorage.fbstore
                                    .refFromURL(cloth.photoUrl)
                                    .delete();
                              } catch (e) {
                                debugPrint(e.toString());
                              }
                              if (context.mounted) {
                                Navigator.of(context).pop();
                              }
                              showAppSnackBar('Cloth Removed');
                            },
                            onTapNo: () => Navigator.of(context).pop(),
                          );
                        },
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(3),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                              blurRadius: 7,
                              spreadRadius: .2,
                              blurStyle: BlurStyle.outer,
                              color: Colors.grey,
                              offset: Offset(.1, 1))
                        ],
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(CupertinoIcons.xmark,
                          color: Colors.black54, size: 20),
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 5),
        Text(cloth.category)
      ],
    );
  }

  Future<dynamic> clothPopup(BuildContext context, Clothes cloth) {
    return showDialog(
        context: context,
        builder: (BuildContext context) =>
            StatefulBuilder(builder: (context, setState2) {
              return Dialog(
                clipBehavior: Clip.antiAlias,
                shape: RoundedRectangleBorder(
                    side: BorderSide.none,
                    borderRadius: BorderRadius.circular(15)),
                child: SizedBox(
                  height: 350,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      InteractiveViewer(
                        minScale: .5,
                        maxScale: 2,
                        scaleEnabled: true,
                        panEnabled: true,
                        // alignPanAxis: false,
                        child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: const Color(0xFF828282)
                                  // color: const Color.fromARGB(
                                  //     255, 185, 183, 183),
                                  ),
                              borderRadius: BorderRadius.circular(5),
                            ),
                            child: CachedNetworkImage(
                              // imageUrl: '',
                              imageUrl: cloth.photoUrl,
                              fit: BoxFit.cover,
                              // height: double.maxFinite,
                              width: double.maxFinite,
                              // width: double.maxFinite,
                              placeholder: (context, url) {
                                return const Center(
                                    child: Icon(CupertinoIcons.camera));
                              },
                              errorWidget: (context, url, error) {
                                return const Center(
                                    child: Icon(CupertinoIcons.camera));
                              },
                              // width: double.maxFinite,
                            )

                            // _selectedImage != null
                            //     ? Image.file(_selectedImage!,
                            //         fit: BoxFit.cover)
                            //     :

                            ),
                      ),
                      Align(
                        alignment: Alignment.topRight,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 8, right: 8),
                          child: InkWell(
                            onTap: () => Navigator.of(context).pop(),
                            child: Container(
                              height: 30,
                              width: 30,
                              decoration: const BoxDecoration(boxShadow: [
                                BoxShadow(
                                    color: Color.fromARGB(34, 0, 0, 0),
                                    blurRadius: 2,
                                    spreadRadius: 3,
                                    offset: Offset(0, 1.1)),
                              ], color: Colors.white, shape: BoxShape.circle),
                              child: const Icon(CupertinoIcons.xmark),
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              );
            }));
  }

  Future<dynamic> newClothes(
      BuildContext context, List<String> namelist, size, String? selectedType) {
    // getData();
    bool loading2 = false;

    return showDialog(
        context: context,
        builder: (BuildContext context) =>
            StatefulBuilder(builder: (context, setState2) {
              return AlertDialog(
                title: const Text('Add New Cloth',
                    style: TextStyle(
                      color: Color(0xFF4F4F4F),
                      fontSize: 20,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                    )),
                content: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () async {
                        showModalBottomSheet(
                          constraints: const BoxConstraints(maxHeight: 100),
                          context: context,
                          builder: (context) {
                            return Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Expanded(
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(5),
                                    onTap: () async {
                                      Navigator.of(context).pop();
                                      await imagePicker();
                                      setState2(() {});
                                    },
                                    child: const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: Center(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              CupertinoIcons.photo_on_rectangle,
                                              size: 30,
                                              color: Color.fromARGB(
                                                  255, 236, 198, 6),
                                            ),
                                            Text(
                                              'Gallery',
                                              style: TextStyle(fontSize: 15),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                // const VerticalDivider(),
                                Expanded(
                                  child: InkWell(
                                    onTap: () async {
                                      Navigator.of(context).pop();
                                      await getImageFromCamera();
                                      setState2(() {});
                                    },
                                    child: const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: Center(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              CupertinoIcons.camera,
                                              size: 30,
                                              color: Color.fromARGB(
                                                  255, 28, 99, 223),
                                            ),
                                            Text(
                                              'Camera',
                                              style: TextStyle(fontSize: 15),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        );
                      },
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              height: 200,
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: const Color(0xFF828282)
                                        // color: const Color.fromARGB(
                                        //     255, 185, 183, 183),
                                        ),
                                borderRadius: BorderRadius.circular(5),
                              ),
                              child: pickedImage != null
                                  ? AspectRatio(
                                      aspectRatio: 1,
                                      child: Image.memory(
                                        pickedImage!.uInt8List,
                                        fit: BoxFit.cover,
                                        width: double.maxFinite,
                                        height: 160,
                                      ),
                                    )
                                  : IntrinsicHeight(
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: InkWell(
                                              onTap: () async {
                                                await getImageFromCamera();
                                                setState2(() {});
                                              },
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    CupertinoIcons.camera,
                                                    color: Color(0xFF828282),
                                                  ),
                                                  Text(
                                                    'Camera',
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: Color(0xFF828282),
                                                      fontSize: 16,
                                                      fontFamily: 'Poppins',
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                          const VerticalDivider(width: 0),
                                          Expanded(
                                            child: InkWell(
                                              onTap: () async {
                                                await imagePicker();
                                                setState2(() {});
                                              },
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    CupertinoIcons.photo,
                                                    color: Color(0xFF828282),
                                                  ),
                                                  Text(
                                                    'Gallery',
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: Color(0xFF828282),
                                                      fontSize: 16,
                                                      fontFamily: 'Poppins',
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    DropdownButtonFormField(
                      decoration: InputDecoration(
                          focusedBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Color(0xFF828282),
                              ),
                              borderRadius: BorderRadius.circular(5)),
                          border: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Color(0xFF828282),
                              ),
                              borderRadius: BorderRadius.circular(5))),
                      items: namelist.map((name) {
                        return DropdownMenuItem(
                          value: name.toString(),
                          child: Text(name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        selectedType = value;
                        setState2(() {});
                      },
                      hint: Text(
                        'Select Category',
                        style: TextStyle(
                          fontSize: size.width > 300 ? 15 : 12,
                          color: const Color(0xFF828282),
                        ),
                      ),
                    ),
                  ],
                ),
                actions: [
                  loading2
                      ? const Center(
                          child: SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: themeColor,
                              strokeWidth: 3,
                            ),
                          ),
                        )
                      : SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                              onPressed: () async {
                                if (pickedImage == null ||
                                    selectedType == null) {
                                  return;
                                }
                                setState2(() {
                                  loading2 = true;
                                });
                                try {
                                  final photoUrl =
                                      await uploadClothFile(pickedImage!);

                                  final data = <String, dynamic>{
                                    "photoUrl": photoUrl,
                                    "category": selectedType,
                                    "uploadedAt": Timestamp.now(),
                                  };
                                  await FBFireStore.residents
                                      .doc(Get.find<HomeCtrl>()
                                          .residentModel!
                                          .docId)
                                      .collection("clothes")
                                      .add(data);
                                  setState2(() {
                                    loading2 = false;
                                  });
                                  // setState(() {});
                                  if (context.mounted) {
                                    Navigator.of(context).pop();
                                  }
                                  showAppSnackBar('Cloth Added');
                                } catch (e) {
                                  debugPrint(e.toString());
                                }
                              },
                              style: ButtonStyle(
                                  backgroundColor: const WidgetStatePropertyAll(
                                      Color(0xFF01B49E)),
                                  shape: WidgetStatePropertyAll(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(5)))),
                              child: const Text(
                                'Save',
                                style: TextStyle(color: Colors.white),
                              )),
                        ),
                ],
              );
            }));
  }

/*  Future<dynamic> newClothes(
      BuildContext context, List<String> namelist, size, String? selectedType) {
    // getData();
    bool loading2 = false;
    return showDialog(
        context: context,
        builder: (BuildContext context) =>
            StatefulBuilder(builder: (context, setState2) {
              return AlertDialog(
                title: const Text('Add New Cloth',
                    style: TextStyle(
                      color: Color(0xFF4F4F4F),
                      fontSize: 20,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                    )),
                content: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () async {
                        await imagePicker();
                        setState2(() {});
                      },
                      // onTap: () {
                      //   _pickImageFromGallery();
                      // },
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: const Color(0xFF828282)
                                        // color: const Color.fromARGB(
                                        //     255, 185, 183, 183),
                                        ),
                                borderRadius: BorderRadius.circular(5),
                              ),
                              height: 200,
                              child: pickedImage != null
                                  ? Image.memory(
                                      pickedImage!.uInt8List,
                                      fit: BoxFit.cover,
                                      width: double.maxFinite,
                                      height: 160,
                                    )
                                  :
                                  // _selectedImage != null
                                  //     ? Image.file(_selectedImage!,
                                  //         fit: BoxFit.cover)
                                  //     :

                                  const Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Icon(
                                          CupertinoIcons.square_arrow_up_fill,
                                          color: Color(0xFF828282),
                                        ),
                                        Text(
                                          'Upload Image',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            color: Color(0xFF828282),
                                            fontSize: 16,
                                            fontFamily: 'Poppins',
                                            fontWeight: FontWeight.w400,
                                          ),
                                        )
                                      ],
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    DropdownButtonFormField(
                      decoration: InputDecoration(
                          focusedBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Color(0xFF828282),
                              ),
                              borderRadius: BorderRadius.circular(5)),
                          border: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Color(0xFF828282),
                              ),
                              borderRadius: BorderRadius.circular(5))),
                      items: namelist.map((name) {
                        return DropdownMenuItem(
                          value: name.toString(),
                          child: Text(name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        selectedType = value;
                        setState2(() {});
                      },
                      hint: Text(
                        'Select Category',
                        style: TextStyle(
                          fontSize: size.width > 300 ? 15 : 12,
                          color: const Color(0xFF828282),
                        ),
                      ),
                    ),
                  ],
                ),
                actions: [
                  loading2
                      ? const Center(
                          child: SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: themeColor,
                              strokeWidth: 3,
                            ),
                          ),
                        )
                      : SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                              onPressed: () async {
                                if (pickedImage == null ||
                                    selectedType == null) {
                                  return;
                                }
                                setState2(() {
                                  loading2 = true;
                                });
                                try {
                                  final photoUrl =
                                      await uploadFile(pickedImage!);

                                  final data = <String, dynamic>{
                                    "photoUrl": photoUrl,
                                    "category": selectedType,
                                  };
                                  await FBFireStore.residents
                                      .doc(Get.find<HomeCtrl>()
                                          .residentModel!
                                          .docId)
                                      .collection("clothes")
                                      .add(data);
                                  setState2(() {
                                    loading2 = false;
                                  });
                                  // setState(() {});
                                  if (context.mounted) {
                                    Navigator.of(context).pop();
                                  }
                                  showAppSnackBar('Cloth Added');
                                } catch (e) {
                                  debugPrint(e.toString());
                                }
                              },
                              style: ButtonStyle(
                                  backgroundColor:
                                      const WidgetStatePropertyAll(
                                          Color(0xFF01B49E)),
                                  shape: WidgetStatePropertyAll(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(5)))),
                              child: const Text(
                                'Save',
                                style: TextStyle(color: Colors.white),
                              )),
                        ),
                ],
              );
            }));
  }
 */
  Future imagePicker() async {
    final res =
        await ImagePickerService().pickImageNew(context, useCompressor: true);
    if (res != null) {
      pickedImage = res;
    }
    setState(() {});
  }

  Future getImageFromCamera() async {
    final resCam = await ImagePickerService()
        .pickImageNewCamera(context, useCompressor: true);

    setState(() {
      if (resCam != null) {
        pickedImage = resCam;
      }
    });
  }
  // Future _pickImageFromGallery() async {
  //   final returnedImage =
  //       await ImagePicker().pickImage(source: ImageSource.gallery);

  //   if (returnedImage == null) return;
  //   setState(() {
  //     _selectedImage = File(returnedImage.path);
  //   });
  // }
}

// class NewClothes extends StatelessWidget {
//   const NewClothes({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return const PopupMenuItem(
//         child: Padding(
//       padding: EdgeInsets.all(20.0),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.end,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [],
//       ),
//     ));
//   }
// }

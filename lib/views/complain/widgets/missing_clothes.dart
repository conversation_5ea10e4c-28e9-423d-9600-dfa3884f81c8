import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/shared/methods.dart';
import '../../../models/pickup_schedules.dart';
import '../../../shared/firebse.dart';
import '../../common/submit_button.dart';

class MissingClothDeliveryPage extends StatefulWidget {
  const MissingClothDeliveryPage({super.key});

  @override
  State<MissingClothDeliveryPage> createState() =>
      _MissingClothDeliveryPageState();
}

class _MissingClothDeliveryPageState extends State<MissingClothDeliveryPage> {
  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
    return StreamBuilder(
      stream: FBFireStore.pickUpSchedules
          .where("floor", isEqualTo: ctrl.residentModel?.floor)
          .where("roomNo", isEqualTo: ctrl.residentModel?.roomNo)
          .where("pickedUp", isEqualTo: true)
          .where("delivered", isEqualTo: true)
          .where('missingCount', isGreaterThan: 0)
          // .where("lastUpdate",
          //     isLessThan: Timestamp.fromDate(
          //         DateTime.now().subtract(const Duration(days: 30))))
          .snapshots()
          .map((event) =>
              event.docs.map((e) => PickUpScheduleModel.fromSnap(e)).toList()),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          debugPrint(snapshot.error.toString());
        }
        if (snapshot.hasData) {
          final missingPickUpSchedules = snapshot.data ?? [];
          return missingPickUpSchedules.isNotEmpty
              ? SingleChildScrollView(
                  child: Padding(
                  padding: const EdgeInsets.all(25),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...List.generate(missingPickUpSchedules.length, (index) {
                        return MissingDeliveryIndividualSet(
                          ctx: context,
                          pickUpScheduleModel: missingPickUpSchedules[index],
                        );
                      })
                    ],
                  ),
                ))
              : const Center(
                  child: Text('No missing clothes',
                      style: TextStyle(
                          fontWeight: FontWeight.w600, fontSize: 17)));
        }

        return const Center(
            child: Text('No missing clothes',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17)));
      },
    );
  }
}

class MissingDeliveryIndividualSet extends StatefulWidget {
  const MissingDeliveryIndividualSet({
    super.key,
    required this.pickUpScheduleModel,
    required this.ctx,
  });
  final PickUpScheduleModel? pickUpScheduleModel;
  final BuildContext ctx;

  @override
  State<MissingDeliveryIndividualSet> createState() =>
      _MissingDeliveryIndividualSetState();
}

class _MissingDeliveryIndividualSetState
    extends State<MissingDeliveryIndividualSet> {
  List<ClothPickupModel> missingClothList = [];
  // List<ClothPickupModel> pickedClothList = [];
  List<String> removedClothesIds = [];
  // List<ClothPickupModel> unTalliedClothes = [];removeClothesIds

  // @override
  // void initState() {
  //   super.initState();
  //   if (widget.pickUpScheduleModel != null) {
  //     removerClothesIds = widget.pickUpScheduleModel!.clothes
  //         .where((element) => element.tallied != true)
  //         .toList();
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    missingClothList = widget.pickUpScheduleModel?.clothes
            .where((element) =>
                (element.pickedUp == true && element.tallied != null) &&
                element.collected != true)
            .toList() ??
        [];
    // pickedClothList = widget.pickUpScheduleModel?.clothes
    //         .where((element) => element.pickedUp == true)
    //         .toList() ??
    //     [];
    // final unTalliedClothesCount = pickedClothList
    //     .where((element) => element.tallied != true)
    //     .toList()
    //     .length;
    // print(talliedClothesCount);
    bool waiting = widget.pickUpScheduleModel?.needApproval ?? false;
    return widget.pickUpScheduleModel != null
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Picked on: ${widget.pickUpScheduleModel!.pickedAt!.toDate().goodDayDate()}",
                style: const TextStyle(fontSize: 15),
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  Expanded(
                    child: StaggeredGrid.extent(
                      maxCrossAxisExtent: 280,
                      crossAxisSpacing: 18,
                      mainAxisSpacing: 18,
                      children: [
                        ...List.generate(missingClothList.length, (index) {
                          final cloth = missingClothList[index];
                          bool accept =
                              !removedClothesIds.contains(cloth.docId);
                          // bool accept =
                          //     !removedClothesIds.contains(cloth.docId) &&
                          //         cloth.collected != false;
                          return Stack(
                            children: [
                              AspectRatio(
                                aspectRatio: 1,
                                child: Container(
                                  // constraints: const BoxConstraints(maxHeight: 150),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      color: const Color(0xFFF2F2F2),
                                      image: DecorationImage(
                                          image: NetworkImage(cloth.photoUrl),
                                          fit: BoxFit.cover)),
                                ),
                              ),
                              Align(
                                alignment: const Alignment(-1, -1),
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: Transform.scale(
                                    scale: 1.4,
                                    child: Checkbox(
                                      fillColor: WidgetStatePropertyAll(accept
                                          // && cloth.tallied == true
                                          ? const Color(0xFF01B49E)
                                          : Colors.white),
                                      side: const BorderSide(
                                          color: Color(0xFF01B49E)),
                                      value: accept,
                                      // && cloth.tallied == true,
                                      onChanged: (value) {
                                        if (
                                            //   cloth.tallied == false
                                            // ||
                                            widget.pickUpScheduleModel
                                                    ?.needApproval ==
                                                true) return;
                                        setState(() {
                                          if (value == true) {
                                            removedClothesIds
                                                .remove(cloth.docId);
                                          } else {
                                            removedClothesIds.add(cloth.docId);
                                          }
                                        });
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                          /*  ImageCard(
                              imageName: widget.pickUpScheduleModel!
                                  .clothes[index].photoUrl); */
                        })
                      ],
                    ),
                  ),
                ],
              ),
              if (widget.pickUpScheduleModel!.enableApprove || waiting)
                const SizedBox(height: 20),
              if (widget.pickUpScheduleModel!.enableApprove || waiting)
                Row(
                  children: [
                    Expanded(
                      child: waiting
                          ? Container(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: const Color(0xff828282),
                                    width: 1,
                                  ),
                                  color: const Color.fromARGB(50, 1, 180, 159)),
                              child: const Center(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      CupertinoIcons.hourglass,
                                      color: Color(0xff828282),
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      "Waiting for approval",
                                      style: TextStyle(
                                          color: Color(0xff828282),
                                          fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                            )

                          /* ElevatedButton(
                              style: ButtonStyle(
                                  padding: const WidgetStatePropertyAll(
                                      EdgeInsets.symmetric(vertical: 20)),
                                  backgroundColor:
                                      WidgetStatePropertyAll(bgColor),
                                  elevation: const WidgetStatePropertyAll(0),
                                  shape: WidgetStatePropertyAll(
                                    RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6)),
                                  )),
                              onPressed: onPressed,
                              child: Text(
                                text,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  letterSpacing: 2,
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w600,
                                  height: 0,
                                ),
                              ),
                            ) */
                          : CommonSubmitButton(
                              text:
                                  'Collect (${missingClothList.length - removedClothesIds.length})',

                              ///* - (accpetedClothList.length - talliedClothesCount))  */
                              onPressed: () async {
                                onPressedSubmit(widget.ctx);
                              },
                              bgColor: const Color(0xFF01B49E),
                            ),
                    )
                  ],
                ),
              const SizedBox(height: 30),
            ],
          )
        : Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  "Clothes Delivered",
                  style: TextStyle(fontSize: 25),
                ),
                const SizedBox(height: 15),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text(
                    "Go Back",
                    style: TextStyle(fontSize: 20),
                  ),
                ),
              ],
            ),
          );
  }

  onPressedSubmit(BuildContext ctx) async {
    try {
      int totalMissingValue = 0;
      // unTalliedClothes =
      //     pickedClothList.where((element) => element.tallied != true).toList();
      for (var missingClothId in removedClothesIds) {
        final removedClothValue = missingClothList
            .firstWhereOrNull((cloth) => cloth.docId == missingClothId)!
            .categoryValue;
        totalMissingValue += removedClothValue;
      }

      await FBFireStore.fb.runTransaction((transaction) async {
        transaction.update(
            FBFireStore.pickUpSchedules.doc(widget.pickUpScheduleModel!.docId),
            {
              "needApproval": removedClothesIds.isNotEmpty,
              "enableApprove": false,
              // "delivered": removedClothesIds.isEmpty,
              // "collectedByDocId": ctrl.residentModel?.docId,
              // "collectedByName": ctrl.residentModel?.name,
              // "deliveryTime": FieldValue.serverTimestamp(),
              "missingCount": removedClothesIds.isNotEmpty
                  ? removedClothesIds.length
                  : null,
              "missingValue":
                  removedClothesIds.isNotEmpty ? totalMissingValue : null,
              "clothes": widget.pickUpScheduleModel!.clothes
                  .map((e) => e.toJson3(!removedClothesIds.contains(e.docId)))
                  .toList(),
            });
        if (removedClothesIds.isEmpty) {
          // await initiateActivity(
          //   uId: widget.pickUpScheduleModel?.uId,
          //   type: ActivityType.clothesDelivered,
          //   typeDocId: widget.pickUpScheduleModel?.docId,
          //   desc:
          //       'Your ${pickedClothList.length - unTalliedClothes.length} out of ${pickedClothList.length}  clothes has been delivered on ${DateTime.now().convertToDDMMYY()} at ${DateTime.now().goodTime()}.',
          //   userType: UserTypes.resident,
          //   title: 'Clothes Delivered',
          // );
        }
        return true;
      });

      /* if (res) {
        final lst = widget.pickUpScheduleModel!.clothes
            .map((e) => e.toJson3(!removedClothesIds.contains(e.docId)))
            .toList();
        final newList = List<ClothPickupModel>.from(
            lst.map((item) => ClothPickupModel.fromJson(item)));
        if (newList.where((element) => element.collected == true).length <
                widget.pickUpScheduleModel!.clothes
                    .map((e) => e.pickedUp == true)
                    .length &&
            removedClothesIds.isEmpty) {
          final randomId = getRandomId(8).toUpperCase();
          final res2 = await initiateComplaint(
            autoGenerate: true,
            uId: widget.pickUpScheduleModel!.uId,
            hostelDocId: widget.pickUpScheduleModel!.hostelDocId,
            wing: widget.pickUpScheduleModel!.wing,
            floor: widget.pickUpScheduleModel!.floor,
            roomNo: widget.pickUpScheduleModel!.roomNo,
            bedNo: widget.pickUpScheduleModel!.bedNo,
            residentName: widget.pickUpScheduleModel!.residentName,
            bunchDocId: widget.pickUpScheduleModel!.bunchDocId ?? "",
            pickUpScheduleDocId: widget.pickUpScheduleModel!.docId,
            complainType: ["Missing clothes"],
            complainDesc: 'Missing cloth complain',
            missingClothes: removedClothesIds,
            clothesUrl: [],
            randomId: randomId,
          );
          if (res2 == true && ctx.mounted && removedClothesIds.isEmpty) {
            return showDialog(
              context: ctx,
              builder: (context) {
                return AlertDialog(
                  title: const Text('Ticket Raised'),
                  content: Text(
                      'Your missing clothes complain has been raised under Id: $randomId. We will surely comeback to you within 2 days.'),
                  actions: [
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            style: TextButton.styleFrom(
                              // backgroundColor: const Color.fromARGB(20, 0, 0, 0),
                              backgroundColor: themeColor,
                              elevation: 0,
                            ),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: const Text(
                              'OK',
                              style:
                                  TextStyle(color: Colors.white, fontSize: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            );
          }
        }
      } */
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}

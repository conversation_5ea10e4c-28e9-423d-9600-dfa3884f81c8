import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/models/complaints_model.dart';
import 'package:laundry_resident/shared/firebse.dart';
import 'complain_card.dart';

class ActiveComplainPage extends StatelessWidget {
  const ActiveComplainPage({super.key});

  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
    final currentResident = ctrl.residentModel;
    return StreamBuilder(
      stream: FBFireStore.complaints
          .where('uId', isEqualTo: currentResident?.docId)
          .where('resolved', isEqualTo: false)
          .where('status', whereIn: ['Active', 'Pending'])
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((event) =>
              event.docs.map((e) => ComplaintsModel.fromSnap(e)).toList()),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: SizedBox(
                height: 35,
                width: 35,
                child: CircularProgressIndicator(
                  strokeWidth: 3.5,
                )),
          );
        }
        if (snapshot.hasError) {
          debugPrint(snapshot.error.toString());
          return const SizedBox();
        }
        if (snapshot.hasData) {
          final allActiveComplains = snapshot.data ?? [];
          return AllActiveComplains(allActiveComplains: allActiveComplains);
        }
        return const SizedBox();
      },
    );
    // : const Text("No data to display");
  }
}

class AllActiveComplains extends StatefulWidget {
  const AllActiveComplains({super.key, required this.allActiveComplains});

  final List<ComplaintsModel> allActiveComplains;

  @override
  State<AllActiveComplains> createState() => _AllActiveComplainsState();
}

class _AllActiveComplainsState extends State<AllActiveComplains> {
  List<ComplaintsModel> activeComplains = [];
  TextEditingController searchCtrl = TextEditingController();

  getSearchData({required String searchedText}) {
    if (searchedText.isEmpty) {
      setState(() {});
      return;
    }

    activeComplains.clear();
    activeComplains.addAll(widget.allActiveComplains
        .where((complain) => complain.randomId.contains(searchedText)));
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    widget.allActiveComplains.sort((a, b) => a.status == 'Pending' ? 0 : 1);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(15),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: searchCtrl,
                  onChanged: (value) {
                    getSearchData(searchedText: value.toUpperCase());
                  },
                  decoration: const InputDecoration(
                      border: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black26)),
                      enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black26)),
                      focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black87)),
                      prefixIcon: Icon(CupertinoIcons.search)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          widget.allActiveComplains.isNotEmpty
              ? ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: searchCtrl.text.isEmpty
                      ? widget.allActiveComplains.length
                      : activeComplains.length,
                  separatorBuilder: (context, index) {
                    return const SizedBox(height: 15);
                  },
                  itemBuilder: (context, index) {
                    final complain = searchCtrl.text.isEmpty
                        ? widget.allActiveComplains[index]
                        : activeComplains[index];
                    // final missingClothesUrl = <String>[];
                    // for (var ele in complain.missingClothes) {
                    //   missingClothesUrl.add(Get.find<HomeCtrl>()
                    //       .clothes
                    //       .firstWhere((element) => element.docId == ele)
                    //       .photoUrl);
                    // }
                    // final clothes = complain.clothesUrl.isNotEmpty
                    //     ? complain.clothesUrl
                    //     : missingClothesUrl;
                    return Column(
                      children: [
                        ComplainCard(complain: complain),
                      ],
                    );
                  },
                )
              : const Center(
                  child: Text(
                  "No active complains",
                  style: TextStyle(fontWeight: FontWeight.w500, fontSize: 17),
                )),
        ],
      ),
    );
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/shared/firebse.dart';
import 'package:laundry_resident/shared/methods.dart';
import 'package:laundry_resident/shared/theme.dart';
import 'package:laundry_resident/views/Home/clothes_collecting.dart';
import 'package:laundry_resident/views/common/submit_button.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../models/resident.dart';
import '../../../services/image_picker.dart';
import '../../common/alert_button.dart';

class NewComplainPage extends StatefulWidget {
  const NewComplainPage({super.key});

  @override
  State<NewComplainPage> createState() => _NewComplainPageState();
}

class _NewComplainPageState extends State<NewComplainPage> {
  // List<SelectedImage> pickedImages = [];
  ResidentModel? currentResident;

  List<String> complainTypeList = [
    'Missing clothes',
    'Torn clothes',
    'Stains',
    'Colour bledding',
    'Shrinking & stretching',
    'Other',
  ];

  List<String> selectedImages = [];
  bool loadOnSubmit = false;
  String? selectedComplainType;
  DateTime? selectedPickupDate;
  TextEditingController complainDescCtrl = TextEditingController();
  TextEditingController mobileCtrl = TextEditingController();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    currentResident = Get.find<HomeCtrl>().residentModel;
    mobileCtrl.text = currentResident?.mobile ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 7),
          const Text(
            "Generate New Complain",
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
          ),
          const SizedBox(height: 20),
          const Text(
            "Complain Type",
            style: TextStyle(fontSize: 16.5),
          ),
          const SizedBox(height: 5),

          // DROPDOWN  SECTION

          DropDownSection(
            complainTypeList: complainTypeList,
            onChanged: (value) {
              selectedComplainType = value;
              setState(() {});
            },
            selectedComplainType: selectedComplainType,
          ),
          if (selectedComplainType != null) const SizedBox(height: 10),

          // ADD  IMAGES  SECTION

          selectedComplainType != null
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          "Add Images",
                          style: TextStyle(fontSize: 16.5),
                        ),
                        ElevatedButton(
                            onPressed: selectedComplainType == 'Missing clothes'
                                ? () {
                                    missingComplainTypeImageSheet(
                                        context: context,
                                        selectedImagesList: selectedImages);
                                  }
                                : () async {
                                    otherComplainTypeImageSheet(context);
                                  },
                            child: Text(selectedImages.isEmpty
                                ? "Add Images"
                                : "Add More"))
                      ],
                    ),
                    const SizedBox(height: 7),
                    StaggeredGrid.extent(
                      maxCrossAxisExtent: 150,
                      mainAxisSpacing: 10,
                      crossAxisSpacing: 10,
                      children: [
                        ...List.generate(
                          selectedImages.length,
                          (index) {
                            return Stack(
                              children: [
                                Container(
                                  clipBehavior: Clip.antiAlias,
                                  decoration: ShapeDecoration(
                                    color: const Color(0xE0E0E0E0),
                                    // color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      // side: const BorderSide(
                                      //     width: 1, color: Color(0xFFE0E0E0)),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                  height: 150,
                                  width: double.maxFinite,
                                  child: CachedNetworkImage(
                                    imageUrl: selectedComplainType ==
                                            'Missing clothes'
                                        ? ctrl.clothes
                                                .firstWhereOrNull(
                                                  (element) =>
                                                      element.docId ==
                                                      selectedImages[index],
                                                )
                                                ?.photoUrl ??
                                            ""
                                        : selectedImages[index],
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    errorWidget: (context, url, error) {
                                      return const Icon(CupertinoIcons.nosign);
                                    },
                                    placeholder: (context, url) {
                                      return const Icon(CupertinoIcons.camera);
                                    },
                                  ),
                                ),
                                Padding(
                                  padding:
                                      const EdgeInsets.only(right: 8.0, top: 8),
                                  child: Align(
                                    alignment: Alignment.topRight,
                                    child: InkWell(
                                      onTap: () {
                                        showDialog(
                                          context: context,
                                          builder: (context) {
                                            return AlertButton(
                                              title: "Remove Image",
                                              content:
                                                  "Are you sure you want to remove?",
                                              onTapYes: () async {
                                                selectedImages.removeAt(index);
                                                setState(() {});
                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                }
                                              },
                                              onTapNo: () =>
                                                  Navigator.of(context).pop(),
                                            );
                                          },
                                        );
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(1.2),
                                        decoration: const BoxDecoration(
                                          color: Colors.white,
                                          boxShadow: [
                                            BoxShadow(
                                              blurRadius: 7,
                                              spreadRadius: .2,
                                              blurStyle: BlurStyle.outer,
                                              color: Colors.grey,
                                              offset: Offset(.1, 1),
                                            )
                                          ],
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(CupertinoIcons.xmark,
                                            color: Colors.black54, size: 20),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                )
              : const SizedBox(),
          const SizedBox(height: 13),
          const Row(
            children: [
              Text(
                "Pickup Date",
                style: TextStyle(fontSize: 16.5),
              ),
            ],
          ),
          const SizedBox(height: 5),

          Container(
            height: 55,
            decoration: BoxDecoration(
                border: Border.all(color: Colors.black12),
                borderRadius: BorderRadius.circular(3)),
            child: IntrinsicHeight(
              child: Row(
                children: [
                  Expanded(
                      child: Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    child: Text(
                      selectedPickupDate?.convertToDDMMYY() ?? "Select date",
                      style: selectedPickupDate == null
                          ? TextStyle(
                              color: Colors.grey.shade700, fontSize: 15.1)
                          : null,
                    ),
                  )),
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 12.0),
                    child: VerticalDivider(),
                  ),
                  IconButton(
                      onPressed: () async {
                        selectedPickupDate = await showDatePicker(
                            context: context,
                            firstDate: DateTime(2000),
                            lastDate: DateTime.now());
                        setState(() {});
                      },
                      icon: const Icon(CupertinoIcons.calendar))
                ],
              ),
            ),
          ),

          const SizedBox(height: 13),
          if (currentResident?.mobile == null) ...[
            const Text(
              "Mobile",
              style: TextStyle(fontSize: 16.5),
            ),
            const SizedBox(height: 5),
            TextFormField(
              controller: mobileCtrl,
              decoration: const InputDecoration(
                hintText: 'Mobile Number',

                hintStyle: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 15,
                ),
                filled: true,
                fillColor: Colors.white,
                focusColor: Colors.white,
                // contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                border: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.black12)),
                enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.black12)),
                focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.black12)),
              ),
            ),
            const SizedBox(height: 13),
          ],

          const Text(
            "Description",
            style: TextStyle(fontSize: 16.5),
          ),
          const SizedBox(height: 5),
          TextFormField(
            controller: complainDescCtrl,
            maxLines: 5,
            decoration: const InputDecoration(
              hintText: 'Type Description',

              hintStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 15,
              ),
              filled: true,
              fillColor: Colors.white,
              focusColor: Colors.white,
              // contentPadding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
              border: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.black12)),
              enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.black12)),
              focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.black12)),
            ),
          ),
          const SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              loadOnSubmit
                  ? const Center(
                      child: SizedBox(
                        height: 28,
                        width: 28,
                        child: CircularProgressIndicator(
                          color: themeColor,
                          strokeWidth: 3.5,
                        ),
                      ),
                    )
                  : Expanded(
                      child: CommonSubmitButton(
                        fontSize: 15.5,
                        verPadding: 10,
                        text: 'Submit',
                        onPressed: () async {
                          if (selectedComplainType == null) {
                            const snackBar = SnackBar(
                              content: Text('No Complain Type Selcted'),
                              duration: Duration(milliseconds: 1500),
                            );
                            ScaffoldMessenger.of(context)
                                .showSnackBar(snackBar);
                            return;
                          }
                          if (selectedImages.isEmpty) {
                            const snackBar = SnackBar(
                              content: Text('No images added'),
                              duration: Duration(milliseconds: 1500),
                            );
                            ScaffoldMessenger.of(context)
                                .showSnackBar(snackBar);
                            return;
                          }
                          if (currentResident?.mobile == null &&
                              mobileCtrl.text.trim().isEmpty) {
                            const snackBar = SnackBar(
                              content: Text('Mobile number required'),
                              duration: Duration(milliseconds: 1500),
                            );
                            ScaffoldMessenger.of(context)
                                .showSnackBar(snackBar);
                            return;
                          }

                          if (selectedPickupDate == null) {
                            const snackBar = SnackBar(
                              content: Text('Select pickup date'),
                              duration: Duration(milliseconds: 1500),
                            );
                            ScaffoldMessenger.of(context)
                                .showSnackBar(snackBar);
                            return;
                          }

                          final mobile =
                              mobileCtrl.text.trim().substring(0, 3) == "+91"
                                  ? mobileCtrl.text.trim()
                                  : '+91${mobileCtrl.text.trim()}';

                          if (mobile.length != 13) {
                            const snackBar = SnackBar(
                              content: Text('Please enter valid mobile number'),
                              duration: Duration(milliseconds: 1500),
                            );
                            ScaffoldMessenger.of(context)
                                .showSnackBar(snackBar);
                            return;
                          }
                          showDialog(
                            context: context,
                            builder: (context) {
                              bool loadOnSubmit2 = false;
                              return StatefulBuilder(
                                  builder: (context, setState2) {
                                return AlertDialog(
                                  actionsAlignment: MainAxisAlignment.end,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20)),
                                  // backgroundColor: Colors.white,
                                  // surfaceTintColor: Colors.white,
                                  title: const Text('Submit'),
                                  content: const Text(
                                    'Your complain will be raised. Are you sure you want to submit.',
                                    style: TextStyle(fontSize: 15),
                                  ),
                                  actions: loadOnSubmit2
                                      ? [
                                          const SizedBox(
                                            height: 30,
                                            width: 30,
                                            child: Center(
                                              child:
                                                  CircularProgressIndicator(),
                                            ),
                                          )
                                        ]
                                      : [
                                          TextButton(
                                            onPressed: () async {
                                              setState2(() {
                                                loadOnSubmit2 = true;
                                              });
                                              try {
                                                final batch =
                                                    FBFireStore.fb.batch();
                                                if (currentResident?.mobile ==
                                                    null) {
                                                  batch.update(
                                                      FBFireStore.residents.doc(
                                                          currentResident
                                                              ?.docId),
                                                      {'mobile': mobile});
                                                }
                                                List<String>
                                                    selectedComplainTypeList =
                                                    [];
                                                selectedComplainTypeList.addIf(
                                                    selectedComplainType !=
                                                        null,
                                                    selectedComplainType!);
                                                final packageInfo =
                                                    await PackageInfo
                                                        .fromPlatform();
                                                final complainData =
                                                    <String, dynamic>{
                                                  'uId': currentResident?.docId,
                                                  'hostelDocId': currentResident
                                                      ?.hostelDocId,
                                                  'wing': currentResident?.wing,
                                                  'clothesUrl':
                                                      selectedComplainType ==
                                                              'Missing clothes'
                                                          ? []
                                                          : selectedImages,
                                                  'randomId': getRandomId(8)
                                                      .toUpperCase(),
                                                  'missingClothes':
                                                      selectedComplainType !=
                                                              'Missing clothes'
                                                          ? []
                                                          : selectedImages,
                                                  'floor':
                                                      currentResident?.floor,
                                                  'roomNo':
                                                      currentResident?.roomNo,
                                                  'remark': null,
                                                  'pickupDate':
                                                      selectedPickupDate,
                                                  'bedNo':
                                                      currentResident?.bedNo,
                                                  'residentName':
                                                      currentResident?.name,
                                                  'bunchDocId': null,
                                                  'pickUpScheduleDocId': null,
                                                  'complainType':
                                                      selectedComplainTypeList,
                                                  'complainDesc':
                                                      complainDescCtrl.text,
                                                  'status': 'Active',
                                                  'mobile': mobile,
                                                  'autoGenerate': false,
                                                  'resolved': false,
                                                  'createdAt': FieldValue
                                                      .serverTimestamp(),
                                                  'mobileVersion':
                                                      packageInfo.buildNumber,
                                                };
                                                final newComplainDoc =
                                                    FBFireStore.complaints
                                                        .doc();
                                                batch.set(newComplainDoc,
                                                    complainData);
                                                initiateActivity(
                                                  uId: currentResident?.docId,
                                                  type: ActivityType.complain,
                                                  typeDocId: newComplainDoc.id,
                                                  desc:
                                                      'Your complain for ${selectedComplainTypeList[0]} has been raised under ID: ${complainData['randomId']} on ${DateTime.now().convertToDDMMYY()} at ${DateTime.now().goodTime()}.',
                                                  userType: UserTypes.resident,
                                                  title: 'Complain',
                                                );
                                                await batch.commit();

                                                selectedComplainType = null;
                                                selectedImages.clear();
                                                complainDescCtrl.clear();
                                                selectedPickupDate = null;
                                                setState2(() {
                                                  loadOnSubmit2 = false;
                                                });
                                                setState(() {});
                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                }
                                              } on Exception catch (e) {
                                                setState2(() {
                                                  loadOnSubmit2 = false;
                                                });
                                                debugPrint(e.toString());
                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                }
                                              }
                                            },
                                            child: const Text(
                                              "Yes",
                                            ),
                                          ),
                                          TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text(
                                              "No",
                                            ),
                                          ),
                                        ],
                                );
                              });
                              /* return AlertButton(
                                title: 'Submit',
                                content:
                                    'Your complain will be raised. Are you sure you want to submit.',
                                onTapYes: () async {
                                  setState2(() {
                                    loadOnSubmit = true;
                                  });
                                  try {
                                    final batch = FBFireStore.fb.batch();
                                    if (currentResident?.mobile == null) {
                                      batch.update(
                                          FBFireStore.residents
                                              .doc(currentResident?.docId),
                                          {'mobile': mobile});
                                    }
                                    List<String> selectedComplainTypeList = [];
                                    selectedComplainTypeList.addIf(
                                        selectedComplainType != null,
                                        selectedComplainType!);
                                    final complainData = <String, dynamic>{
                                      'uId': currentResident?.docId,
                                      'hostelDocId':
                                          currentResident?.hostelDocId,
                                      'wing': currentResident?.wing,
                                      'clothesUrl': selectedComplainType ==
                                              'Missing clothes'
                                          ? []
                                          : selectedImages,
                                      'randomId': getRandomId(8).toUpperCase(),
                                      'missingClothes': selectedComplainType !=
                                              'Missing clothes'
                                          ? []
                                          : selectedImages,
                                      'floor': currentResident?.floor,
                                      'roomNo': currentResident?.roomNo,
                                      'remark': null,
                                      'pickupDate': selectedPickupDate,
                                      'bedNo': currentResident?.bedNo,
                                      'residentName': currentResident?.name,
                                      'bunchDocId': null,
                                      'pickUpScheduleDocId': null,
                                      'complainType': selectedComplainTypeList,
                                      'complainDesc': complainDescCtrl.text,
                                      'status': 'Active',
                                      'mobile': mobile,
                                      'autoGenerate': false,
                                      'resolved': false,
                                      'createdAt': FieldValue.serverTimestamp(),
                                    };
                                    final newComplainDoc =
                                        FBFireStore.complaints.doc();
                                    batch.set(newComplainDoc, complainData);
                                    initiateActivity(
                                      uId: currentResident?.docId,
                                      type: ActivityType.complain,
                                      typeDocId: newComplainDoc.id,
                                      desc:
                                          'Your complain for ${selectedComplainTypeList[0]} has been raised under ID: ${complainData['randomId']} on ${DateTime.now().convertToDDMMYY()} at ${DateTime.now().goodTime()}.',
                                      userType: UserTypes.resident,
                                      title: 'Complain',
                                    );
                                    await batch.commit();
                                    if (context.mounted) {
                                      Navigator.of(context).pop();
                                    }
                                    selectedComplainType = null;
                                    selectedImages.clear();
                                    complainDescCtrl.clear();
                                    selectedPickupDate = null;
                                    setState2(() {
                                      loadOnSubmit = false;
                                    });
                                  } on Exception catch (e) {
                                    setState2(() {
                                      loadOnSubmit = false;
                                    });
                                    debugPrint(e.toString());
                                    if (context.mounted) {
                                      Navigator.of(context).pop();
                                    }
                                  }
                                },
                                onTapNo: () {
                                  Navigator.of(context).pop();
                                },
                              );
                             */
                            },
                          );
                        },
                        bgColor: themeColor,
                      ),
                    ),
            ],
          )
        ],
      ),
    );
  }

  Future<dynamic> missingComplainTypeImageSheet(
      {required BuildContext context,
      required List<String> selectedImagesList}) {
    final allClothes = Get.find<HomeCtrl>().clothes;
    List<String> tempSelctedList = [];
    tempSelctedList.clear();
    tempSelctedList.addAllIf(selectedImagesList.isNotEmpty, selectedImagesList);
    return showModalBottomSheet(
      showDragHandle: false,
      backgroundColor: Colors.white,
      useRootNavigator: true,
      isScrollControlled: true,
      context: context,
      enableDrag: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return DraggableScrollableSheet(
            maxChildSize: .8,
            initialChildSize: .75,
            minChildSize: .2,
            expand: false,
            builder: (context, scrollController) {
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30))),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          "Select Clothes",
                          style: TextStyle(
                              fontSize: 20, fontWeight: FontWeight.w600),
                        ),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              // fixedSize: const Size.fromHeight(0),
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 30),
                              backgroundColor: const Color(0xFF01B49E),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5))),
                          onPressed: () async {
                            bool listsAreSame = false;
                            if (tempSelctedList.length ==
                                selectedImagesList.length) {
                              for (var element in tempSelctedList) {
                                if (selectedImagesList.contains(element)) {
                                  listsAreSame = true;
                                } else {
                                  listsAreSame = false;
                                }
                              }
                            }
                            if (listsAreSame) {
                              Navigator.of(context).pop();
                              return;
                            }

                            if (!listsAreSame) {
                              selectedImages.addAll(tempSelctedList);
                              Navigator.of(context).pop();
                              if (mounted) {
                                setState(() {});
                              }
                            }
                          },
                          child: const Text(
                            'Add',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    const Divider(
                      height: 0,
                      thickness: 2,
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.only(top: 12),
                        controller: scrollController,
                        physics: const ClampingScrollPhysics(),
                        child: Column(
                          children: [
                            StaggeredGrid.extent(
                              maxCrossAxisExtent: 150,
                              mainAxisSpacing: 10,
                              crossAxisSpacing: 10,
                              children: [
                                ...List.generate(
                                  allClothes.length,
                                  (index) {
                                    bool ischeck = tempSelctedList
                                        .contains(allClothes[index].docId);
                                    return Stack(children: [
                                      InkWell(
                                        onTap: () {
                                          if (ischeck) {
                                            tempSelctedList.removeWhere(
                                                (element) =>
                                                    element ==
                                                    allClothes[index].docId);
                                          } else {
                                            tempSelctedList.addIf(!ischeck,
                                                allClothes[index].docId);
                                          }
                                          setState2(() {});
                                        },
                                        child: Container(
                                          clipBehavior: Clip.antiAlias,
                                          decoration: ShapeDecoration(
                                            color: const Color(0xE0E0E0E0),
                                            shape: RoundedRectangleBorder(
                                              // side: const BorderSide(
                                              //     width: 1, color: Color(0xFFE0E0E0)),
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                          ),
                                          height: 130,
                                          child: CachedNetworkImage(
                                            imageUrl:
                                                allClothes[index].photoUrl,
                                            fit: BoxFit.cover,
                                            width: double.maxFinite,
                                          ),
                                        ),
                                      ),
                                      Checkbox(
                                        side:
                                            // const BorderSide(color: Color(0xFF01B49E)),
                                            const BorderSide(
                                                color: Colors.black),
                                        checkColor: Colors.white,
                                        fillColor: WidgetStatePropertyAll(
                                            ischeck
                                                ? const Color(0xFF01B49E)
                                                : Colors.white),
                                        value: ischeck,
                                        onChanged: (newValue) {
                                          if (newValue == false) {
                                            tempSelctedList.removeWhere(
                                                (element) =>
                                                    element ==
                                                    allClothes[index].docId);
                                          }
                                          if (newValue == true) {
                                            tempSelctedList.addIf(!ischeck,
                                                allClothes[index].docId);
                                          }
                                          setState2(() {});
                                        },
                                      )
                                    ]);
                                  },
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        });
      },
    );
  }

  Future<dynamic> otherComplainTypeImageSheet(BuildContext context) {
    return showModalBottomSheet(
      constraints: const BoxConstraints(maxHeight: 100),
      context: context,
      builder: (context) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: InkWell(
                onTap: () async {
                  Navigator.of(context).pop();
                  await imagePicker();
                  setState(() {});
                },
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          CupertinoIcons.photo_on_rectangle,
                          size: 30,
                          color: Color.fromARGB(255, 236, 198, 6),
                        ),
                        Text(
                          'Gallery',
                          style: TextStyle(fontSize: 15),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            // const VerticalDivider(),
            Expanded(
              child: InkWell(
                onTap: () async {
                  Navigator.of(context).pop();
                  await getImageFromCamera();
                  setState(() {});
                },
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          CupertinoIcons.camera,
                          size: 30,
                          color: Color.fromARGB(255, 28, 99, 223),
                        ),
                        Text(
                          'Camera',
                          style: TextStyle(fontSize: 15),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future imagePicker() async {
    final res = await ImagePickerService()
        .pickImageAndCrop(context, useCompressor: true);

    for (var element in res) {
      final image = await uploadComplainFile(element);
      selectedImages.addIf(image != null, image!);
    }

    setState(() {});
  }

  Future getImageFromCamera() async {
    final resCam = await ImagePickerService()
        .pickImageNewCamera(context, useCompressor: true);
    if (resCam != null) {
      final image = await uploadComplainFile(resCam);
      selectedImages.addIf(image != null, image!);
    }
    setState(() {});
  }
}

class DropDownSection extends StatelessWidget {
  const DropDownSection({
    super.key,
    this.selectedComplainType,
    required this.complainTypeList,
    this.onChanged,
  });

  final String? selectedComplainType;
  final List<String> complainTypeList;
  final Function(String?)? onChanged;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButtonFormField<String?>(
        elevation: 0,
        borderRadius: BorderRadius.circular(6),
        style: const TextStyle(color: Colors.black, fontSize: 15.5),
        hint: const Text('Select complain type'),
        decoration: const InputDecoration(
          hintStyle: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 15,
          ),

          filled: true,
          fillColor: Colors.white,
          focusColor: Colors.white,
          // contentPadding:
          //     EdgeInsets.symmetric(vertical: 5, horizontal: 10),
          border:
              OutlineInputBorder(borderSide: BorderSide(color: Colors.black12)),
          enabledBorder:
              OutlineInputBorder(borderSide: BorderSide(color: Colors.black12)),
          focusedBorder:
              OutlineInputBorder(borderSide: BorderSide(color: Colors.black12)),
        ),
        value: selectedComplainType,
        items: List.generate(
          complainTypeList.length,
          (index) {
            return DropdownMenuItem(
              value: complainTypeList[index],
              child: Text(complainTypeList[index]),
            );
          },
        ),
        onChanged: onChanged,
      ),
    );
  }
}

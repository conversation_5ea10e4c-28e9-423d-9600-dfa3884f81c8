import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:laundry_resident/shared/methods.dart';
import 'package:laundry_resident/views/Home/clothes_collecting.dart';
import '../../../models/complaints_model.dart';
import '../../../shared/firebse.dart';
import '../../../shared/theme.dart';

class ComplainCard extends StatelessWidget {
  const ComplainCard({
    super.key,
    required this.complain,
  });

  final ComplaintsModel complain;

  @override
  Widget build(BuildContext context) {
    bool isResolved = complain.resolved;

    final missingClothesUrl = ctrl.clothes
        .where((cloth) => complain.missingClothes.contains(cloth.docId))
        .map((e) => e.photoUrl)
        .toList();
    List<String> clothes = complain.complainType[0] == 'Missing clothes'
        ? missingClothesUrl
        : complain.clothesUrl;
    return InkWell(
      onTap: () {
        complainDetailsDialog(context, complain, clothes, isResolved);
      },
      child: Card(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(7),
        ),
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(complain.randomId),
                  Text(complain.createdAt.toDate().goodDayDate())
                ],
              ),
              const SizedBox(height: 7),
              Text(
                complain.complainType[0],
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 7),
              Text(
                complain.complainDesc ?? "",
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          shadowColor: Colors.transparent,
                          surfaceTintColor: Colors.transparent,
                          backgroundColor: const Color(0xFF01B49E),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          )),
                      onPressed: () async {
                        await FBFireStore.complaints
                            .doc(complain.docId)
                            .update({
                          'status': 'Resolved',
                          'resolved': true,
                          'isSatisfied': true,
                          'resolvedAt': FieldValue.serverTimestamp(),
                        });
                      },
                      child: const Text(
                        "Approve",
                        style: TextStyle(fontSize: 13, color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 5),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      style: OutlinedButton.styleFrom(
                          shadowColor: Colors.transparent,
                          surfaceTintColor: Colors.transparent,
                          // backgroundColor: Colors.yellow.shade700,
                          side: const BorderSide(color: Color(0xFF01B49E)),
                          // backgroundColor: Color(0xFF01B49E).withOpacity(.05),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          )),
                      onPressed: () async {
                        await FBFireStore.complaints
                            .doc(complain.docId)
                            .update({
                          'status': 'Resolved',
                          'isSatisfied': false,
                          'resolved': true,
                          'resolvedAt': FieldValue.serverTimestamp(),
                        });
                      },
                      child: const Text(
                        "Not Satisified",
                        textAlign: TextAlign.center,
                        style:
                            TextStyle(fontSize: 13, color: Color(0xFF01B49E)),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 5),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      style: TextButton.styleFrom(
                          shadowColor: Colors.transparent,
                          surfaceTintColor: Colors.transparent,
                          backgroundColor: Colors.red.shade50,
                          // side: BorderSide(color: Colors.redAccent),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          )),
                      onPressed: () async {
                        await FBFireStore.complaints
                            .doc(complain.docId)
                            .update({
                          'status': 'Active',
                        });
                      },
                      child: const Text(
                        "Reject",
                        style: TextStyle(fontSize: 13, color: Colors.red),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<dynamic> complainDetailsDialog(
    BuildContext context,
    ComplaintsModel complain,
    List<String> clothes,
    bool isResolved,
  ) {
    return showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          // backgroundColor: Colors.white,
          // surfaceTintColor: Colors.white,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Complain Details',
                  style: TextStyle(
                    fontSize: 20,
                    // fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 15),
                Text.rich(TextSpan(
                  children: [
                    const TextSpan(
                      text: "ID: ",
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 15,
                      ),
                    ),
                    TextSpan(
                      text: complain.randomId,
                      style: const TextStyle(
                        fontSize: 14,
                        // fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                )),
                const SizedBox(height: 12),
                Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(
                        text: "Type: ",
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14.5,
                        ),
                      ),
                      TextSpan(
                        text: complain.complainType[0],
                        style: const TextStyle(
                          fontSize: 14,
                          // fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(
                        text: "Status: ",
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14.5,
                        ),
                      ),
                      TextSpan(
                        text: isResolved ? "Resolved" : "Active",
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: isResolved ? Colors.green : Colors.redAccent
                            // fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(
                        text: "Description: ",
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14.5,
                        ),
                      ),
                      TextSpan(
                        text: complain.complainDesc ?? "",
                        style: const TextStyle(
                          fontSize: 14,
                          // fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isResolved) const SizedBox(height: 12),
                if (isResolved)
                  Text.rich(
                    TextSpan(
                      children: [
                        const TextSpan(
                          text: "Remark: ",
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14.5,
                          ),
                        ),
                        TextSpan(
                          text: complain.remark ?? "",
                          style: const TextStyle(
                            fontSize: 14,
                            // fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 12),
                // const Text(
                //   "Description",
                //   style: TextStyle(
                //     fontWeight: FontWeight.w500,
                //     fontSize: 14.5,
                //   ),
                // ),
                // const SizedBox(height: 5),
                // Container(
                //   padding:
                //       const EdgeInsets.symmetric(vertical: 12, horizontal: 10),
                //   width: double.maxFinite,
                //   height: 108,
                //   decoration: BoxDecoration(
                //     color: Colors.white,
                //     border: Border.all(color: const Color(0xff2d2e2f)),
                //     borderRadius: BorderRadius.circular(5),
                //   ),
                //   child: Text(
                //     complain.complainDesc ?? "",
                //     maxLines: 4,
                //     overflow: TextOverflow.ellipsis,
                //   ),
                // ),
                /*   Text.rich(
                  TextSpan(children: [
                    const TextSpan(
                        text: 'Complain ID: ',
                        style: TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 15.2)),
                    TextSpan(
                        text: complain.randomId,
                        style: const TextStyle(
                            fontWeight: FontWeight.w400, fontSize: 14.5))
                  ]),
                ),
                const SizedBox(height: 10),
                Text.rich(
                  TextSpan(children: [
                    const TextSpan(
                        text: 'Complain Type: ',
                        style: TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 15.2)),
                    TextSpan(
                        text: complain.complainType[0],
                        style: const TextStyle(
                            fontWeight: FontWeight.w400, fontSize: 14.5))
                  ]),
                ),
                const SizedBox(height: 10),
                const Text("Description:",
                    style:
                        TextStyle(fontWeight: FontWeight.w500, fontSize: 15.2)),
                const SizedBox(height: 5),
                Text(complain.complainDesc ?? "",
                    style: const TextStyle(
                        fontWeight: FontWeight.w400, fontSize: 14.5)), */
                // const SizedBox(height: 10),
                if (complain.clothesUrl.isNotEmpty ||
                    complain.missingClothes.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text("Clothes",
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14.5,
                          )),
                      const SizedBox(height: 5),
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 100,
                        mainAxisSpacing: 10,
                        crossAxisSpacing: 10,
                        children: [
                          ...List.generate(
                            clothes.length,
                            (index) {
                              return Container(
                                clipBehavior: Clip.antiAlias,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7)),
                                child: CachedNetworkImage(
                                  imageUrl: clothes[index],

                                  // height: 200,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                if (complain.clothesUrl.isNotEmpty ||
                    complain.missingClothes.isNotEmpty)
                  const SizedBox(height: 15),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        style: TextButton.styleFrom(
                          // backgroundColor: const Color.fromARGB(20, 0, 0, 0),
                          backgroundColor: themeColor,
                          elevation: 0,
                        ),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text(
                          'OK',
                          style: TextStyle(color: Colors.white, fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

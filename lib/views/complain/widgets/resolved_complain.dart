import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:laundry_resident/controllers/home_controller.dart';

import '../../../models/complaints_model.dart';
import '../../../shared/firebse.dart';
import 'complain_card.dart';

class ResolvedComplainPage extends StatefulWidget {
  const ResolvedComplainPage({super.key});

  @override
  State<ResolvedComplainPage> createState() => _ResolvedComplainPageState();
}

class _ResolvedComplainPageState extends State<ResolvedComplainPage> {
  ScrollController scrollCtrl = ScrollController();
  bool reloading = false;
  bool fetchingData = false;
  QueryDocumentSnapshot<Object?>? lastvisibleDoc;
  List<ComplaintsModel> allResolvedComplain = [];
  @override
  void initState() {
    super.initState();
    getFirstResolvedComplainData();
  }

  getFirstResolvedComplainData() async {
    fetchingData = true;
    setState(() {});

    final tempResolvedList = await FBFireStore.complaints
        .where('uId', isEqualTo: Get.find<HomeCtrl>().residentModel?.docId)
        .where('resolved', isEqualTo: true)
        .where('status', isEqualTo: 'Resolved')
        .orderBy('createdAt', descending: true)
        .limit(10)
        .get()
        .then((event) {
      if (event.docs.isNotEmpty) {
        lastvisibleDoc = event.docs.last;
        return event.docs.map((e) => ComplaintsModel.fromSnap(e)).toList();
      } else {
        return <ComplaintsModel>[];
      }
    });

    allResolvedComplain.addAll(tempResolvedList);
    fetchingData = false;
    setState(() {});
  }

  getOtherResolvedDocument() async {
    if (lastvisibleDoc == null || reloading) return;
    reloading = true;
    setState(() {});
    await Future.delayed(const Duration(milliseconds: 1500));
    final tempResolvedList = await FBFireStore.complaints
        .where('uId', isEqualTo: Get.find<HomeCtrl>().residentModel?.docId)
        .where('resolved', isEqualTo: true)
        .where('status', isEqualTo: 'Resolved')
        .orderBy('createdAt', descending: true)
        .startAfterDocument(lastvisibleDoc!)
        .limit(10)
        .get()
        .then((event) {
      if (event.docs.isEmpty) {
        lastvisibleDoc = null;
        return <ComplaintsModel>[];
      }
      lastvisibleDoc = event.docs.last;
      return event.docs.map((e) => ComplaintsModel.fromSnap(e)).toList();
    });
    allResolvedComplain.addAll(tempResolvedList);
    reloading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      scrollCtrl.addListener(() {
        if (scrollCtrl.position.maxScrollExtent == scrollCtrl.offset) {
          getOtherResolvedDocument();
        }
      });
    });
    return fetchingData
        ? const Center(
            child: CircularProgressIndicator(strokeWidth: 3.2),
          )
        : allResolvedComplain.isNotEmpty
            ? Stack(
                children: [
                  SingleChildScrollView(
                    padding: const EdgeInsets.all(15),
                    physics: const ClampingScrollPhysics(),
                    controller: scrollCtrl,
                    child: Padding(
                      padding: EdgeInsets.only(
                          bottom: lastvisibleDoc == null ? 0 : 20.0),
                      child: ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: allResolvedComplain.length,
                        separatorBuilder: (context, index) {
                          return const SizedBox(height: 15);
                        },
                        itemBuilder: (context, index) {
                          return ComplainCard(
                              complain: allResolvedComplain[index]);
                        },
                      ),
                    ),
                  ),
                  if (reloading)
                    const Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                                height: 28,
                                width: 28,
                                child: CircularProgressIndicator(
                                  strokeWidth: 3.2,
                                )),
                          ],
                        ),
                      ],
                    ),
                ],
              )
            : const Center(
                child: Text(
                  "No resolved complains",
                  style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
                ),
              );
  }
}

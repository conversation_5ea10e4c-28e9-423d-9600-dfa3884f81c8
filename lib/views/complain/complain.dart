import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/shared/theme.dart';
import 'package:laundry_resident/views/Home/clothes_collecting.dart';
import 'package:laundry_resident/views/complain/widgets/active_complain.dart';
import 'package:laundry_resident/views/complain/widgets/new_complain.dart';
import 'package:laundry_resident/views/complain/widgets/resolved_complain.dart';
import 'package:url_launcher/url_launcher_string.dart';

class ComplainPage extends StatefulWidget {
  const ComplainPage({super.key});

  @override
  State<ComplainPage> createState() => _ComplainPageState();
}

class _ComplainPageState extends State<ComplainPage> {
  @override
  Widget build(BuildContext context) {
    // final size = MediaQuery.sizeOf(context);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shadowColor: Colors.white,
        titleSpacing: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
        title: const Text(
          'Complaints',
          style: TextStyle(
            color: Color(0xFF4F4F4F),
            // fontSize: 24,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 10.0),
            child: IconButton(
              // color:,
              onPressed: () {
                launchUrlString("https://wa.me/91${ctrl.settings?.chatNumber}");
              },
              icon: Center(
                child: Image.asset(
                  'assets/images/whatsapp_logo.png',
                  height: 22,
                  width: 22,
                  color: Colors.green,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: DefaultTabController(
                length: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            // height: 70,
                            decoration: const BoxDecoration(
                                color: Colors.white,
                                boxShadow: [
                                  BoxShadow(
                                      blurRadius: 10,
                                      color: Colors.black12,
                                      spreadRadius: 5,
                                      offset: Offset(0, 0)),
                                ]),
                            child: TabBar(
                              overlayColor: const WidgetStatePropertyAll(
                                  Colors.transparent),
                              tabAlignment: TabAlignment.start,
                              // dragStartBehavior: DragStartBehavior.down,
                              // dragStartBehavior: ,
                              unselectedLabelStyle: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 15,
                              ),
                              labelStyle: const TextStyle(
                                color: themeColor,
                                fontSize: 16.5,
                              ),
                              // physics: NeverScrollableScrollPhysics(),

                              padding: EdgeInsets.zero,
                              isScrollable: true,
                              indicatorColor: themeColor,
                              indicatorSize: TabBarIndicatorSize.tab,
                              tabs: const [
                                Tab(
                                  child: TabBarImgTxt(
                                    text: 'New',
                                    icon: CupertinoIcons.add,
                                  ),
                                ),
                                Tab(
                                  child: TabBarImgTxt(
                                    text: 'Active',
                                    icon: CupertinoIcons.exclamationmark_circle,
                                  ),
                                ),
                                Tab(
                                  child: TabBarImgTxt(
                                    text: 'Resolved',
                                    icon: CupertinoIcons.checkmark_alt_circle,
                                  ),
                                ),
                                // Tab(
                                //   child: TabBarImgTxt(
                                //     text: 'Missing Clothes',
                                //     icon: Icons.abc,
                                //   ),
                                // ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    Expanded(
                      child: Container(
                        color: Colors.white,
                        child: const TabBarView(
                          physics: NeverScrollableScrollPhysics(),
                          children: [
                            NewComplainPage(),
                            ActiveComplainPage(),
                            ResolvedComplainPage(),
                            // MissingClothDeliveryPage(),
                          ],
                        ),
                      ),
                    ),
                  ],
                )),
          )
        ],
      ),
    );
  }
}

class TabBarImgTxt extends StatelessWidget {
  const TabBarImgTxt({
    super.key,
    required this.text,
    required this.icon,
  });
  final String text;
  final IconData icon;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 185,
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 24),
            const SizedBox(width: 10),
            Text(
              text,
              style: const TextStyle(fontSize: 18),
            )
          ],
        ),
      ),
    );
  }
}

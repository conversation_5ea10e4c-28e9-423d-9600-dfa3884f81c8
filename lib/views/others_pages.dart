import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/shared/firebse.dart';
import 'package:laundry_resident/shared/router.dart';
import 'package:unique_identifier/unique_identifier.dart';

const oldDeviceLine =
    'Your account is already logged in on another device. Want to use on this device ?';

class DeviceCheckPage extends StatefulWidget {
  const DeviceCheckPage({super.key, required this.isNewUser});
  final bool isNewUser;
  @override
  State<DeviceCheckPage> createState() => _DeviceCheckPageState();
}

class _DeviceCheckPageState extends State<DeviceCheckPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(20),
          height: MediaQuery.sizeOf(context).height,
          decoration: const BoxDecoration(
              image: DecorationImage(
                  image: AssetImage("assets/images/Image.png"),
                  opacity: .75,
                  fit: BoxFit.cover)),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              // crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Text(
                  "NEW DEVICE",
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 15),
                const Text(
                  oldDeviceLine,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(7))),
                        onPressed: () async {
                          await FBFireStore.residents
                              .doc(FBAuth.auth.currentUser?.uid)
                              .update({
                            'deviceId': await UniqueIdentifier.serial,
                          });
                          if (context.mounted) context.go(homeRoute);
                        },
                        child: const Text(
                          "USE HERE",
                          style: TextStyle(fontSize: 18, letterSpacing: .7),
                        )),
                    const SizedBox(width: 15),
                    ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.redAccent,
                            foregroundColor: Colors.white,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(7))),
                        onPressed: () async {
                          await FBAuth.auth.signOut();
                          if (context.mounted) context.go(Routes.auth);
                        },
                        child: const Text(
                          "LOGOUT",
                          style: TextStyle(fontSize: 18, letterSpacing: .7),
                        )),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ConnectivityPage extends StatelessWidget {
  const ConnectivityPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        padding: const EdgeInsets.all(40),
        height: MediaQuery.sizeOf(context).height,
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage("assets/images/Image.png"),
                opacity: .75,
                fit: BoxFit.cover)),
        child: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/no-signal_4234737.png',
              height: 100,
            ),
            const SizedBox(height: 20),
            const Text(
              "Unable to connect to the server. Please check your internet connection and try again.",
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
          ],
        )),
      ),
    );
  }
}

class UpdateVersionScreen extends StatelessWidget {
  const UpdateVersionScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        padding: const EdgeInsets.all(40),
        height: MediaQuery.sizeOf(context).height,
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage("assets/images/Image.png"),
                opacity: .75,
                fit: BoxFit.cover)),
        child: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/version_upgrade.png',
              height: 100,
            ),
            const SizedBox(height: 20),
            const Text(
              "Version not supported. Please upgrade app version from Play Store/App Store.",
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
          ],
        )),
      ),
    );
  }
}

// ignore_for_file: no_wildcard_variable_uses

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/models/hostel_model_ori.dart';
import 'package:laundry_resident/models/resident.dart';
import 'package:laundry_resident/shared/theme.dart';
import 'package:laundry_resident/views/common/alert_button.dart';
import 'package:url_launcher/url_launcher_string.dart';
import '../../services/image_picker.dart';
import '../../shared/firebse.dart';
import '../../shared/methods.dart';
import '../../shared/router.dart';

class Account extends StatefulWidget {
  const Account({super.key});

  @override
  State<Account> createState() => _AccountState();
}

class _AccountState extends State<Account> {
  SelectedImage? pickedImage;
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<HomeCtrl>(builder: (_) {
      bool reviewExist = _.review != null;
      return Container(
        height: size.height,
        decoration: const BoxDecoration(
            image: DecorationImage(
                repeat: ImageRepeat.repeatY,
                opacity: .5,
                image: AssetImage('assets/images/Image.png'))),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            surfaceTintColor: Colors.transparent,
            // shadowColor: Colors.black12,
            // elevation: 0,
            titleSpacing: 0,
            centerTitle: false,
            leading: IconButton(
              onPressed: () => context.pop(),
              icon: const Icon(Icons.arrow_back),
            ),
            title: const Text(
              'My Account',
              style: TextStyle(
                color: Color(0xFF4F4F4F),
                // fontSize: 24,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
              ),
            ),
            actions: [
              Padding(
                padding: const EdgeInsets.only(right: 12.0),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 5, horizontal: 11),
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    // color: Colors.black38,
                    // color: themeColor,
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Text.rich(TextSpan(children: [
                    const TextSpan(
                      text: 'Balance: ',
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w400,
                        color: Colors.white,
                        // color: Color(0xFF4F4F4F),
                      ),
                    ),
                    const TextSpan(
                      text: '₹ ',
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                        // color: Color(0xFF4F4F4F),
                      ),
                    ),
                    TextSpan(
                      text: _.residentModel!.balance.toString(),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        // color: Color(0xFF4F4F4F),
                      ),
                    ),
                  ])),
                ),
              ),
            ],
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(15, 5, 15, 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: SizedBox(
                    height: 120,
                    width: 120,
                    child: Stack(
                      children: [
                        Container(
                          width: 120,
                          height: 120,
                          clipBehavior: Clip.antiAlias,
                          decoration: const BoxDecoration(
                              shape: BoxShape.circle, color: themeColor),
                          child: CachedNetworkImage(
                            imageUrl: _.residentModel?.profileImage ?? "",
                            fit: BoxFit.cover,
                            placeholder: (context, url) {
                              return const CircularProgressIndicator();
                            },
                            errorWidget: (context, url, error) {
                              return Center(
                                child: Text(
                                  _.residentModel!.name[0].toUpperCase(),
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                      color: Colors.white, fontSize: 40),
                                ),
                              );
                            },
                          ),
                        ),
                        /*  Align(
                          // alignment: Alignment.bottomRight,
                          alignment: const Alignment(1.25, 0),
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              CircleAvatar(
                                radius: 20,
                                backgroundColor: themeColor,
                                child: IconButton(
                                  onPressed: () async {
                                    showModalBottomSheet(
                                      constraints:
                                          const BoxConstraints(maxHeight: 100),
                                      context: context,
                                      builder: (context) {
                                        return Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Expanded(
                                              child: InkWell(
                                                onTap: () async {
                                                  Navigator.of(context).pop();
                                                  await imagePicker();
                                                  setState(() {});
                                                },
                                                child: const Padding(
                                                  padding: EdgeInsets.all(8.0),
                                                  child: Center(
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Icon(
                                                          CupertinoIcons
                                                              .photo_on_rectangle,
                                                          size: 30,
                                                          color: Color.fromARGB(
                                                              255, 236, 198, 6),
                                                        ),
                                                        Text(
                                                          'Gallery',
                                                          style: TextStyle(
                                                              fontSize: 15),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            // const VerticalDivider(),
                                            Expanded(
                                              child: InkWell(
                                                onTap: () async {
                                                  Navigator.of(context).pop();
                                                  await getImageFromCamera();
                                                  setState(() {});
                                                },
                                                child: const Padding(
                                                  padding: EdgeInsets.all(8.0),
                                                  child: Center(
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Icon(
                                                          CupertinoIcons.camera,
                                                          size: 30,
                                                          color: Color.fromARGB(
                                                              255, 28, 99, 223),
                                                        ),
                                                        Text(
                                                          'Camera',
                                                          style: TextStyle(
                                                              fontSize: 15),
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                    );
                                  },
                                  icon: const Icon(Icons.edit_outlined,
                                      color: Colors.white, size: 23),
                                ),
                              ),
                            ],
                          ),
                        ),
                      */
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Center(
                    child: Text(
                  _.residentModel?.name ?? "",
                  style: const TextStyle(
                      color: Color(0xff4F4F4F),
                      fontWeight: FontWeight.bold,
                      fontSize: 20),
                )),
                const SizedBox(height: 5),
                Center(
                    child: Text(
                  FBAuth.auth.currentUser?.email ?? "",
                  style:
                      const TextStyle(color: Color(0xff4F4F4F), fontSize: 15),
                )),
                const SizedBox(height: 8),
                Center(
                  child: Container(
                    height: 40,
                    width: 120,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30),
                        color: reviewExist ? Colors.grey[350] : themeColor),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(30),
                      onTap: reviewExist
                          ? () {
                              const snackBar = SnackBar(
                                content: Text(
                                    'Unable to edit. Profile under Review.'),
                                duration: Duration(milliseconds: 1500),
                              );
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(snackBar);
                              return;
                            }
                          : () => context.push(Routes.profile,
                              extra: Get.find<HomeCtrl>().residentModel),
                      child: const Center(
                          child: Text(
                        "Edit Profile",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 15,
                            fontWeight: FontWeight.w500),
                      )),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Tiles(reviewExist: reviewExist),
                // const SizedBox(height: 5),
                // _Tiles(
                //   pendingRequest: _.review != null ? 1 : 0,
                //   onHelp: () {
                //     Get.find<HomeCtrl>().selectedIndex = 2;
                //     context.go(Routes.help);
                //   },
                //   enabled: !(_.review != null),
                //   onProfile: () =>
                //       context.push(Routes.profile, extra: _.residentModel),
                //   onReview: () => context.push(Routes.review, extra: _.residentModel),
                // ),
                /*   ListTile(
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return AlertButton(
                            title: "Alert",
                            content: "Are you sure you want to logout?",
                            onTapYes: () async {
                              await FBAuth.auth.signOut();
                              if (context.mounted) {
                                Navigator.of(context).pop();
                                context.go(Routes.login);
                              }
                            },
                            onTapNo: () {
                              Navigator.of(context).pop();
                            });
                      },
                    );
                  },
                  // contentPadding:
                  //     const EdgeInsets.symmetric(vertical: 5, horizontal: 25),
                  contentPadding: const EdgeInsets.fromLTRB(15, 5, 25, 5),
                  tileColor: const Color(0xffE9FFFC),
                  leading: CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.transparent,
                    child: Image.asset(
                      'assets/images/logoutB.png',
                      height: 20,
                      width: 20,
                      fit: BoxFit.cover,
                    ),
                  ),
                  // leading: SizedBox(
                  //   height: 20,
                  //   width: 20,
                  //   child:
                  //       Image.asset('assets/images/logoutB.png', fit: BoxFit.cover),
                  // ),
                  title: const Text(
                    "Logout",
                    style: TextStyle(color: Color(0xff01B49E), fontSize: 16),
                  ),
                ),
               */
              ],
            ),
          ),
        ),
      );
    });
  }

  Future imagePicker() async {
    final res =
        await ImagePickerService().pickImageNew(context, useCompressor: true);
    final photoUrl = await uploadProfileFile(res!);
    await FBFireStore.residents
        .doc(Get.find<HomeCtrl>().residentModel?.docId)
        .update({'profileImage': photoUrl});
    // if (res != null) {
    //   pickedImage = res;
    // }
    setState(() {});
  }

  Future getImageFromCamera() async {
    final resCam = await ImagePickerService()
        .pickImageNewCamera(context, useCompressor: true);
    final photoUrl = await uploadProfileFile(resCam!);
    await FBFireStore.residents
        .doc(Get.find<HomeCtrl>().residentModel?.docId)
        .update({'profileImage': photoUrl});
    setState(() {
      // if (resCam != null) {
      //   pickedImage = resCam;
      // }
    });
  }
}

class Tiles extends StatelessWidget {
  const Tiles({super.key, required this.reviewExist});
  final bool reviewExist;
  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
    // print(ctrl.review);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TileDemo(
            onTap: () {
              showModalBottomSheet(
                // backgroundColor: Colors.white,
                context: context,
                builder: (context) {
                  ResidentModel? resident = ctrl.residentModel;
                  HostelModel? hostel = ctrl.hostelModel;
                  return residentDataBottomSheet(context, resident, hostel);
                },
              );
            },
            icon: CupertinoIcons.person,
            title: 'View Profile'),
        if (reviewExist)
          TileDemo(
            onTap: () => context.push(Routes.review, extra: ctrl.residentModel),
            icon: CupertinoIcons.exclamationmark_circle,
            title: 'Pending Approval',
            titleColor: Colors.redAccent,
          ),
        // TileDemo(
        //     onTap: () {
        //       ctrl.selectedIndex = 2;
        //       context.push(Routes.help);
        //     },
        //     icon: CupertinoIcons.question_circle,
        //     title: 'Help'),
        // TileDemo(onTap: () {}, icon: CupertinoIcons.settings, title: 'Setting'),
        TileDemo(
            onTap: () {
              showDialog(
                context: context,
                builder: (context) {
                  return AlertDialog(
                    backgroundColor: Colors.white,
                    surfaceTintColor: Colors.white,
                    // insetPadding: const EdgeInsets.symmetric(
                    //     vertical: 10, horizontal: 10),
                    titlePadding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 20),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                    actionsPadding: const EdgeInsets.fromLTRB(0, 5, 15, 10),
                    title: const Text("Contact"),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ContactUsTileDemo(
                          leadingIcon: 'assets/images/call.png',
                          title: '+91 ${ctrl.settings?.callNumber}',
                          onTap: () {
                            launchUrlString(
                                "tel://91${ctrl.settings?.callNumber}");
                            Navigator.of(context).pop();
                          },
                        ),
                        const SizedBox(height: 8),
                        ContactUsTileDemo(
                          leadingIcon: 'assets/images/email.png',
                          title: '<EMAIL>',
                          onTap: () {
                            launchUrlString('mailto:${ctrl.settings?.email}');
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    ),
                    actions: [
                      TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: const Text('Close'))
                    ],
                  );
                },
              );
            },
            icon: CupertinoIcons.phone,
            title: 'Contact Us'),
        TileDemo(
          onTap: () {
            showDialog(
              context: context,
              builder: (context) {
                return AlertButton(
                    title: "Logout",
                    content: "Are you sure you want to leave?",
                    onTapYes: () async {
                      await FBAuth.auth.signOut();
                      if (context.mounted) {
                        Navigator.of(context).pop();
                        context.go(Routes.auth);
                      }
                    },
                    onTapNo: () {
                      Navigator.of(context).pop();
                    });
              },
            );
          },
          icon: CupertinoIcons.power,
          title: 'Logout',
          trailing: false,
        ),
        TileDemo(
          onTap: () async {
            bool res = await showDialog(
              context: context,
              builder: (context) {
                return AlertDialog(
                  title: const Center(
                    child: Icon(
                      CupertinoIcons.exclamationmark_octagon,
                      color: Colors.redAccent,
                      size: 40,
                    ),
                  ),
                  content: const Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "Are you sure you want to delete your account?",
                        textAlign: TextAlign.center,
                      ),
                      Text(
                        "This action is irreversible, and all your data will be permanently lost.",
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                  actionsAlignment: MainAxisAlignment.center,
                  actions: [
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                              onPressed: () async {
                                await FBAuth.auth.currentUser?.delete();
                                await FBAuth.auth.signOut();
                                if (context.mounted) {
                                  Navigator.of(context).pop(true);
                                  context.go(Routes.auth);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                          content: Text(
                                              'Your account has been deleted permanently')));
                                }
                              },
                              child: const Text("Delete")),
                        ),
                        Expanded(
                          child: TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(false);
                              },
                              child: const Text("Cancel")),
                        ),
                      ],
                    ),
                  ],
                );
              },
            );
            if (res) {
              var list = FBStorage.fbstore2;
              var clothFolder =
                  list.child(ctrl.residentModel?.docId ?? "").child('clothes');
              var profileFolder =
                  list.child(ctrl.residentModel?.docId ?? "").child('profile');
              var proofFolder =
                  list.child(ctrl.residentModel?.docId ?? "").child('proof');
              var complainFolder =
                  list.child(ctrl.residentModel?.docId ?? "").child('complain');
              await clothFolder.listAll().then((res) {
                for (var element in res.items) {
                  element.delete();
                }
              });
              await complainFolder.listAll().then((res) {
                for (var element in res.items) {
                  element.delete();
                }
              });
              await profileFolder.listAll().then((res) {
                for (var element in res.items) {
                  element.delete();
                }
              });
              await proofFolder.listAll().then((res) {
                for (var element in res.items) {
                  element.delete();
                }
              });

              deleteNestedSubcollections(ctrl.residentModel?.docId);

              await FBFireStore.residents
                  .doc(ctrl.residentModel?.docId)
                  .delete();

              // await FBStorage.fbstore2
              //     .child(ctrl.residentModel?.docId ?? "")
              //     .delete();
              // await FBFireStore.residents
              //     .doc(ctrl.residentModel?.docId)
              //     .delete();
            }
          },
          icon: CupertinoIcons.exclamationmark,
          title: 'Delete Account',
          trailing: false,
        ),
      ],
    );
  }

  void deleteNestedSubcollections(String? id) {
    if (id != null) {
      Future<QuerySnapshot> clothes =
          FBFireStore.residents.doc(id).collection("clothes").get();
      clothes.then((value) {
        for (var element in value.docs) {
          FBFireStore.residents
              .doc(id)
              .collection("clothes")
              .doc(element.id)
              .delete()
              // ignore: avoid_print
              .then((value) => print("success"));
        }
      });
      Future<QuerySnapshot> notifications =
          FBFireStore.residents.doc(id).collection("notifications").get();
      notifications.then((value) {
        for (var element in value.docs) {
          FBFireStore.residents
              .doc(id)
              .collection("notifications")
              .doc(element.id)
              .delete()
              // ignore: avoid_print
              .then((value) => print("success"));
        }
      });
      Future<QuerySnapshot> pickUpSchedules = FBFireStore.pickUpSchedules
          .where('uId', isEqualTo: id)
          .where('pickedUp', isEqualTo: false)
          .get();
      pickUpSchedules.then((value) {
        for (var element in value.docs) {
          FBFireStore.pickUpSchedules
              .doc(element.id)
              .delete()
              // ignore: avoid_print
              .then((value) => print("success"));
        }
      });
    }
  }

  Widget residentDataBottomSheet(
      BuildContext context, ResidentModel? resident, HostelModel? hostel) {
    final size = MediaQuery.sizeOf(context).height;
    return Container(
      height: size / 1.9,
      width: double.maxFinite,
      decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(30), topRight: Radius.circular(30))),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(20, 30, 20, 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                  width: double.maxFinite,
                  padding: const EdgeInsets.fromLTRB(10, 10, 0, 10),
                  decoration: BoxDecoration(
                      color: themeColor.withOpacity(.1),
                      borderRadius: BorderRadius.circular(4)),
                  /*  padding: const EdgeInsets.fromLTRB(10, 10, 0, 10),
                  decoration: BoxDecoration(
                      border: const Border(
                          left: BorderSide(color: themeColor, width: 2)),
                      borderRadius: const BorderRadius.horizontal(
                          left: Radius.circular(4)),
                      gradient: LinearGradient(colors: [
                        themeColor.withOpacity(.1),
                        // Colors.grey.withOpacity(.1),
                        Colors.white10,
                        // Colors.grey.shade100
                      ], stops: const [
                        .01,
                        .4
                      ])), */
                  child: const Text("Personal Details",
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 17.5,
                        // fontWeight: FontWeight.w600,
                        letterSpacing: 1,
                      ))),
              const SizedBox(height: 5),
              _bottomDataTile(
                  leadingIcon: CupertinoIcons.person,
                  text: resident?.name ?? ""),
              _bottomDataTile(
                  leadingIcon: Icons.email_outlined,
                  text: FBAuth.auth.currentUser?.email ?? ''),
              _bottomDataTile(
                  leadingIcon: CupertinoIcons.phone,
                  text: resident?.mobile ?? '-'),
              _bottomDataTile(
                  leadingIcon: Icons.numbers, text: resident?.hostelId ?? ''),
              const SizedBox(height: 15),
              Container(
                  // padding: EdgeInsets.only(left: 10),
                  width: double.maxFinite,
                  padding: const EdgeInsets.fromLTRB(10, 10, 0, 10),
                  decoration: BoxDecoration(
                      color: themeColor.withOpacity(.1),
                      borderRadius: BorderRadius.circular(4)),
                  /*      decoration: BoxDecoration(
                      border: const Border(
                          left: BorderSide(color: themeColor, width: 2)),
                      borderRadius: const BorderRadius.horizontal(
                          left: Radius.circular(4)),
                      gradient: LinearGradient(colors: [
                        themeColor.withOpacity(.1),
                        // Colors.grey.withOpacity(.1),
                        // Colors.grey.shade100
                        Colors.white10,
                      ], stops: const [
                        .01,
                        .4
                      ])), */
                  child: const Text("Hostel Details",
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 17.5,
                        // fontWeight: FontWeight.w600,
                        letterSpacing: 1,
                      ))),
              const SizedBox(height: 5),
              _bottomDataTile(
                  leadingIcon: CupertinoIcons.building_2_fill,
                  text: hostel?.name ?? ""),
              _bottomDataTile(
                  leadingIcon: CupertinoIcons.list_number,
                  text: resident?.floor.toString() ?? ''),
              _bottomDataTile(
                  leadingIcon: Icons.door_sliding_outlined,
                  text: resident?.roomNo.toString() ?? ''),
              _bottomDataTile(
                  leadingIcon: CupertinoIcons.bed_double,
                  text: resident?.bedNo.toString() ?? ''),
            ],
          ),
        ),
      ),
    );
  }

  Widget _bottomDataTile(
      {required String text, required IconData leadingIcon}) {
    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 30,
            width: 30,
            decoration: const BoxDecoration(
                shape: BoxShape.circle, color: Color(0xffE9FFFC)),
            child: Center(
              child: Icon(
                leadingIcon,
                size: 20,
                color: themeColor,
              ),
            ),
          ),
          // Text(
          //   leadingText,
          //   style: const TextStyle(
          //       fontSize: 16, fontWeight: FontWeight.w600, color: Colors.black87),
          // ),
          // const Text(
          //   " :",
          //   style: TextStyle(
          //       fontSize: 16, fontWeight: FontWeight.w600, color: Colors.black),
          // ),
          const SizedBox(width: 8),
          Text(
            text,
            style: const TextStyle(fontSize: 15),
          ),
        ],
      ),
    );
  }
}

class ContactUsTileDemo extends StatelessWidget {
  const ContactUsTileDemo(
      {super.key,
      required this.leadingIcon,
      required this.title,
      required this.onTap,
      this.iconColor});
  final String leadingIcon;
  final String title;
  final Function() onTap;
  final Color? iconColor;
  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 2),

      // minLeadingWidth: 20,
      // leading: Icon(
      //   leadingIcon,
      //   color: iconColor,
      // ),

      leading: SizedBox(
        height: 25,
        width: 25,
        child: Image.asset(leadingIcon),
      ),

      title: Text(
        title,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: const TextStyle(fontSize: 15),
      ),
      onTap: onTap,
      trailing: const CupertinoListTileChevron(),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
        // side: BorderSide(color: Colors.grey.shade400),
      ),
    );
  }
}

class TileDemo extends StatelessWidget {
  const TileDemo({
    super.key,
    required this.onTap,
    required this.icon,
    required this.title,
    this.titleColor,
    this.trailing = true,
  });
  final Function() onTap;
  final IconData icon;
  final String title;
  final Color? titleColor;
  final bool trailing;
  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      contentPadding: const EdgeInsets.fromLTRB(15, 5, 25, 5),
      leading: CircleAvatar(
          radius: 20,
          // backgroundColor: themeColor.withOpacity(.07),
          backgroundColor: const Color(0xffE9FFFC),
          child: Icon(icon, color: themeColor)),
      title: Text(title,
          style: TextStyle(
              color: titleColor ?? const Color(0xff4F4F4F), fontSize: 16)),
      trailing: trailing
          ? Container(
              height: 30,
              width: 30,
              decoration: BoxDecoration(
                  shape: BoxShape.circle, color: Colors.grey.shade200),
              child: const Center(child: CupertinoListTileChevron()))
          : null,
    );
  }
}

/* class _Tiles extends StatelessWidget {
  const _Tiles({
    required this.onProfile,
    required this.onHelp,
    required this.onReview,
    required this.pendingRequest,
    this.enabled = true,
  });
  final Function() onProfile;
  final Function() onHelp;
  final Function()? onReview;
  final int pendingRequest;
  final bool enabled;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: enabled
              ? onProfile
              : () {
                  showAppSnackBar(
                      'Unable to edit. Profile already under review');
                },
          child: ListTile(
            // onTap: enabled
            //     ? onProfile
            //     : () {
            //         print(">>>>$enabled");
            //         showAppSnackBar(
            //             'Unable to edit. Profile already under review');
            //       },
            contentPadding: const EdgeInsets.fromLTRB(25, 0, 25, 5),
            tileColor: Colors.white,
            leading: const Icon(
              CupertinoIcons.profile_circled,
              color: Color(0xff333333),
              size: 27,
            ),
            enabled: enabled,
            title: const Text("Profile",
                style: TextStyle(color: Color(0xff4F4F4F), fontSize: 16)),
            trailing: const CupertinoListTileChevron(),
          ),
        ),
        const Divider(
          thickness: 1,
          color: Color(0xffF2EEEE),
          height: 0,
        ),
        if (pendingRequest != 0)
          ListTile(
            // onTap: () {},
            onTap: onReview,
            contentPadding: const EdgeInsets.fromLTRB(25, 0, 25, 5),
            tileColor: Colors.white,
            leading: const Icon(
              CupertinoIcons.exclamationmark_circle,
              color: Color(0xff333333),
              size: 27,
            ),
            title: const Text(
              "Pending Request",
              style: TextStyle(color: Color(0xff4F4F4F), fontSize: 16),
            ),
            trailing: const CupertinoListTileChevron(),
          ),
        if (pendingRequest != 0)
          const Divider(
            thickness: 1,
            color: Color(0xffF2EEEE),
            height: 0,
          ),
        ListTile(
          onTap: onHelp,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 5, horizontal: 25),
          tileColor: Colors.white,
          leading: const Icon(
            CupertinoIcons.question_circle,
            color: Color(0xff333333),
            size: 27,
          ),
          title: const Text(
            "Help",
            style: TextStyle(color: Color(0xff4F4F4F), fontSize: 16),
          ),
          trailing: const CupertinoListTileChevron(),
        ),
        const Divider(
          thickness: 1,
          color: Color(0xffF2EEEE),
          height: 0,
        ),
        ListTile(
          onTap: () {},
          contentPadding:
              const EdgeInsets.symmetric(vertical: 5, horizontal: 25),
          tileColor: Colors.white,
          leading: const Icon(
            CupertinoIcons.settings,
            color: Color(0xff333333),
            size: 27,
          ),
          title: const Text(
            "Settings",
            style: TextStyle(color: Color(0xff4F4F4F), fontSize: 16),
          ),
          trailing: const CupertinoListTileChevron(),
        ),
        const Divider(
          thickness: 1,
          color: Color(0xffF2EEEE),
          height: 0,
        ),
        ListTile(
          onTap: () {},
          contentPadding:
              const EdgeInsets.symmetric(vertical: 5, horizontal: 25),
          tileColor: Colors.white,
          leading: const Icon(
            CupertinoIcons.phone,
            color: Color(0xff333333),
            size: 27,
          ),
          title: const Text(
            "Contact Us",
            style: TextStyle(color: Color(0xff4F4F4F), fontSize: 16),
          ),
          trailing: const CupertinoListTileChevron(),
        ),
      ],
    );
  }
}
 */
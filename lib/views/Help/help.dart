import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class Help extends StatelessWidget {
  const Help({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shadowColor: Colors.white,
        titleSpacing: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
        title: const Text(
          'Help & Support',
          style: TextStyle(
            color: Color(0xFF4F4F4F),
            // fontSize: 24,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: const SingleChildScrollView(
          padding: EdgeInsets.all(20.0),
          child: <PERSON>umn(
            children: [
              Center(child: Text('Help')),
            ],
          )),
    );
  }
}

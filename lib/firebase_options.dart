// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAopiTpR1CkOlT74wxV7NlvoiRmcfMhC7U',
    appId: '1:1036649922444:web:bccef8113bb1d4d63720a9',
    messagingSenderId: '1036649922444',
    projectId: 'pu-laundry',
    authDomain: 'pu-laundry.firebaseapp.com',
    storageBucket: 'pu-laundry.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAa_xYOWbcJui19QuZKKvYuLrEtNlJyanI',
    appId: '1:1036649922444:android:b0889b9e38fae8cc3720a9',
    messagingSenderId: '1036649922444',
    projectId: 'pu-laundry',
    storageBucket: 'pu-laundry.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBzaSu0CecPIC-YsyWMcUta8Bj5Ump8qFw',
    appId: '1:1036649922444:ios:b697eadb67b546043720a9',
    messagingSenderId: '1036649922444',
    projectId: 'pu-laundry',
    storageBucket: 'pu-laundry.appspot.com',
    iosBundleId: 'com.diwizon.pulresident',
  );

}
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fb = FirebaseFirestore.instance;
  // // static final sets = fb.collection('sets').doc('sets');
  static final settings = fb.collection('sets').doc('sets');
  // static final outlets = fb.collection('outlet');
  // static final categories = fb.collection('category');
  // static final addOn = fb.collection('addon');
  // static final foods = fb.collection('food');
  // // static final riders = fb.collection('rider');
  // static final sets = fb.collection('sets').doc('sets');
  static final hostels = fb.collection('hostels');
  static final residents = fb.collection('residents');
  static final savedusers = fb.collection('savedusers');
  static final managers = fb.collection('managers');
  static final complaints = fb.collection('complaints');
  static final schedules = fb.collection('schedules');
  static final review = fb.collection('review');
  static final pickUpSchedules = fb.collection('pickupschedules');
  static final activities = fb.collection('activities');
  static final notifies = fb.collection('notifies');
  static final paymentOrder = fb.collection('paymentOrder');
  static final enrollments = fb.collection('enrollments');
}

class FBStorage {
  static final fbstore = FirebaseStorage.instance;
  static final fbstore2 = fbstore.ref();
  // static final clothes = fbstore
  //     .child(Get.find<HomeCtrl>().residentModel?.docId ?? "")
  //     .child('clothes');
  // static final profile = fbstore
  //     .child(Get.find<HomeCtrl>().residentModel?.docId ?? "")
  //     .child('profile');
  // static final proof = fbstore.child('proof');
  // static final otherCertis = fb.ref().child('otherCertis');
}

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}

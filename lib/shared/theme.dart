import 'package:flutter/material.dart';

const themeColor = Color.fromARGB(255, 20, 189, 172);
const themeColor2 = Color(0xff01B49E);

final themeData = ThemeData(
    colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xff01B49E)),
    useMaterial3: true,
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.white,
      shadowColor: Colors.white,
    ),
    scaffoldBackgroundColor: Colors.white,
    bottomSheetTheme: const BottomSheetThemeData(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.white,
    ),
    bottomNavigationBarTheme:
        BottomNavigationBarThemeData(backgroundColor: Colors.grey[50]));

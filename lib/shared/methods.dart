import 'dart:math';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:laundry_resident/shared/firebse.dart';
import '../controllers/home_controller.dart';
import '../services/image_picker.dart';
import 'const.dart';

extension StringExtensions on String {
  String capitalizeFirst() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}

const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomId(int length) => String.fromCharCodes(Iterable.generate(
    length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

bool isLoggedIn() => FBAuth.auth.currentUser != null;

Future<String?> uploadClothFile(SelectedImage imageFile) async {
  try {
    final imageRef = FBStorage.fbstore2
        .child(Get.find<HomeCtrl>().residentModel?.docId ?? "")
        .child('clothes')
        .child(
            '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');

    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<String?> uploadProofFile(SelectedImage imageFile) async {
  try {
    final imageRef = FBStorage.fbstore2
        .child(Get.find<HomeCtrl>().residentModel?.docId ?? "")
        .child('proof')
        .child(
            '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');

    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<String?> uploadComplainFile(SelectedImage imageFile) async {
  try {
    final imageRef = FBStorage.fbstore2
        .child(Get.find<HomeCtrl>().residentModel?.docId ?? "")
        .child('complain')
        .child(
            '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');

    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

Future<String?> uploadProfileFile(SelectedImage imageFile) async {
  try {
    final imageRef = FBStorage.fbstore2
        .child(Get.find<HomeCtrl>().residentModel?.docId ?? "")
        .child('profile')
        .child(
            '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}');
    final task = await imageRef.putData(imageFile.uInt8List);
    return await task.ref.getDownloadURL();
  } on Exception catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

showAppSnackBar(String message,
        {SnackBarAction? action,
        Duration duration = const Duration(milliseconds: 1500)}) =>
    snackbarKey.currentState?.showSnackBar(SnackBar(
      content: Text(message),
      action: action,
      duration: duration,
    ));

extension MetaWid on DateTime {
  String goodDate() {
    try {
      return DateFormat.yMMMM().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String convertToDDMMYY() {
    DateFormat formatter = DateFormat('dd-MM-yyyy');
    return formatter.format(this);
  }

  String convertToMMDD() {
    DateFormat formatter = DateFormat('MMM, dd');
    return formatter.format(this);
  }

  String goodDayDate() {
    try {
      return DateFormat.yMMMMd().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodTime() {
    try {
      return DateFormat('hh:mm a').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }
}

bool isDateBetween(DateTime startDate, DateTime endDate, DateTime dateToCheck) {
  return startDate.isBefore(dateToCheck) && endDate.isAfter(dateToCheck);
}

String findDayOfWeek(DateTime date) {
  List<String> days = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday'
  ];
  return days[date.weekday - 1];
}

DateTime findDateFromDay(String desiredDay) {
  DateTime now = DateTime.now();
  int currentWeekday = now.weekday;
  int desiredWeekday = DateTime.parse(desiredDay.substring(0, 3)).weekday;

  int difference = desiredWeekday - currentWeekday;
  if (difference <= 0) {
    difference += 7;
  }

  DateTime desiredDate = now.add(Duration(days: difference));
  return desiredDate;
}

String getMonthName(int monthNumber) {
  final months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec'
  ];

  if (monthNumber < 1 || monthNumber > 12) {
    return 'Invalid month number';
  }

  return months[monthNumber - 1];
}

int getExtendedVersionNumber(String version) {
  List versionCells = version.split('.');
  versionCells = versionCells.map((i) => int.parse(i)).toList();
  return versionCells[0] * 100000 + versionCells[1] * 1000 + versionCells[2];
}

// unixTOTypeTimeSatmp(int unixTimestamp) {
//   // Convert Unix timestamp to DateTime
//   final dateTime = DateTime.fromMillisecondsSinceEpoch(unixTimestamp * 1000);

// // Format the DateTime using your desired format
//   const format = 'd MMMM yyyy HH:mm:ss z';
//   final formattedDate = DateFormat(format).format(dateTime);
// }

import 'package:flutter/material.dart';
import 'package:flutter_cashfree_pg_sdk/api/cferrorresponse/cferrorresponse.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfwebcheckoutpayment.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpaymentgateway/cfpaymentgatewayservice.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfsession/cfsession.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfexceptions.dart';
import 'package:get/instance_manager.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
// import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'firebse.dart';

class PaymentGatewayClass {
  // final razorpay = Razorpay();
  var cfPaymentGatewayService = CFPaymentGatewayService();
  bool? paymentSuccess;
  static const testMode = false;
  // static const testMode = true;
  final keyId =
      testMode ? 'rzp_test_UaHSIIL4XCcRR4' : '***********************';
  final keySecret =
      testMode ? 'XbsCKlmZnLX7YFTFtEZ2uWAH' : 'QmsLQEel8A2CUt0rrWHIdq9P';
  //  const url = 'https://api.razorpay.com/v1/orders';
  // final url = 'http://127.0.0.1:5001/chandresh-nathani/us-central1/createOrder';

  // final razorpay = Razorpay();

  Future<void> createRazorpayOrder({
    required String pickupScheduleDocId,
    required String receiptNo,
    required double amount,
  }) async {
    try {
      final result = await FBFunctions.ff.httpsCallable('addExtraClothes').call(
        {
          // 'amount': amount * 100,
          'amount': amount,
          'receiptNo': receiptNo,
          'customer_id': Get.find<HomeCtrl>().residentModel?.hostelId,
          'customer_phone': Get.find<HomeCtrl>().residentModel?.mobile,
        },
      );
      final response = result.data;

      debugPrint(response.toString());
      debugPrint(response['id'].toString());
      _initCfPayment(response);
    } catch (e) {
      debugPrint(e.toString());
    }
    return;
    try {
      /*   final result = await FBFunctions.ff.httpsCallable('addExtraClothes').call(
        {
          'amount': amount * 100,
          'receiptNo': receiptNo,
        },
      );
      final response = result.data;

      debugPrint(response.toString());
      debugPrint(response['id'].toString());
      // return;
      // Init Payment //
      if (response['id'] != null) {
        // final responseData = json.decode(response.body);
        _initPayment(
          pickupScheduleDocId,
          response['id'],
          double.parse(response['amount'].toString()),
          receiptNo,
        );
        paymentListener();
      } else {
        debugPrint('Error creating order');
      } */
      /*
        final response = await http.post(
        Uri.parse(url),
        headers: {
          // 'Authorization': 'Basic ${base64Encode(utf8.encode(keySecret))}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'amount': amount * 100,
          'receiptNo': receiptNo,
        }),
      );
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        _initPayment(orderDocId, responseData['id'], responseData['amount']);
      } else {
        debugPrint('Error creating order');
      } 
      */
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  _initCfPayment(orderData) async {
    try {
      try {
        var session = CFSessionBuilder()
            .setEnvironment(CFEnvironment.PRODUCTION)
            .setOrderId(orderData['order_id'])
            .setPaymentSessionId(orderData['payment_session_id'])
            .build();
        // return session;
        var cfWebCheckout =
            CFWebCheckoutPaymentBuilder().setSession(session).build();
        cfPaymentGatewayService.setCallback(verifyPayment, onError);
        cfPaymentGatewayService.doPayment(cfWebCheckout);
      } on CFException catch (e) {
        debugPrint(e.message);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void verifyPayment(String orderId) {
    print("Verify Payment");
  }

  void onError(CFErrorResponse errorResponse, String orderId) {
    print(errorResponse.getMessage());
    print("Error while making payment");
  }

  _initPayment(String pickupScheduleDocId, String orderId, double amount,
      String paymentOrderId) async {
    debugPrint(
        'pickup schedule docId: ============== ------------------------=====================22222222    $pickupScheduleDocId');
    try {
      var options = {
        'key': keyId,
        'amount': amount,
        'order_id': orderId,
        'name': "BOSS LAUNDRY",
        // 'description': 'Fine T-Shirt',
        'prefill': {'email': FBAuth.auth.currentUser?.email},
        'notes': {
          'paymentOrderId': paymentOrderId,
        }
      };
      // _paymentListener(orderDocId);
      // razorpay.open(options);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  // initiateRazorPay() {
  //   // To handle different event with previous functions
  //   _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
  //   _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
  //   _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  // }

  // void _handlePaymentSuccess(PaymentSuccessResponse response) {
  //   // Do something when payment succeeds
  //   print("Payment successful");
  // }
/* 
  paymentListener() async {
    razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, (response) {
      // _handlePaymentSuccess(response);
      showAppSnackBar(
          'Thanks for adding your cloths. Keep selected clothes ready for pickup!',
          duration: const Duration(seconds: 5));
      // return true;
    });
    razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, (response) {
      debugPrint(response.toString());
      showAppSnackBar('Payment Failed');
      // return false;
      // _handlePaymentError(response, orderDocId);
    });
    // razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, (response) {
    //   // _handleExternalWallet(response, orderDocId);
    // });
  } */

  // void _handleExternalWallet(ExternalWalletResponse response) {
  //   // Do something when an external wallet was selected
  //   print("External wallet");
  // }
}

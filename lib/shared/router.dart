import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:laundry_resident/controllers/home_controller.dart';
import 'package:laundry_resident/main.dart';
import 'package:laundry_resident/views/Account/account.dart';
import 'package:laundry_resident/views/Activity_section/activity_complain.dart';
import 'package:laundry_resident/views/Activity_section/activity_page.dart';
import 'package:laundry_resident/views/Activity_section/activity_pickupschedule.dart';
import 'package:laundry_resident/views/Auth/auth.dart';
import 'package:laundry_resident/views/Auth/reset_pw.dart';
import 'package:laundry_resident/views/clothes/clothes_listing.dart';
import 'package:laundry_resident/views/Home/notification.dart';
import 'package:laundry_resident/views/Help/help.dart';
import 'package:laundry_resident/views/Home/home_page.dart';
import 'package:laundry_resident/views/Home/add_clothes.dart';
import 'package:laundry_resident/views/Home/clothes_collecting.dart';
import 'package:laundry_resident/views/Profile/profilepage.dart';
import 'package:laundry_resident/views/complain/complain.dart';
import '../models/resident.dart';
import '../views/Profile/pending_request.dart';
import '../views/common/error.dart';
import '../wrapper.dart';
import 'methods.dart';

class Routes {
  static const home = '/home';
  static const clothes = '/clothes';
  static const help = '/help';
  static const complain = '/complain';
  static const account = '/account';
  static const addclothes = '/addclothes';
  static const clothcollect = '/clothcollect';
  static const auth = '/auth';
  // static const login = '/login';
  // static const signup = '/signup';
  static const resetPw = '/resetPw';
  static const profile = '/profile';
  static const review = '/review';
  static const activities = '/activities';
  static const editProfile = '/editprofile';
  static const notification = '/notification';
  static const activityComplain = '/activityComplain';
  static const activityPickupSchedule = '/activityPicked';
  // static const activityDelivered = '/activityDelivered';
  // static const activityTallied = '/activityTallied';
}

const homeRoute = Routes.home;

final GoRouter appRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: homeRoute,
  routes: _routes,
  redirect: redirector,
  errorBuilder: (context, state) => const ErrorPage(),
);

FutureOr<String?> redirector(BuildContext context, GoRouterState state) {
  // print(isLoggedIn());
  if (isLoggedIn()) {
    // if (state.fullPath == Routes.login || state.fullPath == Routes.signup) {
    if (state.fullPath == Routes.auth || state.fullPath == Routes.resetPw) {
      if (Get.isRegistered<HomeCtrl>()) {
        Future.delayed(const Duration(milliseconds: 10))
            .then((value) => Get.find<HomeCtrl>().update());
      }
      return homeRoute;
    } else {
      if (Get.isRegistered<HomeCtrl>()) Get.find<HomeCtrl>().update();
      return null;
    }
  } else {
    return state.fullPath == Routes.auth || state.fullPath == Routes.resetPw
        // return state.fullPath == Routes.login ||
        // state.fullPath == Routes.signup ||
        // state.fullPath == Routes.resetPw
        ? null
        : Routes.auth;
  }
}

List<RouteBase> get _routes {
  return <RouteBase>[
    // GoRoute(
    //   path: Routes.login,
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       const NoTransitionPage(child: Login()),
    // ),
    // GoRoute(
    //   path: Routes.signup,
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       const NoTransitionPage(child: SignUp()),
    // ),
    GoRoute(
      path: Routes.auth,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: AuthPage()),
    ),
    GoRoute(
      path: Routes.resetPw,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: ResetPassword()),
    ),
    GoRoute(
      path: Routes.profile,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: EditProfilePage(
        resident: state.extra as ResidentModel?,
      )),
    ),
    GoRoute(
      path: Routes.activities,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: ActivityPage(
        resident: state.extra as ResidentModel?,
      )),
    ),
    GoRoute(
      path: Routes.review,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: ReviewPendingPage(
        resident: state.extra as ResidentModel?,
      )),
    ),
    GoRoute(
      path: Routes.addclothes,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: AddClothes()),
    ),

    GoRoute(
      path: Routes.complain,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: ComplainPage()),
    ),
    GoRoute(
      path: Routes.notification,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: NotificationPage()),
    ),
    GoRoute(
      path: Routes.clothcollect,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: ClothesCollection()),
    ),
    GoRoute(
      path: '${Routes.activityComplain}/:id',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: ActivityComplain(
        typeDocId: state.pathParameters['id'] as String,
      )),
    ),
    GoRoute(
      path: '${Routes.activityPickupSchedule}/:id',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: ActivityPickupScheduleDetails(
        typeDocId: state.pathParameters['id'] as String,
      )),
    ),
    ShellRoute(
        builder: (context, state, child) {
          if (!Get.isRegistered<HomeCtrl>()) Get.put(HomeCtrl());
          return Wrapper(child: child);
          // return BaseWid(child: child);
        },
        routes: [
          GoRoute(
              path: Routes.home,
              pageBuilder: (BuildContext context, GoRouterState state) {
                // final extraBool = state.extra.;

                return NoTransitionPage(
                    child: HomePage(
                  key: ValueKey(DateTime.now()),
                  showDialog: state.extra as bool?,
                ));
              }),
          GoRoute(
            path: Routes.clothes,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: ClothesListing()),
          ),
          GoRoute(
            path: Routes.help,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Help()),
          ),
          GoRoute(
            path: Routes.account,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Account()),
          ),
        ]),
  ];
}

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';

class SelectedImage {
  final String name;
  final String? extention;
  final Uint8List uInt8List;

  SelectedImage(
      {required this.name, required this.uInt8List, required this.extention});
}

class ImagePickerService {
  Future<List<SelectedImage>> pickImageAndCrop(BuildContext context,
      {required bool useCompressor}) async {
    try {
      final result = await FilePicker.platform.pickFiles(allowMultiple: true);
      if (result != null) {
        final imgs = <SelectedImage>[];
        for (var e in result.files) {
          final finalBytes =
              useCompressor ? await imageCompressor(e.bytes!) : e.bytes!;
          imgs.add(SelectedImage(
              name: e.name, extention: e.extension!, uInt8List: finalBytes));
        }
        return imgs;
      }
      return [];
    } catch (e) {
      debugPrint(e.toString());
      return [];
    }
  }

  Future<CroppedFile?> _cropImage(XFile? pickedImage) async {
    if (pickedImage != null) {
      return await ImageCropper().cropImage(
        sourcePath: pickedImage.path,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 100,
        // aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
        uiSettings: [
          AndroidUiSettings(
            aspectRatioPresets: [CropAspectRatioPreset.original],
            // aspectRatioPresets: [CropAspectRatioPreset.square],
            toolbarTitle: 'Cropper',
            toolbarColor: Colors.deepOrange,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: true,
          ),
          IOSUiSettings(
            title: 'Cropper',
            aspectRatioPresets: [CropAspectRatioPreset.original],
          ),
        ],
      );
    }
    return null;
  }

  Future<SelectedImage?> pickImageNew(BuildContext context,
      {required bool useCompressor}) async {
    try {
      final XFile? image =
          await ImagePicker().pickImage(source: ImageSource.gallery);
      final croppedImage = await _cropImage(image);
      if (croppedImage != null) {
        final nameSplits = image!.name.split(".");
        final finalBytes = useCompressor
            ? await imageCompressor(await croppedImage.readAsBytes())
            : await croppedImage.readAsBytes();
        return SelectedImage(
            name: nameSplits.first,
            extention: nameSplits.length > 1 ? nameSplits.last : "",
            uInt8List: finalBytes);
      }
      return null;
    } catch (e) {
      debugPrint(e.toString());
      // showAppSnack(e.toString());
      Clipboard.setData(ClipboardData(text: e.toString()));
      return null;
    }
  }

  Future<SelectedImage?> pickImageNewCamera(BuildContext context,
      {required bool useCompressor}) async {
    try {
      final XFile? image =
          await ImagePicker().pickImage(source: ImageSource.camera);
      final croppedImage = await _cropImage(image);
      if (croppedImage != null) {
        final nameSplits = image!.name.split(".");
        final finalBytes = useCompressor
            ? await imageCompressor(await croppedImage.readAsBytes())
            : await croppedImage.readAsBytes();
        return SelectedImage(
            name: nameSplits.first,
            extention: nameSplits.length > 1 ? nameSplits.last : "",
            uInt8List: finalBytes);
      }
      return null;
    } catch (e) {
      debugPrint(e.toString());
      // showAppSnack(e.toString());
      Clipboard.setData(ClipboardData(text: e.toString()));
      return null;
    }
  }
}

Future<Uint8List> imageCompressor(Uint8List list) async {
  var result = await FlutterImageCompress.compressWithList(
    list,
    minHeight: 800, // Reduced from 1920
    minWidth: 600, // Reduced from 1080
    quality: 70, // Increased quality but smaller size
    format: CompressFormat.jpeg,
  );
  return result;
}
